<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;
use common\services\MessageSyncService;
use common\models\LiveChatGroup;

/**
 * Console controller for syncing messages from GetStream
 *
 * Usage:
 * php yii message-sync/sync-all - Sync messages for all groups
 * php yii message-sync/sync-group 1 - Sync messages for specific group
 * php yii message-sync/cleanup - Clean up old deleted messages
 */
class MessageSyncController extends Controller
{
    /**
     * Sync messages for all active groups
     * This should be run every 5 minutes via cron job
     */
    public function actionSyncAll()
    {
        $this->stdout("Starting message sync for all groups...\n");
        
        /** @var MessageSyncService $syncService */
        $syncService = Yii::createObject(MessageSyncService::class);
        
        $result = $syncService->syncAllGroupMessages();
        
        $this->stdout("Sync completed:\n");
        $this->stdout("- Groups processed: {$result['groups_processed']}\n");
        $this->stdout("- Total messages synced: {$result['total_synced']}\n");
        $this->stdout('- Errors: ' . count($result['errors']) . "\n");
        
        if (!empty($result['errors'])) {
            $this->stdout("Errors encountered:\n");
            foreach ($result['errors'] as $error) {
                $this->stdout("- {$error}\n");
            }
        }
        
        return 0;
    }

    /**
     * Sync messages for a specific group
     * @param int $groupId
     */
    public function actionSyncGroup($groupId)
    {
        $group = LiveChatGroup::findOne($groupId);
        if (!$group) {
            $this->stdout("Group with ID {$groupId} not found.\n");
            return 1;
        }

        $this->stdout("Syncing messages for group: {$group->group_name}\n");
        
        /** @var MessageSyncService $syncService */
        $syncService = Yii::createObject(MessageSyncService::class);
        
        try {
            $syncedCount = $syncService->syncGroupMessages($group);
            $this->stdout("Successfully synced {$syncedCount} messages.\n");
            return 0;
        } catch (\Exception $e) {
            $this->stdout('Error syncing messages: ' . $e->getMessage() . "\n");
            return 1;
        }
    }

    /**
     * Clean up old deleted messages
     * @param int $daysOld Number of days old (default: 90)
     */
    public function actionCleanup($daysOld = 90)
    {
        $this->stdout("Cleaning up messages older than {$daysOld} days...\n");
        
        /** @var MessageSyncService $syncService */
        $syncService = Yii::createObject(MessageSyncService::class);
        
        $deletedCount = $syncService->cleanupOldMessages($daysOld);
        $this->stdout("Cleaned up {$deletedCount} old messages.\n");
        
        return 0;
    }

    /**
     * Show sync status for all groups
     */
    public function actionStatus()
    {
        $groups = LiveChatGroup::find()
            ->where(['status' => LiveChatGroup::STATUS_ACTIVE])
            ->all();

        $this->stdout("Live Chat Groups Status:\n");
        $this->stdout(str_repeat('-', 80) . "\n");
        $this->stdout(sprintf(
            "%-5s %-30s %-15s %-10s %-15s\n",
            'ID',
            'Group Name',
            'Channel ID',
            'Messages',
            'Last Message'
        ));
        $this->stdout(str_repeat('-', 80) . "\n");

        foreach ($groups as $group) {
            $messageCount = $group->getLiveChatUserMessages()->count();
            $lastMessage = $group->getLiveChatUserMessages()
                ->orderBy(['created_at' => SORT_DESC])
                ->one();
            
            $lastMessageTime = $lastMessage ?
                date('Y-m-d H:i', strtotime($lastMessage->created_at)) : 'Never';

            $this->stdout(sprintf(
                "%-5s %-30s %-15s %-10s %-15s\n",
                $group->id,
                substr($group->group_name, 0, 29),
                $group->channel_id ?: 'Not Set',
                $messageCount,
                $lastMessageTime
            ));
        }
        
        return 0;
    }

    /**
     * Test GetStream connection
     */
    public function actionTestConnection()
    {
        $this->stdout("Testing GetStream connection...\n");
        
        try {
            /** @var \common\services\GetStreamService $getStreamService */
            $getStreamService = Yii::$app->getStreamService;
            
            // Test by creating a token
            $token = $getStreamService->createUserToken('test_user');
            
            if ($token) {
                $this->stdout("✅ GetStream connection successful!\n");
                $this->stdout("Test token created for user 'test_user'\n");
                return 0;
            } else {
                $this->stdout("❌ Failed to create test token\n");
                return 1;
            }
        } catch (\Exception $e) {
            $this->stdout('❌ GetStream connection failed: ' . $e->getMessage() . "\n");
            return 1;
        }
    }
}

<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;
use common\services\ChatNotificationService;
use common\models\LiveChatUserMessage;
use common\models\LiveChatUserStatus;

/**
 * Console controller for processing chat notifications
 *
 * Usage:
 * php yii chat-notification/process-volume - Process volume notifications
 * php yii chat-notification/process-mentions - Process mention notifications
 * php yii chat-notification/test-email - Test email functionality
 */
class ChatNotificationController extends Controller
{
    /**
     * Process volume notifications for users with many unread messages
     * This should be run every hour via cron job
     */
    public function actionProcessVolume()
    {
        $this->stdout("Processing volume notifications...\n");

        /** @var ChatNotificationService $notificationService */
        $notificationService = Yii::createObject(ChatNotificationService::class);

        $sentCount = $notificationService->processVolumeNotifications();

        $this->stdout("Sent {$sentCount} volume notifications.\n");
        return 0;
    }

    /**
     * Process mention notifications for recent messages
     * This should be run every 5-10 minutes via cron job
     */
    public function actionProcessMentions()
    {
        $this->stdout("Processing mention notifications...\n");

        /** @var ChatNotificationService $notificationService */
        $notificationService = Yii::createObject(ChatNotificationService::class);

        // Get recent messages (last 10 minutes) that might contain mentions
        $recentMessages = LiveChatUserMessage::find()
            ->where(['>=', 'created_at', date('Y-m-d H:i:s', strtotime('-10 minutes'))])
            ->andWhere(['is_deleted' => LiveChatUserMessage::DELETED_NO])
            ->andWhere(['like', 'message_text', '@'])
            ->all();

        $totalSent = 0;

        foreach ($recentMessages as $message) {
            $sent = $notificationService->processMentionNotifications($message);
            $totalSent += $sent;

            if ($sent > 0) {
                $this->stdout("Processed mentions in message {$message->id}: {$sent} notifications sent\n");
            }
        }

        $this->stdout("Total mention notifications sent: {$totalSent}\n");
        return 0;
    }

    public function actionMarkInactiveUsersOffline()
    {
        // Mark users offline if no activity in last 5 minutes
        $offlineThreshold = date('Y-m-d H:i:s', strtotime('-5 minutes'));

        $affectedRows = LiveChatUserStatus::updateAll(
            [
                'online_status' => LiveChatUserStatus::STATUS_OFFLINE,
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'and',
                ['<', 'updated_at', $offlineThreshold],
                ['online_status' => LiveChatUserStatus::STATUS_ONLINE]
            ]
        );

        echo "Marked {$affectedRows} inactive users as offline\n";
    }
}

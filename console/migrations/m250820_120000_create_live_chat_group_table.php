<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%live_chat_group}}`.
 */
class m250820_120000_create_live_chat_group_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%live_chat_group}}', [
            'id' => $this->primaryKey(),
            'entity' => $this->string(20)->notNull()->comment('Entity type: college, exam, board, article, news'),
            'entity_id' => $this->integer()->notNull()->comment('ID of the entity'),
            'group_name' => $this->string()->notNull()->comment('Name of the chat group'),
            'group_link' => $this->string()->comment('GetStream API link for chat group'),
            'channel_id' => $this->string()->comment('GetStream channel ID'),
            'channel_type' => $this->string(50)->defaultValue('messaging')->comment('GetStream channel type'),
            'description' => $this->text()->comment('Group description'),
            'is_active_on_news' => $this->tinyInteger()->defaultValue(0)->comment('Show on news pages: 0=No, 1=Yes'),
            'is_active_on_articles' => $this->tinyInteger()->defaultValue(1)->comment('Show on article pages: 0=No, 1=Yes'),
            'status' => $this->tinyInteger()->defaultValue(1)->comment('Group status: 0=Inactive, 1=Active, 2=Deleted'),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $options);

        // creates index for column `entity`
        $this->createIndex(
            '{{%idx-live_chat_group-entity}}',
            '{{%live_chat_group}}',
            'entity'
        );

        // creates index for column `entity_id`
        $this->createIndex(
            '{{%idx-live_chat_group-entity_id}}',
            '{{%live_chat_group}}',
            'entity_id'
        );

        // creates index for column `status`
        $this->createIndex(
            '{{%idx-live_chat_group-status}}',
            '{{%live_chat_group}}',
            'status'
        );

        // creates unique index for entity and entity_id combination
        $this->createIndex(
            '{{%idx-live_chat_group-entity-entity_id}}',
            '{{%live_chat_group}}',
            ['entity', 'entity_id'],
            true
        );

        // creates index for channel_id
        $this->createIndex(
            '{{%idx-live_chat_group-channel_id}}',
            '{{%live_chat_group}}',
            'channel_id'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // drops indexes
        $this->dropIndex('{{%idx-live_chat_group-channel_id}}', '{{%live_chat_group}}');
        $this->dropIndex('{{%idx-live_chat_group-entity-entity_id}}', '{{%live_chat_group}}');
        $this->dropIndex('{{%idx-live_chat_group-status}}', '{{%live_chat_group}}');
        $this->dropIndex('{{%idx-live_chat_group-entity_id}}', '{{%live_chat_group}}');
        $this->dropIndex('{{%idx-live_chat_group-entity}}', '{{%live_chat_group}}');
        
        $this->dropTable('{{%live_chat_group}}');
    }
}

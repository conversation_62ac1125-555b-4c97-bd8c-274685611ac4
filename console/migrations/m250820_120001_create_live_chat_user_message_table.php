<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%live_chat_user_message}}`.
 */
class m250820_120001_create_live_chat_user_message_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%live_chat_user_message}}', [
            'id' => $this->primaryKey(),
            'group_id' => $this->integer()->notNull()->comment('Reference to live_chat_group table'),
            'user_id' => $this->integer()->comment('Reference to user table (null for guest users)'),
            'message_id' => $this->string()->notNull()->comment('GetStream message ID'),
            'message_text' => $this->text()->comment('Message content'),
            'message_type' => $this->string(50)->defaultValue('text')->comment('Message type: text, image, file, etc.'),
            'attachments' => $this->text()->comment('JSON encoded array of file attachments'),
            'user_name' => $this->string()->comment('User display name'),
            'user_email' => $this->string()->comment('User email'),
            'is_admin_message' => $this->tinyInteger()->defaultValue(0)->comment('Is admin message: 0=No, 1=Yes'),
            'is_deleted' => $this->tinyInteger()->defaultValue(0)->comment('Is message deleted: 0=No, 1=Yes'),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $options);

        // creates index for column `group_id`
        $this->createIndex(
            '{{%idx-live_chat_user_message-group_id}}',
            '{{%live_chat_user_message}}',
            'group_id'
        );

        // creates index for column `user_id`
        $this->createIndex(
            '{{%idx-live_chat_user_message-user_id}}',
            '{{%live_chat_user_message}}',
            'user_id'
        );

        // creates index for column `message_id`
        $this->createIndex(
            '{{%idx-live_chat_user_message-message_id}}',
            '{{%live_chat_user_message}}',
            'message_id'
        );

        // creates index for column `is_admin_message`
        $this->createIndex(
            '{{%idx-live_chat_user_message-is_admin_message}}',
            '{{%live_chat_user_message}}',
            'is_admin_message'
        );

        // creates index for column `is_deleted`
        $this->createIndex(
            '{{%idx-live_chat_user_message-is_deleted}}',
            '{{%live_chat_user_message}}',
            'is_deleted'
        );

        // creates index for created_at for sorting
        $this->createIndex(
            '{{%idx-live_chat_user_message-created_at}}',
            '{{%live_chat_user_message}}',
            'created_at'
        );

        // add foreign key for table `{{%live_chat_group}}`
        $this->addForeignKey(
            '{{%fk-live_chat_user_message-group_id}}',
            '{{%live_chat_user_message}}',
            'group_id',
            '{{%live_chat_group}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey(
            '{{%fk-live_chat_user_message-group_id}}',
            '{{%live_chat_user_message}}'
        );

        // drops indexes
        $this->dropIndex('{{%idx-live_chat_user_message-created_at}}', '{{%live_chat_user_message}}');
        $this->dropIndex('{{%idx-live_chat_user_message-is_deleted}}', '{{%live_chat_user_message}}');
        $this->dropIndex('{{%idx-live_chat_user_message-is_admin_message}}', '{{%live_chat_user_message}}');
        $this->dropIndex('{{%idx-live_chat_user_message-message_id}}', '{{%live_chat_user_message}}');
        $this->dropIndex('{{%idx-live_chat_user_message-user_id}}', '{{%live_chat_user_message}}');
        $this->dropIndex('{{%idx-live_chat_user_message-group_id}}', '{{%live_chat_user_message}}');
        
        $this->dropTable('{{%live_chat_user_message}}');
    }
}

<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%live_chat_user_status}}`.
 */
class m250830_185522_create_live_chat_user_status_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%live_chat_user_status}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull()->comment('Reference to user table'),
            'channel_id' => $this->string()->notNull()->comment('Reference to live_chat_group table'),
            'online_status' => $this->tinyInteger()->defaultValue(0)->comment('Online status: 0=Offline, 1=Online'),
            'last_seen_at' => $this->dateTime()->defaultValue(null)->comment('Last seen timestamp'),
            'created_at' => $this->dateTime()->notNull(),
            'updated_at' => $this->dateTime()->notNull(),
        ], $options);

        // creates unique index for columns `user_id` and `channel_id`
        $this->createIndex(
            '{{%idx-live_chat_user_status-user_id-channel_id}}',
            '{{%live_chat_user_status}}',
            ['user_id', 'channel_id'],
            true
        );

        // creates index for column `user_id`
        $this->createIndex(
            '{{%idx-live_chat_user_status-user_id}}',
            '{{%live_chat_user_status}}',
            'user_id'
        );

        // creates index for column `channel_id`
        $this->createIndex(
            '{{%idx-live_chat_user_status-channel_id}}',
            '{{%live_chat_user_status}}',
            'channel_id'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // drops indexes
        $this->dropIndex('{{%idx-live_chat_user_status-user_id-channel_id}}', '{{%live_chat_user_status}}');
        $this->dropIndex('{{%idx-live_chat_user_status-user_id}}', '{{%live_chat_user_status}}');
        $this->dropIndex('{{%idx-live_chat_user_status-channel_id}}', '{{%live_chat_user_status}}');
        $this->dropTable('{{%live_chat_user_status}}');
    }
}

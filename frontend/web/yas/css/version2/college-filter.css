/* College Landing Page General*/
.hideExamFilter {
  display: none;
}

.examMoreFilter {
  cursor: pointer;
}

.clgListHeroSection {
  margin-bottom: 20px;
  margin-top: 10px;
}

.clgListHeroSection h1 {
  font-size: 24px;
  line-height: 38px;
  font-weight: 400;
  padding-bottom: 10px;
}

.lg-pr-0 {
  padding-right: 0;
}

.breadcrumbDiv ul li a {
  font-weight: 500;
}

* {
  letter-spacing: normal;
}

/* Icons */
.redCaret {
  width: 12px;
  height: 10px;
  background-position: 651px -154px;
  transform: rotate(180deg);
  margin-left: 5px;
}

/* College Landing Filter CSS */
.filterSidebarSection {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
  padding-bottom: 0;
}

.filterSidebarSection .row {
  margin: 0;
}

.filterSidebarSection p,
.filterSidebarSection li,
.filterSidebarSection a,
.filterSidebarSection button {
  font-size: 14px;
  line-height: 24px;
}

.filterSidebarSection .clearAll {
  color: var(--color-red);
  cursor: pointer;
}

.filterSidebarSection .foundesults {
  justify-content: space-between;
  padding-bottom: 20px;
}

.filterSidebarSection .filterDiv a {
  flex-basis: calc(100% - 16px);
}

.filterSidebarSection a {
  color: #787878;
  font-size: 14px;
  line-height: 24px;
  text-decoration: none;
}

.filterCategoryName {
  margin: 0 -20px;
  padding: 8px 20px;
  font-weight: 500;
  background: #f5f5f5;
  position: relative;
  cursor: pointer;
}

.filterCategoryName:after {
  content: " ";
  background: url(../../images/master_sprite.webp);
  width: 12px;
  height: 16px;
  position: absolute;
  right: 17px;
  top: 12px;
  background-position: 652px -94px;
  transition: 0.2s ease;
  transform: rotate(-90deg);
}

.filterCategoryName.down_angle:after {
  transform: rotate(90deg);
}

.filterDiv {
  padding: 16px 0;
}

.filterDiv button {
  color: #787878;
  padding: 5px 8px;
  border-radius: 24px;
  border: var(--border-line);
  background: var(--color-white);
  margin-right: 5px;
  margin-bottom: 16px;
  font-weight: 500;
  cursor: initial;
}

.filterDiv .closeIcon {
  cursor: pointer;
}

.filterDiv input[type="checkbox"] {
  margin: 0;
  margin-top: 3px;
  vertical-align: middle;
  flex-basis: 16px;
}

.filterDiv ul {
  padding: 0;
  margin: 0;
  max-height: 120px;
  overflow: auto;
}

.filterDiv ul li {
  list-style-type: none;
  padding-bottom: 8px;
  display: flex;
}

.filterDiv ul::-webkit-scrollbar {
  width: 5px;
}

.filterDiv ul::-webkit-scrollbar-thumb {
  background: #ccc;
}

.filterDiv ul::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.filterDiv label {
  color: #787878;
  padding-left: 10px;
  cursor: pointer;
  vertical-align: middle;
  flex-basis: calc(100% - 16px);
}

.filterSidebarSection .filterDiv a {
  flex-basis: calc(100% - 16px);
}

.filterSearch input[type="text"] {
  border: var(--border-line);
  padding: 7px 16px;
  margin-bottom: 16px;
  line-height: 24px;
  font-size: 14px;
  width: 100%;
  border-radius: 2px;
}

/* Resulted college sort section */
.sortBySection.row {
  margin: 0;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.sortBySection .sortByList select {
  padding: 5px 12px;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  background: var(--color-white);
  border: var(--border-line);
  color: #787878;
  border-radius: 3px;
  min-width: 162px;
  background-position: 92% 14px !important;
  cursor: pointer;
  outline: none;
}

/* College Cards */
.searchedcollegeList .row {
  margin: 0;
}

.collegeInfoCard {
  border-radius: 4px;
  border: var(--border-line);
  flex-basis: 31.8%;
  margin-right: 20px;
  margin-bottom: 20px;
  background: var(--color-white);
  max-width: 31.8%;
}

.collegeInfoCard:nth-of-type(1n) {
  margin-right: 13px;
}

.collegeInfoCard a {
  text-decoration: none;
}

.collegeInfoCard .row {
  margin: 0;
  position: relative;
  z-index: 1;
}

.collegeInfoCard .collegeLogo {
  flex-basis: 56px;
  width: 56px;
  height: 56px;
  margin-right: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: -10px;
  overflow: hidden;
  background: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.collegeInfoCard .collegeName {
  flex-basis: calc(100% - 61px);
}

.clgInfoCardHeader {
  padding: 10px;
  padding-top: 58px;
  padding-bottom: 0;
  position: relative;
  background-size: 100% 100%;
  background-position: initial;
  background-repeat: no-repeat;
  cursor: pointer;
}

.clgInfoCardHeader:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, #000, rgba(0, 0, 0, 0)),
    linear-gradient(to bottom, #c4c4c4, #c4c4c4);
  opacity: 0.5;
}

.clgInfoCardHeader .collegeName {
  color: var(--color-white);
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  height: 40px;
}

.clgInfoCardHeader .collegeName a {
  color: var(--color-white);
}

.clgInfoCardHeader .collegeName a:hover {
  text-decoration: underline;
}

.aboutCollege .collegeIntroText {
  min-height: 48px;
}

.clgInfoCardHeader .rankingLabel {
  background: #ffc318;
  padding: 0 10px;
  padding-left: 0;
  color: var(--color-white);
  font-size: 12px;
  line-height: 20px;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}

.clgInfoCardHeader .rankingLabel:before {
  content: "";
  border-top: 20px solid #ffc318;
  border-left: 16px solid transparent;
  position: absolute;
  left: -16px;
  top: 0;
}

.aboutCollege {
  padding: 10px;
  padding-top: 17px;
  padding-bottom: 4px;
  position: relative;
}

.aboutCollege .favriteIcon {
  width: 35px;
  position: absolute;
  top: -17px;
  right: 10px;
  margin: 0;
  height: 35px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  border-radius: 50%;
  background-position: 209px -286px;
  background-color: #fff;
  z-index: 1;
  cursor: pointer;
}

.aboutCollege p {
  font-size: 14px;
  line-height: 24px;
}

.aboutCollege .collegeRating,
.aboutCollege .collegeLocation {
  position: relative;
  font-size: 12px;
  line-height: 20px;
}

.aboutCollege .collegeRating {
  padding-bottom: 8px;
}

.tooltipIcon.tooltipAngle {
  width: 0;
  height: 0;
  border-top: 12px dashed;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  vertical-align: middle;
}

.tooltipIcon {
  width: 15px;
  height: 15px;
  background-position: 92px -323px;
  cursor: pointer;
}

.aboutCollege .tooltipIcon.tooltipAngle {
  border-top: 8px dashed;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  vertical-align: revert;
}

.tooltipIconText:before {
  content: "";
  width: 15px;
  height: 15px;
  transform: rotate(42.5deg);
  top: -7px;
  left: 5px;
  background: var(--color-white);
  position: absolute;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.16);
  z-index: 1;
  display: block;
}

.tooltipIconText,
.tooltipIconText:before {
  position: absolute;
  background: var(--color-white);
  box-shadow: 0 3px 20px rgba(0, 0, 0, 0.16);
  text-transform: initial;
  display: none;
}

.tooltipIconText:before {
  box-shadow: 0 0 0 rgba(0, 0, 0, 0.16);
}

.tooltipIconText {
  width: 270px;
  padding: 10px;
  font-size: 14px;
  line-height: 20px;
  color: var(--primary-font-color);
  top: 28px;
  left: auto;
  z-index: 3;
  cursor: auto;
  font-weight: 400;
}

.tooltipIcon:hover .tooltipIconText,
.tooltipIcon:hover .tooltipIconText:before {
  display: block;
}

.aboutCollege .row p {
  font-size: 12px;
}

.aboutCollege .keyInfo {
  flex-basis: 43%;
  padding-right: 10px;
}

/*.aboutCollege .keyInfo p {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
  }*/
.aboutCollege .keyInfo:nth-of-type(2n) {
  flex-basis: 57%;
  padding-right: 0;
}

.aboutCollege .keyInfo p:first-child {
  color: #989898;
}

.aboutCollege .collegeName {
  display: none;
}

.collegeLocation .locationIconBlue {
  width: 16px;
  height: 20px;
  vertical-align: middle;
  margin-right: 3px;
  background-position: 342px -317px;
}

.clgInfoCardfooter {
  display: flex;
  border-top: var(--border-line);
}

.clgInfoCardfooter .addToCompare,
.clgInfoCardfooter .primaryBtn {
  flex-basis: 50%;
  cursor: pointer;
  padding: 8px;
}

.clgInfoCardfooter .primaryBtn {
  border-radius: 0;
}

.clgInfoCardfooter .addToCompare {
  font-size: 12px;
  line-height: 20px;
  font-weight: 500;
  color: #787878;
  text-align: center;
}

.clgInfoCardfooter .addToCompare input[type="checkbox"] {
  margin: 0;
  vertical-align: sub;
  margin-right: 6px;
  border-radius: 2px;
}

.textRed {
  color: var(--color-red);
  cursor: pointer;
  flex-basis: 50%;
  padding: 8px;
  background: 0 0;
  font-size: 14px;
  font-weight: var(--font-semibold);
  line-height: 20px;
  border: none;
  transition: 0.2s ease;
  outline: none;
}

.clgInfoCardfooter .textBlue {
  color: var(--anchor-textclr);
  cursor: pointer;
  flex-basis: 50%;
  padding: 8px;
  background: 0 0;
  font-size: 14px;
  font-weight: var(--font-semibold);
  line-height: 20px;
  border: none;
  transition: 0.2s ease;
  outline: none;
}

.collegeCriteria {
  border-top: var(--border-line);
}

.collegeCriteria .row {
  display: block;
  white-space: nowrap;
  overflow: auto;
}

.collegeCriteria .row::-webkit-scrollbar {
  display: none;
}

.collegeCriteria ul {
  text-align: center;
  margin: 0;
  padding: 5px 0;
  justify-content: center;
}

.collegeCriteria ul li {
  font-size: 12px;
  line-height: 20px;
  padding: 0 10px;
  list-style-type: none;
  position: relative;
  display: inline-block;
}

.collegeCriteria ul li:after {
  content: "";
  position: absolute;
  height: 100%;
  width: 1px;
  background: #d8d8d8;
  top: 0;
  right: 0;
}

.collegeCriteria ul li:last-child:after {
  display: none;
}

/* Sponsored Card CSS */
.collegeSponsoredInfoCards {
  flex-basis: 31.8%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 20px;
  margin-right: 13px;
}

.collegeSponsoredInfoCards:nth-of-type(3n) {
  margin-right: 0;
}

.collegeSponsoredInfoCards .sponsorCard:first-child {
  border-bottom: 0;
}

.collegeSponsoredInfoCards .sponsorCard:last-child {
  border-top: 0;
}

.collegeSponsoredInfoCards .sponsorCard .sponsorContainer {
  padding: 12.5px 10px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  background-color: var(--color-white);
  border-bottom: none;
  position: relative;
}

.collegeSponsoredInfoCards .sponsorCard:last-child .sponsorContainer {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.collegeSponsoredInfoCards .sponsorCard .sponsorContainer .sponsorLabel {
  position: absolute;
  top: 0;
  right: 0;
  border-left: 14px solid transparent;
  border-top: 20px solid #ffc318;
  text-align: center;
  width: 85px;
}

.collegeSponsoredInfoCards .sponsorCard .sponsorContainer .sponsorLabel span {
  position: absolute;
  top: -16px;
  right: 8px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
}

.collegeSponsoredInfoCards .sponsorCard .sponsorContainer .row {
  display: flex;
}

.collegeSponsoredInfoCards .sponsorCard .sponsorContainer .row .collegeLogo {
  flex-basis: 56px;
  width: 56px;
  height: 56px;
  margin-right: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 72px;
  max-height: 72px;
}

.collegeSponsoredInfoCards .sponsorCard .sponsorContainer .row .sponsoredBanner {
  flex-basis: calc(100% - 61px);
}

.collegeSponsoredInfoCards .sponsorCard .sponsorContainer .row .sponsoredBanner .collegeName {
  margin-top: 5px;
  color: #282828;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
}

.collegeSponsoredInfoCards .sponsorCard .sponsorContainer .row .sponsoredBanner p {
  font-size: 12px;
  font-weight: 400;
  color: #282828;
}

.collegeSponsoredInfoCards .sponsorCard .sponsorContainer .row .sponsoredBanner .locationIconBlue {
  transform: scale(0.8);
  vertical-align: middle;
  margin: 0;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeRating {
  font-size: 12px;
  margin-bottom: 8px;
  min-width: 150px;
  min-height: 17px;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeRating .full-star {
  background-position: 129px -247px;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeRating .half-star {
  background-position: 99px -247px;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeRating .tooltipIconText {
  top: 110px;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeRating .tooltipIcon.tooltipAngle {
  width: 0;
  height: 0;
  border-top: 8px dashed !important;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  vertical-align: middle;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeLocation {
  flex-basis: 100%;
  font-size: 12px;
  font-weight: 400;
  color: #282828;
  margin-bottom: 5px;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeKeyInfo {
  flex-basis: 100%;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeKeyInfo .keyInfo {
  flex-basis: 43%;
  padding-right: 10px;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeKeyInfo .keyInfo p {
  font-size: 12px;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeKeyInfo .keyInfo p:first-child {
  color: #989898;
  margin-bottom: 5px;
}

.collegeSponsoredInfoCards .sponsorCard .clgInfoCardfooter {
  width: 100%;
  border-radius: 3px;
  border: solid 1px #d8d8d8;
}

.collegeSponsoredInfoCards .sponsorCard:first-child .clgInfoCardfooter {
  border-bottom: 0;
}

.collegeSponsoredInfoCards .sponsorCard .clgInfoCardfooter .brochureRed {
  font-size: 14px;
  font-weight: var(--font-semibold);
  color: #fff;
  flex-basis: 50%;
  background-color: #ff4e53;
  border: none;
  border-right: none;
}

.collegeSponsoredInfoCards .sponsorCard .clgInfoCardfooter .primaryBtn {
  background-color: #fff;
  color: #ff4e53;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeRating .full-star,
.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeRating .half-star,
.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeRating .empty-star {
  vertical-align: middle;
}

.collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeKeyInfo div:nth-child(even) {
  flex-basis: 57%;
  padding: 0;
}

/* Load More Colleges */
.loadMoreColleges {
  margin-right: 13px;
}

.courseTypeList .loadMoreList,
.collegeListpageBody .loadMoreList {
  font-size: 14px;
  line-height: 24px;
  padding: 5px;
  border-radius: 3px;
  border: 1px solid var(--color-red);
  background: var(--color-white);
  max-width: 460px;
  margin: 0 auto;
  margin-bottom: 20px;
  color: var(--color-red);
  text-align: center;
  font-weight: 500;
  cursor: pointer;
}

.pageData {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.full-star,
.half-star,
.empty-star {
  width: 17px;
  height: 17px;
  vertical-align: text-bottom;
}

.full-star {
  background-position: 129px -246px;
}

.half-star {
  background-position: 99px -246px;
}

.empty-star {
  background-position: 74px -246px;
}

.collegeListpageBody .pageInfo {
  max-height: 130px;
}

.pageData p,
.pageData li,
.pageData a {
  font-size: 15px;
  line-height: 26px;
}

.pageData p {
  color: var(--primary-font-color);
  padding-bottom: 15px;
}

.pageData h2 {
  font-size: 18px;
  line-height: 28px;
  padding: 8px 20px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  text-transform: uppercase;
  position: relative;
}

.pageData h3 {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}

.pageData ul li {
  position: relative;
  list-style-type: none;
}

.pageData ul li:before {
  content: "";
  background: url(../../../images/master_sprite.webp);
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

/** CLS changes**/
.topContent {
  min-height: 176px;
}

#sponsor_list_slot1 {
  min-height: 370px;
}

.clgInfoCardfooter {
  min-height: 37px;
}

.getSupport {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 0;
  z-index: 5;
  gap: 18px;
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
  color: #282828;
  align-items: center;
  justify-content: center;
  display: none;
  border: 1px sollid #d8d8d8;
  background-color: white;
}

.college-filter-get-support .primaryBtn {
  padding: 0;
}

.college-filter-get-support button {
  padding: 10px 20px !important;
}

.college-filter-get-support p {
  display: inline-block;
  font-size: 18px;
  line-height: 26px;
}

/* end  */
@media (max-width: 1023px) {
  .all-college-ajax{
    min-height: 91px;
  }

  .filterSidebarSection {
    display: none;
  }

  .sortBySection {
    display: none;
  }

  .foundClgs button {
    display: none;
  }

  /* College Landing Page General*/
  .selectedResultsMobile .filterDiv {
    padding: 0;
  }

  .selectedResultsMobile .filterDiv button {
    padding: 7px 8px;
    margin-bottom: 10px;
  }

  .selectedResultsMobile .filterDiv span {
    font-size: 13px;
    line-height: 20px;
  }

  .clgListHeroSection {
    margin: -1px -10px 10px;
    background: var(--topheader-bg);
    padding: 10px;
  }

  .clgListHeroSection h1 {
    font-size: 18px;
    line-height: 28px;
    padding-bottom: 10px;
    color: #282828;
  }

  .topContent .pageData {
    padding: 20px;
  }

  .topContent .readMoreDiv {
    margin-bottom: 0;
  }

  .brochureBtn {
    display: block;
    text-align: center;
    margin: 0 -10px;
    margin-bottom: 10px;
    border: 10px solid var(--color-white) !important;
    transition: none !important;
    padding: 0;
  }

  .brochureBtn button {
    width: 100%;
  }

  /* .brochureBtn.fixedbrochureBtn {
      position: fixed;
      width: 100%;
      left: 0;
      top: 0px;
      z-index: 2;
      margin: 0;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
      border-radius: 0;
      transition: none;
    } */
  .collegeListpageBody .pageInfo {
    max-height: 280px;
  }

  .selectedResultsMobile .foundesults {
    font-size: 15px;
    line-height: 25px;
    padding-bottom: 10px;
    font-weight: 500;
  }

  /* Mobile Sort and Filter Section */
  .mobileSortandFilter {
    display: block;
    /* position: fixed; */
    width: 100%;
    bottom: 0;
    background: #fff;
    left: 0;
    z-index: 2;
    border-top: var(--border-line);
    border: var(--border-line);
    margin-bottom: 10px;
  }

  .mobileSortandFilter .sortIcon {
    background-position: 534px -194px;
  }

  .mobileSortandFilter .optionDiv {
    display: flex;
  }

  .mobileSortandFilter button {
    width: 50%;
    border-radius: 0;
    font-size: 14px;
    line-height: 24px;
    color: #787878;
    background-color: var(--color-white);
    padding: 9px 6px;
    text-transform: uppercase;
    position: relative;
    font-weight: 500;
    border-radius: 3px;
    border: none;
  }

  .mobileSortandFilter button:first-child {
    border-right: var(--border-line);
  }

  .mobileFilterSection {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 13;
    width: 100%;
    height: 100%;
    background: var(--color-white);
  }

  .mobileFilterSection h2,
  .mobileFilterSection .mobileFilterHeading {
    background: var(--color-white);
    padding: 12px 16px;
    color: var(--primary-font-color);
    line-height: 20px;
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    display: flex;
    justify-content: space-between;
    border-bottom: var(--border-line);
  }

  .mobileFilterSection h2 span,
  .mobileFilterSection .mobileFilterHeading span {
    color: var(--color-red);
  }

  .filterTab {
    display: flex;
    height: 100%;
  }

  .filterTab ul {
    padding: 0;
    margin: 0;
  }

  .filterTab .tabs {
    flex-basis: 140px;
    border-right: var(--border-line);
    height: calc(100% - 89px);
    overflow: hidden auto;
  }

  .filterTab .tabs li {
    padding: 10px 16px;
    font-size: 14px;
    line-height: 24px;
    color: var(--primary-font-color);
    position: relative;
  }

  .filterTab .tabs li.tab-link.current {
    font-weight: 500;
    background: #fafbfc;
  }

  .filterTab .tabs li.tab-link.current:after {
    content: "";
    position: absolute;
    width: 3px;
    height: 15px;
    border-radius: 3px;
    right: -2px;
    top: 50%;
    transform: translate(0, -50%);
    background: #c4c4c4;
  }

  .filterTab .tabs li.appliedFilter:before {
    content: "";
    position: absolute;
    width: 5px;
    height: 5px;
    background: #ff4e53;
    top: 50%;
    transform: translate(0, -50%);
    right: 5px;
    border-radius: 50%;
  }

  .filterContentDiv {
    flex-basis: calc(100% - 140px);
    background: #fafbfc;
    padding: 10px;
  }

  .filterContentDiv a:hover {
    text-decoration: none;
  }

  .filterContentDiv input[type="checkbox"] {
    width: auto;
    display: inline-block;
    margin: 0;
    height: auto;
    width: 16px;
    height: 16px;
    padding: 0;
    vertical-align: middle;
  }

  .filterContentDiv label,
  .filterContentDiv li {
    color: #787878;
    line-height: 32px;
    font-size: 14px;
    padding-left: 6px;
    list-style-type: none;
  }

  .filterContentDiv label {
    line-height: 32px;
  }

  .filterOptionDiv {
    display: flex;
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 3;
  }

  .filterOptionDiv button {
    flex-basis: 50%;
    font-size: 14px;
    line-height: 20px;
    border: 1px solid var(--color-red);
    background-color: #fafbfc;
    padding: 11px;
    font-weight: var(--font-semibold);
    color: var(--color-red);
    text-align: center;
    background: var(--color-white);
  }

  .filterOptionDiv button.applyFilter {
    background: var(--color-red);
    color: var(--color-white);
  }

  .filterSearch input[type="text"] {
    margin-bottom: 6px;
  }

  .filterSidebarSection {
    display: none;
  }

  .mobileSortSection {
    background: rgba(0, 0, 0, 0.639215);
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 13;
    display: none;
  }

  .mobileSortDiv {
    position: fixed;
    height: auto;
    bottom: 0;
    left: 0;
    overflow: auto;
    border-radius: 4px 4px 0 0;
    width: 100%;
    background: var(--color-white);
    z-index: 3;
  }

  .mobileSortDiv h2,
  .mobileSortDiv .mobileSortHeading {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    border-bottom: var(--border-line);
    font-size: 15px;
    line-height: 24px;
    font-weight: 500;
  }

  .mobileSortDiv ul {
    margin: 0;
    padding: 0 20px;
  }

  .mobileSortDiv ul li {
    font-size: 14px;
    line-height: 24px;
    border-bottom: var(--border-line);
    display: flex;
    color: #787878;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }

  .mobileSortDiv input[type="checkbox"] {
    border: none;
    margin: 0;
  }

  .mobileSortDiv input[type="checkbox"]:checked {
    background: var(--color-white);
    border: none;
  }

  .mobileSortDiv input[type="checkbox"].inputChecked:checked:after,
  .mobileSortDiv input[type="checkbox"]:checked:after {
    background: var(--color-white);
    border-color: #20d086;
    width: 7px;
    height: 16px;
  }

  .resultCount {
    padding-bottom: 15px;
  }

  /* Sponsored card */
  .collegeSponsoredInfoCards .sponsorCard .collegeDetails .collegeRating .tooltipIcon:before {
    top: 100px !important;
  }

  .collegeSponsoredInfoCards {
    flex-basis: 100%;
    /* margin-right: 13px !important; */
  }

  .collegeSponsoredInfoCards .clgInfoCardfooter .brochureRed {
    margin: 0;
  }

  /* College Cards */
  .collegeInfoCard {
    flex-basis: 100%;
    max-width: 100%;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .collegeInfoCard .collegeLogo {
    margin-bottom: -28px;
  }

  .collegeListpageBody .sideBarSection .sidebarTextLink .applyText {
    margin-top: 0;
  }

  .aboutCollege {
    padding-top: 34px;
  }

  .aboutCollege .collegeLocation,
  .aboutCollege .collegeRating {
    display: inline-block;
    padding-right: 3px;
  }

  .aboutCollege .collegeName {
    font-size: 14px;
    line-height: 26px;
    padding-bottom: 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
    font-weight: 500;
  }

  .aboutCollege .collegeName a {
    color: var(--primary-font-color);
  }

  .clgInfoCardHeader {
    padding-top: 102px;
  }

  .clgInfoCardHeader .collegeName {
    display: none;
  }

  .collegeCriteria ul li,
  .clgInfoCardfooter .addToCompare {
    font-size: 14px;
  }

  .collegeKeyinfo {
    display: block;
    -moz-column-count: 2;
    column-count: 2;
  }

  .collegeKeyinfo .keyInfo {
    padding: 0;
  }

  .filterSearch ul {
    height: calc(100vh - 145px);
    overflow: auto;
  }

  .pageData h2,
  .reviewsSection h2 {
    padding: 8px 10px;
    font-size: 15px;
    line-height: 28px;
    margin-bottom: 10px;
    display: block;
  }

  .pageData p,
  .reviewsSection p {
    padding-bottom: 10px;
  }

  .pageData ul,
  .reviewsSection ul {
    margin: 10px 0;
    padding-left: 30px;
  }

  /* Cls issue */
  .topContent {
    min-height: unset;
  }

  .collegeSponsoredInfoCards {
    margin-right: 0;
  }

  .collegeInfoCard:nth-of-type(1n) {
    margin-right: 0;
  }

  /* end Cls issue */
  .topContent:has(p) {
    min-height: 326px;
  }

  .mobileOnly.primaryBtn.brochureBtn.filter-college-scholership.leadFilterData {
    position: fixed;
    bottom: 0;
    width: 100%;
    margin: 0;
    margin-left: -10px;
    z-index: 11;
  }
}
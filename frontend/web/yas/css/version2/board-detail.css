/* * {
  letter-spacing: normal;
  color: var(--primary-font-color);
} */

.table-responsive {
  margin-bottom: 10px;
  text-align: center;
}

a strong {
  color: var(--anchor-textclr);
}

table td span {
  color: var(--primary-font-color) !important;
}

.breadcrumbDiv ul li a {
  font-weight: 500;
}

.p-0 {
  padding: 0;
}

.pb-0 {
  padding-bottom: 0;
}

.text-center {
  text-align: center;
}

.pageInfo {
  max-height: 500px;
}

.faq_section.pageInfo {
  max-height: 400px;
}

table td {
  border-bottom: 0.2px solid #d8d8d8;
}

table tr td:last-child {
  border-right: 0.2px solid #eaeaea;
}

.boardsheroSection {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
  background: linear-gradient(90deg, #ffffff 57%, #fffae7 100%);
  padding: 28px;
  margin-bottom: 20px;
  margin-top: 10px;
}

.boardsheroSection h1 {
  font-size: 24px;
  line-height: 38px;
  font-weight: 400;
  padding-bottom: 20px;
}

.boardsheroSection button.primaryBtn {
  padding: 5px 12px;
}

.boardsheroSection .col-md-5 {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.pageData {
  background: var(--color-white);
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  border: var(--border-line);
}

.pageData h2 {
  font-size: 18px;
  line-height: 28px;
  padding: 8px 20px;
  margin: 0;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: #f5f5f5;
  text-transform: uppercase;
  position: relative;
}

.pageData h2 {
  text-transform: none;
}

.pageData h2 a {
  font-size: 14px;
  line-height: 24px;
  color: var(--color-red);
  font-weight: 500;
  text-transform: capitalize;
}

.pageData caption {
  padding-top: 10px;
  text-align: center;
  caption-side: bottom;
  margin-bottom: 0;
  font-size: 14px;
  color: var(--primary-font-color);
}

.pageData img {
  margin: 0 auto;
  display: block;
}

.pageData p,
.pageData li,
.pageData a {
  font-size: 15px;
  line-height: 26px;
}

.pageData p,
.pageData p span {
  color: var(--primary-font-color);
  padding-bottom: 15px;
}

.pageData h3 {
  font-size: 17px;
  line-height: 24px;
  padding-bottom: 10px;
  color: var(--primary-font-color);
}

.pageData h4 {
  padding-bottom: 10px;
  line-height: 24px;
  font-weight: 500;
}

.pageData button {
  background: var(--color-red);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-white);
  padding: 8px;
  font-weight: var(--font-semibold);
  border-radius: 3px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border: none;
  transition: 0.2s ease;
  outline: none;
  margin-bottom: 10px;
}

.pageData ul li {
  position: relative;
  list-style-type: none;
}

.pageData ul li:before {
  content: "";
  background: url(/yas/images/master_sprite.webp);
  width: 12px;
  height: 17px;
  position: absolute;
  left: -19px;
  top: 5px;
  background-position: 651px -71px;
  z-index: 1;
}

.customSlider {
  position: relative;
}

.customSlider .scrollRight {
  right: -20px;
}

.customSlider .scrollLeft {
  top: 50%;
  left: -20px;
}

.customSlider .row {
  margin: 0;
}

.customSlider .customSliderCards {
  display: block;
  white-space: nowrap;
  overflow: auto;
}

.customSlider .customSliderCards::-webkit-scrollbar {
  display: none;
}

.customSlider .sliderCardInfo {
  border-radius: 4px;
  border: var(--border-line);
  margin-right: 14px;
}

.customSlider .sliderCardInfo:last-child {
  margin-right: 0;
}

.customSlider .sliderCardInfo img {
  display: block;
  margin: 0;
  height: 207px;
  width: 100%;
}

.customSlider .sliderCardInfo .clgLogo {
  max-width: 72px;
  height: 72px;
  display: block;
  margin-right: 20px;
}

.customSlider .sliderCardInfo .textDiv {
  padding: 20px;
}

.customSlider .sliderCardInfo .textDiv .collegeLogo {
  width: 56px;
  height: 56px;
  margin: 0;
  border-radius: 4px;
  margin-top: -60px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 5px;
  display: block;
}

.customSlider .sliderCardInfo p {
  font-size: 14px;
  line-height: 24px;
  color: var(--primary-font-color);
  font-weight: var(--font-semibold);
  padding-bottom: 0;
  white-space: initial;
}

.customSlider .sliderCardInfo p span {
  color: #989898;
  font-weight: 400;
  font-size: 13px;
}

.customSlider .sliderCardInfo p:first-child {
  font-size: 16px;
  line-height: 24px;
  padding-bottom: 2px;
}

.customSlider .sliderCardInfo .widgetCardHeading {
  font-size: 14px;
  padding-bottom: 0;
  min-height: 48px;
  margin-bottom: 5px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
}

.customSlider .sliderCardInfo .subText {
  color: #989898;
  font-weight: 400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
  padding: 0;
  position: relative;
}

.customSlider .sliderCardInfo .subText .spriteIcon {
  position: initial;
  vertical-align: middle;
}

.customSlider .sliderCardInfo a {
  text-decoration: none;
}

.customSlider .sliderCardInfo a:hover .widgetCardHeading {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.customSlider .sliderCardInfo a:hover .subText {
  text-decoration: none;
}

.customSlider .sliderCardInfo img {
  cursor: pointer;
}

.four-cardDisplay .sliderCardInfo {
  width: 23.8%;
  display: inline-block;
  white-space: initial;
}

.four-cardDisplay .sliderCardInfo:nth-of-type(4n) {
  margin-right: 0;
}

.four-cardDisplay .sliderCardInfo img {
  display: block;
  height: 207px;
  width: 100%;
}

.displayCard:hover .widgetCardHeading {
  color: var(--anchor-textclr);
  text-decoration: underline;
}

.examDates p {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
}

.examDates p:last-child {
  color: #989898;
  font-weight: 400;
}

a {
  text-decoration: none;
  color: var(--anchor-textclr);
}

.updatedBy p {
  display: contents;
  font-size: 14px;
  line-height: 24px;
}

.updatedBy a {
  color: var(--primary-font-color);
  font-weight: 500;
}

.updatedBy a:hover {
  color: var(--anchor-textclr);
}

.readMoreDiv {
  box-shadow: none;
  border: var(--border-line);
  border-top: none;
}

.readMoreDiv .readMoreInfo {
  text-transform: capitalize;
}

.faq_section {
  box-shadow: none;
  border: var(--border-line);
}

.custom-cardDisplay .sliderCardInfo {
  width: 275px;
  display: inline-block;
  padding: 0;
  white-space: initial;
}

.two-cardDisplay .sliderCardInfo {
  width: 48.4%;
  display: inline-block;
  padding: 20px;
  margin-right: 18px;
}

.two-cardDisplay .sliderCardInfo:last-child {
  margin-right: 0;
}

.getSupport {
  padding: 19px;
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 3px;
  margin-bottom: 20px;
}

.getSupport .row {
  margin: 0;
  align-items: center;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}

.getSupport img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}

.getSupport p {
  font-size: 18px;
  line-height: 26px;
}

.getSupport button {
  width: 161px;
  border-radius: 3px;
  font-size: 14px;
  line-height: 24px;
  padding: 6px;
  text-align: center;
  color: var(--color-white);
  font-weight: var(--font-bold);
  border: none;
}

.getSupport .button__row__container .lead-cta button {
  white-space: nowrap;
  font-size: 12.8px;
  width: 225px;
}


.getSupport button.talkToExpert {
  background: var(--topheader-bg);
}

.getSupport button.applyNow {
  background: var(--color-red);
  margin-left: 15px;
}

.sideBarSection {
  background: var(--color-white);
  border: var(--border-line);
  border-radius: 4px;
  margin-bottom: 20px;
}

.sideBarSection .sidebarHeading {
  background: #d8d8d8;
  padding: 10px 20px;
  font-size: 16px;
  line-height: 24px;
  margin: 20px;
  margin-bottom: 6px;
  font-weight: 500;
}

.sideBarSection .sidebarTextLink {
  flex-basis: calc(100% - 92px);
}

.sideBarSection .sidebarTextLink p {
  font-size: 14px;
  line-height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.sideBarSection .sidebarTextLink .cardText {
  color: var(--primary-font-color);
}

.sideBarSection .sidebarTextLink .subText {
  color: #989898;
}

.sideBarSection .listCard {
  display: block;
  padding: 10px 20px;
  border-bottom: var(--border-line);
}

.sideBarSection .listCard:hover .cardText,
.sideBarSection .listCard:hover .subText {
  color: var(--anchor-textclr);
}

.sideBarSection .listCard:last-child {
  border-bottom: none;
}

.sidebarAds {
  padding-top: 15px;
  z-index: 1;
}

.sidebarAds .appendAdDiv {
  width: 300px;
  height: 250px;
  display: block;
  margin: 0 auto;
  margin-bottom: 20px;
}

.lg-button-red {
  text-align: center !important;
}

.pageData .btn,
.pageData button.btn {
  color: var(--primary-font-color);
  border: var(--border-line);
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 13px;
  margin-right: 6px;
  margin-bottom: 7px;
  display: inline-block;
  cursor: pointer;
  text-decoration: none;
  background: var(--color-white);
}

.pageData .btn:hover,
.pageData button.btn:hover {
  background: #f2f2f2;
}

.pageData iframe {
  display: block;
  margin: 0 auto;
}

.webpSpriteIcon {
  display: inline-block !important;
  background: url(/yas/images/master_sprite.webp);
  text-align: left;
  overflow: hidden;
}

.authorInfoAndTranslateBtn {
  display: flex;
  justify-content: space-between;
}

.translateIcon1 {
  background-position: -635px -876px;
  width: 27px;
  height: 24px;
  vertical-align: middle;
  margin-right: 10px;
  display: inline-block;
}

.translateIcon2 {
  background-position: -676px -876px;
  width: 27px;
  height: 24px;
  vertical-align: middle;
  margin-right: 10px;
  display: inline-block;
}

.authorInfoAndTranslateBtn .updated-info.row {
  margin-top: 0;
}

.authorInfoAndTranslateBtn .translateBtn {
  padding: 3px 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.73;
  color: #3d8ff2;
  border-radius: 3px;
  border: solid 1px #d8d8d8;
  background-color: #fff;
  text-transform: none;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  height: 36px;
}

.subNavDropDown a {
  display: inline-block !important;
}

.subNavDropDown:hover .caret {
  background-position: 651px -154px;
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
}

.subNavDropDown .subNavDropDownMenu {
  display: none;
  margin: 0;
  position: absolute;
  z-index: 2;
  padding: 5px 20px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.24);
  background-color: #fff;
}

.subNavDropDown .subNavDropDownMenu li {
  display: block;
  color: #787878;
  cursor: pointer;
}

.subNavDropDown .subNavDropDownMenu li:not(:last-child) {
  margin-bottom: 5px;
}

.subNavDropDown .subNavDropDownMenu li:hover {
  color: #ff4e53;
}

.subNavDropDown:hover .subNavDropDownMenu {
  display: block;
}

.btn_left,
.btn_right {
  z-index: 1;
}

.mobileSubNavDropDownMenu {
  display: none;
}

.subNavDropDown .subNavDropDownMenu li a {
  padding: 0;
  color: #787878;
}

.subNavDropDown .subNavDropDownMenu li a:hover {
  color: #ff4e53;
}

.pageRedirectionLinks .caret {
  padding: 0;
  border: 0;
  line-height: unset;
}

.subNavActive {
  color: #ff4e53 !important;
}

.customSlider .sliderCardInfo .textDiv .collegeLogo {
  margin: 0 !important;
}

.customSlider .sliderCardInfo a {
  text-decoration: none;
}

.examDates p {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
}

.examDates p:last-child {
  color: #989898;
  font-weight: 400;
}

.commonHeroSection {
  padding: 20px;
}

.commonHeroSection .heroHeader {
  align-items: center;
  margin: 0;
  flex-wrap: nowrap;
}

.commonHeroSection .heroHeader .imgContainer {
  max-width: 72px;
  max-height: 72px;
}

.commonHeroSection .heroHeader .headingContainer {
  margin-left: 10px;
}

.commonHeroSection .heroHeader .headingContainer h1 {
  font-size: 24px;
  font-weight: 400;
  line-height: 1.58;
  color: #282828;
  padding-bottom: 0;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.commonHeroSection .helpfulInfo .helpfuItem span {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.71;
  color: #282828;
}

.commonHeroSection .helpfulInfo .helpfuItem .calenderIcon {
  background-position: -395px -931px;
  width: 15px;
  height: 16px;
  vertical-align: middle;
  margin-right: 5px;
}

.commonHeroSection .ctaColumn {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
}

.commonHeroSection .ctaColumn .ctaRow {
  display: flex;
  gap: 20px;
  justify-content: flex-end;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton {
  /* max-width: 184px; */
  height: 36px;
  border-radius: 4px;
  border: solid 1px #ff4e53;
  background-color: rgba(255, 78, 83, 0.1);
  font-size: 16px;
  font-weight: 700;
  line-height: 1.5;
  color: #ff4e53;
  flex-grow: 1;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton,
.commonHeroSection .ctaColumn .ctaRow .setExamAlert {
  padding: 4px 2px;
  width: 210px !important;
  font-weight: 500;
  font-size: 12.8px !important;
  white-space: nowrap;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton span.spriteIcon.whiteDownloadIcon.redDownloadIcon {
  background-position: -952px -365px !important;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton .applyNowIcon {
  background-position: -543px -330px;
  width: 20px;
  height: 20px;
  vertical-align: middle;
  margin-left: 12px;
}

.commonHeroSection .ctaColumn .ctaRow .brochureButton {
  max-width: 126px;
  height: 36px;
  border-radius: 4px;
  background-color: #ff4e53;
  flex-grow: 1;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.71;
  color: #fff;
}

.commonHeroSection .ctaColumn .ctaRow .brochureButton .brochureButtonIcon {
  background-position: -513px -355px;
  width: 16px;
  height: 16px;
  margin-left: 10px;
  vertical-align: middle;
}

.pageData .latestUpdates ul li span {
  font-weight: 500;
  font-size: 15px;
}

.pageData .latestUpdates ul li a {
  font-size: 15px;
  font-weight: 500;
}

.latestUpdates {
  margin-top: 10px;
  margin-bottom: 10px;
}

.latestUpdates .cardHeading {
  color: #282828;
}

table {
  table-layout: fixed;
}

table thead tr {
  background-color: #0d3d63;
  color: #fff;
}

table thead tr td {
  color: inherit;
}

.table-responsive table thead tr td p {
  color: #fff;
}

.table-responsive table thead p strong {
  color: #fff;
}

table thead td {
  font-size: 15px;
  font-weight: 500;
}

.readMoreDiv,
.showMoreCourseWrap {
  max-height: 36px;
  padding: 5px;
}

.commonHeroSection {
  margin-top: 20px;
}

.pageData .latestUpdates {
  padding-left: 0;
}

.readMoreInfo,
.showMoreCourseCard {
  padding: 0;
}

.readMoreDiv:after,
.showMoreCourseWrap:after {
  top: -45px;
}

.questionPaperComponent h3 {
  background: #f5f5f5;
  padding: 10px 20px;
  font-size: 18px;
  line-height: 24px;
  margin-bottom: 20px;
}

.pageData h2 {
  text-transform: none;
}

.customSlider .sliderCardInfo .textDiv .collegeLogo {
  margin-top: -60px;
}

.topHeader .writeReview,
.topHeader .register {
  border-radius: 3px;
  font-weight: 500;
}

.applyRedIcon {
  width: 20px;
  height: 20px;
  background-position: -165px -594px;
  vertical-align: middle;
  margin-left: 12px;
}

.commonHeroSection .ctaColumn .ctaRow .applyNowButton {
  /* max-width: 165px; */
  font-weight: 500;
  font-size: 14px;
}

.commonHeroSection .ctaColumn .ctaRow .brochureButton {
  max-width: 165px;
  font-weight: 500;
  font-size: 14px;
}

.latestInfoSection h2,
.otherEntranceExams h2 {
  font-weight: 500;
}

.pageFooter {
  margin: 0;
}

.defaultLogoImage {
  border-radius: 4px;
}

.updated-info.row {
  margin-bottom: 20px !important;
}

.pageData .latestUpdates {
  margin-bottom: 0;
}

.commonHeroSection .helpfulInfo .helpfuItem .calenderIcon:last-of-type {
  margin-left: 10px;
}

table thead tr th,
table thead tr td,
table th {
  background-color: #0d3d63;
  color: #fff;
}

table th,
table td {
  text-align: left;
}

table thead strong {
  color: inherit;
}

.pageData h2 {
  margin-top: 20px;
}

.pageData h2:first-of-type {
  margin-top: 0;
}

.commonHeroSection .helpfulInfo {
  margin: 0;
  margin-top: 6px;
  padding: 5px 10px;
  border-radius: 4px;
  border: solid 1px #d8d8d8;
  background-color: rgba(9, 102, 194, 0.1);
  max-width: fit-content;
}

.commonHeroSection .helpfulInfo .helpfuItem:not(:first-of-type) .calenderIcon {
  margin-left: 20px;
}

.updated-info.row {
  margin-bottom: 20px;
}

.updated-info.row {
  margin-top: 0;
}

.updated-info.row {
  margin: 0;
  align-items: center;
  line-height: 20px;
  font-size: 14px;
}

.updated-info.row .authorAndDate .authorName {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.43;
  color: #282828;
  text-transform: none;
  padding: 0 !important;
}

.updated-info.row .authorAndDate .verifiedBlueTickIcon {
  background-position: -673px -557px;
  width: 17px;
  height: 16px;
  vertical-align: top;
  transform: scale(0.85);
  margin-left: 8px;
}

.updated-info.row img {
  width: 40px;
  height: 40px;
}

.updated-info.row img {
  width: 40px;
  height: 40px;
}

.updated-info img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 10px;
  vertical-align: middle;
}

.updated-info.row .authorAndDate p {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.29;
  color: #787878;
  padding-bottom: 0;
  margin-top: 2px;
}

.second-row-date {
  display: flex;
  justify-content: end;
}

.examDates {
  padding-left: 0;
  padding-right: 0;
}

.authorInfoAndTranslateBtn {
  padding: 0;
  gap: 12px;
}

.applyWhiteIconCta {
  width: 20px;
  height: 20px;
  background-position: -191px -594px;
  vertical-align: middle;
  margin-left: 12px;
}

.applyWhiteIconCta {
  position: static !important;
  top: 127px;
  right: 62px;
}

.pageRedirectionLinks {
  padding: 0 10px;
  border: var(--border-line);
  background: var(--color-white);
  margin-bottom: 20px;
  border-radius: 4px;
  position: relative;
}

.pageRedirectionLinks .btn_right,
.pageRedirectionLinks .btn_left {
  position: absolute;
  width: 48px;
  height: 44px;
  background-color: #fff;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  top: 0;
  cursor: pointer;
}

.pageRedirectionLinks .btn_left {
  left: 0;
}

.pageRedirectionLinks .btn_right {
  right: 0;
}

.pageRedirectionLinks .right_angle,
.pageRedirectionLinks .left_angle {
  margin: 12px 0;
}

.pageRedirectionLinks ul {
  margin: 0;
  padding: 0;
  white-space: nowrap;
  overflow: auto;
}

.pageRedirectionLinks ul::-webkit-scrollbar {
  display: none;
}

.pageRedirectionLinks ul li {
  font-size: 14px;
  line-height: 24px;
  color: #787878;
  margin: 0 5px;
  list-style-type: none;
  display: inline-block;
}

.pageRedirectionLinks ul li .activeLink {
  color: var(--color-red);
  border-bottom: 3px solid var(--color-red);
  display: inline-block;
  font-weight: 500;
  padding: 10px 0;
}

.pageRedirectionLinks ul li a {
  color: #787878;
  text-decoration: none;
  display: inline-block;
  padding: 10px 0;
}

.quickLinksBoard {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .12);
  background-color: var(--color-white);
  padding: 20px;
  padding: 0;
  margin-bottom: 20px
}

.quickLinksBoard ul {
  margin: 0;
  padding: 0
}

.quickLinksBoard ul li:last-child a {
  border-bottom: none
}

.quickLinksBoard ul li a {
  display: block;
  font-size: 14px;
  line-height: 24px;
  text-decoration: none;
  color: var(--primary-font-color);
  padding: 0;
  border: none
}

.quickLinksBoard ul li a:hover {
  color: #3d8ff2;
  text-decoration: underline
}

.quickLinksBoard p {
  font-size: 15px;
  font-weight: 500;
  background: no-repeat;
  line-height: 24px;
  padding: 10px 20px;
  border-bottom: 1px solid #d8d8d8;
  color: #000
}

.quickLinksBoard ul li {
  padding: 10px 20px;
  border-bottom: 1px solid #d8d8d8;
  list-style-type: none
}

.fixedRedirectionLinks {
  position: fixed;
  top: 0;
  z-index: 2;
  width: 100%;
  max-width: 1206px;
  margin: 0 auto;
}

/*GMU-471*/

.getSupport {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 10px;
  border-radius: 0px;
  z-index: 3;
  /*display: flex;*/
  gap: 18px;
  font-size: 15px;
  font-weight: 400;
  line-height: 22px;
  color: #282828;
  align-items: center;
  justify-content: center;
  height: 58px;
  display: none;
}

.button__row__container {
  min-width: 300px;
}

/**/
.table-content-ctn {
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  margin-bottom: 40px;
}

.table-content-heading-article {
  background-color: #f5f5f5;
  padding: 10px 16px;
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
  color: #282828;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.downArrowIcon,
.upArrowIcon {
  background-position: -151px -125px;
  width: 18px;
  height: 11px;
}

.table-content-article {
  padding: 0px 0px 10px 0px;
  margin: 0 0 10px 0;
  max-height: 200px;
  overflow-y: auto;
}

.table-content-article li {
  list-style-type: none;
  position: relative;
  padding: 0 30px;
  margin-top: 10px;
}

.rotate {
  transform: rotate(180deg);
  top: 0px !important;
}

a {
  color: #3d8ff2;
  text-decoration: none;
  cursor: pointer;
}

.table-content-ctn ::-webkit-scrollbar {
  width: 8px;
}

.table-content-ctn ::-webkit-scrollbar-thumb {
  background: #d8d8d8;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 5px #d8d8d8;
}

.table-content-article {
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s ease-out, padding 0.4s ease-out;
}

.table-content-article.open {
  max-height: 200px;
  padding: 10px 0;
  overflow: auto;
}

ul.table-content-article.open li:before {
  left: 5px;
}

/* Scroll Top CSS */
.scrollToh2CSS {
  scroll-margin-top: 53px;
}

.board-aside {
  min-height: 100%;
  padding-bottom: 20px;
}

.quickLinks {
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  background-color: var(--color-white);
  padding: 20px;
  padding: 0;
}

.quickLinks h2 {
  font-size: 18px;
  line-height: 28px;
  background: #f5f5f5;
  padding: 8px 20px;
  color: var(--primary-font-color);
  text-transform: uppercase;
}

.quickLinks ul {
  padding: 10px 20px;
  margin: 0;
  max-height: 440px;
  overflow: auto;
}

.quickLinks ul::-webkit-scrollbar {
  width: 5px;
}

.quickLinks ul::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.quickLinks ul::-webkit-scrollbar-thumb {
  background: #ccc;
}

.quickLinks ul li {
  list-style-type: none;
}

.quickLinks ul li:last-child a {
  border-bottom: none;
}

.quickLinks ul li a {
  display: block;
  font-size: 14px;
  line-height: 24px;
  border-bottom: var(--border-line);
  text-decoration: none;
  color: var(--primary-font-color);
  padding: 8px 0;
}

.quickLinks ul li a:hover {
  color: #3d8ff2;
  text-decoration: underline;
}

.quickLinks-cls {
  max-height: 487px;
  z-index: 2;
}

/************************* New Footer Design ***********************/

.boardsMenu {
  margin-top: 16px;
  border-top: 1px solid white;
}

.boardsMenu .heading.accor-box-head {
  color: #fff;
  border: 0;
  padding: 17px 0 17px 0;
  -webkit-tap-highlight-color: transparent;
}

.boardsMenu .heading.accor-box-head h4 {
  margin: 0;
  color: #fff;
  font-weight: bold !important;
}

.boardsMenu .box {
  display: table;
  width: 100%;
  border-radius: 2px;
}

.boardsMenu .accordionLink {
  margin: 0 -5px;
}

.boardsMenu ul,
.boardsMenu ol,
.boardsMenu li {
  padding: 0;
  list-style: none;
}

.boardsMenu .accordionLink li {
  padding: 5px 18px 5px 0px;
  display: inline-block;
  vertical-align: top;
}

.boardsMenu .nesteddiv>ul>li {
  width: 224px;
  position: relative;
}

.boardsMenu .nesteddiv li .toggle {
  overflow: hidden;
  height: 31px;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-line-clamp: 1;
  font-weight: 400;
  width: 190px;
  display: block;
  color: #fff !important;
}

.boardsMenu .popUp {
  position: relative;
  width: 98%;
  margin-top: 10px;
  height: auto;
  display: none;
  padding: 20px;
  margin-bottom: 20px;
  top: 7px;
  border-radius: 5px;
  left: 0;
  border: 0.5px solid #FFFFFF;
}

.boardsMenu .popUp ul {
  margin: 0 -5px;
}

.boardsMenu .parentUl {
  padding: 0px 5px;
  margin: 0px 5px;
}

.boardsMenu .popUp li {
  width: auto !important;
  position: relative;
  padding: 0 5px !important;
  vertical-align: top;
}

.boardsMenu .popUp li span {
  color: #fff !important;
  font-size: 14px;
}

.boardsMenu .nesteddiv li span {
  cursor: pointer;
  font-weight: 600;
}

.boardsMenu .popUp li a {
  line-height: 11px;
  font-size: 14px;
  position: relative;
  color: #F3F2EF !important;
}

.boardsMenu .popUp li:not(:last-child) a:before {
  position: absolute;
  content: '';
  width: 1px;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  top: 0;
  right: -6px;
}

.boardsMenu .activeLink::before {
  content: '';
  width: 20px;
  height: 20px;
  border: solid #fff;
  border-width: 0 1px 1px 0;
  display: inline-block;
  background-color: #273553;
  position: absolute;
  transform: rotate(-135deg);
  -webkit-transform: rotate(-135deg);
  top: 43px;
  z-index: 1;
  left: 24px;
}

/** end of board Menu*/

.authorAndDate {
  display: flex;
  flex-direction: column;
}

.updated-info.row .authorAndDate .verifiedBlueTickIcon {
  margin-left: 0;
  margin-top: 1px;
}

.articelNote {
  border: var(--border-line);
  background: var(--color-white);
  font-size: 15px;
  font-weight: 400;
  font-stretch: normal;
  font-style: italic;
  line-height: 1.73;
  letter-spacing: 0.3px;
  text-align: left;
  color: #282828;
  border-left: solid #ff4e53 6px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%);
  padding: 20px;
}

.articelNote p {
  font-size: 15px;
  line-height: 26px;
}

@media (max-width: 409px) and (max-height: 802px) {

  .whiteDownloadIcon,
  .redDownloadIcon {
    margin-left: 0px !important;
  }

  .getSupport .button__row__container .lead-cta button {
    font-size: 12.1px !important;
    width: 191px !important;
  }
}


@media (max-width: 1023px) {

  .commonHeroSection .ctaColumn .ctaRow .primaryBtn,
  .commonHeroSection .ctaColumn .ctaRow .applyNowButton {
    width: 48%;
    font-size: 12.7px !important;
    padding: 8px 1px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    text-align: center;
    white-space: nowrap;
    line-height: 1.2;
  }


  .fixedRedirectionLinks {
    border: 0;
    border-bottom: var(--border-line);
  }

  iframe {
    max-width: 100%;
    overflow: auto;
  }

  .table-responsive::-webkit-scrollbar {
    width: 5px;
  }

  .table-responsive::-webkit-scrollbar:vertical {
    width: 10px;
  }

  .table-responsive::-webkit-scrollbar:horizontal {
    width: 10px;
  }

  .table-responsive::-webkit-scrollbar-thumb {
    background-color: #d8d8d8;
    border-radius: 5px;
    border: 2px solid #fff;
  }

  .horizontalRectangle,
  .verticleRectangle,
  .squareDiv {
    margin-bottom: 10px;
  }

  .topHeader {
    height: 60px;
  }

  .boardsheroSection {
    margin-top: -157px;
    padding: 20px;
    margin-bottom: 10px;
  }

  .boardsheroSection h1 {
    font-size: 18px;
    line-height: 28px;
    padding-bottom: 10px;
  }

  .examDates .examDatesDiv {
    margin-right: 0;
    width: 100%;
    max-width: 100%;
    padding-bottom: 10px;
  }

  .examDates .examDatesDiv {
    padding-bottom: 10px;
  }

  .examDates .examDatesDiv:last-child {
    padding-bottom: 0;
  }

  .boardsheroSection .examDates .col-md-4 {
    padding-bottom: 10px;
  }

  .boardsheroSection .examDates .col-md-4:last-child {
    padding-bottom: 0;
  }

  .pageData {
    padding: 10px;
    margin-bottom: 10px;
  }

  .pageData h2 {
    font-size: 15px;
    line-height: 24px;
    padding: 8px 10px;
    margin-bottom: 10px;
  }

  .pageData h2 a {
    display: none;
  }

  .pageData p {
    padding-bottom: 10px;
  }

  .pageData ul {
    margin: 10px 0;
    padding-left: 30px;
  }

  .four-cardDisplay .sliderCardInfo,
  .custom-cardDisplay .sliderCardInfo {
    margin-right: 6px;
    width: 224px;
    display: inline-block !important;
  }

  .four-cardDisplay .sliderCardInfo:nth-of-type(4n),
  .custom-cardDisplay .sliderCardInfo:nth-of-type(4n) {
    margin-right: 6px;
  }

  .four-cardDisplay .sliderCardInfo:last-child,
  .custom-cardDisplay .sliderCardInfo:last-child {
    margin-right: 0;
  }

  .four-cardDisplay .sliderCardInfo img,
  .custom-cardDisplay .sliderCardInfo img {
    height: 168px;
  }

  .four-cardDisplay .sliderCardInfo .textDiv,
  .custom-cardDisplay .sliderCardInfo .textDiv {
    padding: 10px;
  }

  .four-cardDisplay .sliderCardInfo .widgetCardHeading,
  .custom-cardDisplay .sliderCardInfo .widgetCardHeading {
    font-weight: 400;
  }

  .four-cardDisplay .sliderCardInfo.mobileOnly,
  .custom-cardDisplay .sliderCardInfo.mobileOnly {
    vertical-align: bottom;
  }

  .four-cardDisplay .sliderCardInfo+.mobileOnly .viewAllDiv {
    min-height: 266px;
  }

  .pageRedirectionLinks {
    border-radius: 0;
    border-right: 0;
    border-left: 0;
    margin: 0 -10px;
    margin-bottom: 10px;
    padding: 0 5px;
  }

  .pageRedirectionLinks ul li {
    padding: 0;
    margin: 0 5px;
  }

  .pageRedirectionLinks ul li .activeLink,
  .pageRedirectionLinks ul li a {
    padding: 11px 0;
  }

  .getSupport {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    margin: 0;
    padding: 10px;
    border-radius: 0;
    z-index: 1;
  }

  .getSupport .row {
    display: none;
  }

  .getSupport button {
    width: 49%;
    border-radius: 2px;
    font-size: 13px;
  }

  .getSupport .button__row__container .lead-cta button {
    width: 200px;
    border-radius: 2px;
    font-size: 12.5px;
    padding: 6px 0px;
    white-space: nowrap;
  }

  .getSupport button.applyNow {
    margin-left: 0;
  }

  .customSlider .scrollLeft,
  .customSlider .scrollRight {
    display: none !important;
  }

  .two-cardDisplay .sliderCardInfo {
    padding: 10px;
    width: 271px;
    margin-right: 6px;
  }

  .two-cardDisplay .sliderCardInfo .row {
    display: block;
  }

  .two-cardDisplay .sliderCardInfo .clgLogo {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .two-cardDisplay .sliderCardInfo p:first-child {
    font-size: 14px;
  }

  .readMoreDiv {
    margin-bottom: 10px;
  }

  .sideBarSection {
    margin-bottom: 10px;
  }

  .sideBarSection .sidebarHeading {
    margin: 10px;
    margin-bottom: 0;
  }

  .sideBarSection .listCard {
    padding: 10px;
  }

  .sideBarSection .sidebarTextLink p {
    font-size: 12px;
    line-height: 16px;
  }

  .sideBarSection .sidebarTextLink .cardText {
    padding-bottom: 5px;
  }

  .mobileSubNavDropDownMenu {
    background: rgba(0, 0, 0, 0.119215);
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
    display: none;
  }

  .mobileSubNavDropDownDiv {
    position: fixed;
    height: auto;
    bottom: 0;
    left: 0;
    overflow: auto;
    border-radius: 4px 4px 0 0;
    width: 100%;
    background: #fff;
    z-index: 3;
  }

  .mobileSubNavDropDownDiv ul {
    margin: 0;
    padding: 0 20px;
  }

  .mobileSubNavDropDownDiv ul li {
    font-size: 14px;
    line-height: 24px;
    border-bottom: 1px solid #d8d8d8;
    display: flex;
    color: #787878;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
  }

  .mobileSubNavDropDownDiv ul li a {
    background: 0 0;
    border: none;
    color: #787878;
  }

  .mobileSubNavDropDownMenu.current {
    display: block;
  }

  .mobileSubNavDropDownDiv ul li a {
    padding: 0;
  }

  .subNavDropDown:hover .caret {
    background-position: -438px -1089px;
    -webkit-transform: unset;
    transform: unset;
  }

  .mobileSubNavDropDown .caret {
    background-position: -382px -1057px;
    width: 25px;
    height: 37px;
    margin-left: 2px;
    margin-right: -10px;
    position: relative;
    bottom: 3px;
    transform: none !important;
  }

  .subNavDropDown:has(.activeLink:hover):hover .caret {
    background-position: -382px -1057px;
  }

  .second-row-date {
    display: block;
  }

  table {
    border: 1px solid #eaeaea !important;
  }

  table td:first-child {
    border-right: 1px solid #eaeaea;
  }

  table tr td:last-child {
    border-right: 1px solid #eaeaea;
  }

  table td {
    border-right: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
  }

  .helpfuItem {
    white-space: nowrap;
  }

  .pageRedirectionLinks .btn_right,
  .pageRedirectionLinks .btn_left {
    display: none;
  }

  .authorInfoAndTranslateBtn {
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
  }

  .cta-mobile {
    width: 100%;
    margin-top: 0;
  }

  .commonHeroSection .heroHeader .headingContainer h1 {
    -webkit-line-clamp: 3;
  }

  .xs-h50 {
    height: 50px !important;
  }

  .pageData .latestUpdates ul li span,
  .pageData .latestUpdates ul li a {
    font-size: 14px;
  }

  .questionPapersDiv .questionPapersLinks {
    flex-basis: calc(100% - 60px);
  }

  .questionPapersDiv p {
    padding-bottom: 0;
  }

  .commonHeroSection {
    padding: 10px;
    margin-top: 0;
    margin: 0 -10px 10px;
    background: #fff;
  }

  .commonHeroSection .heroHeader {
    flex-wrap: nowrap;
  }

  .commonHeroSection .heroHeader .imgContainer {
    max-width: 56px;
    max-height: 56px;
    flex-shrink: 0;
  }

  .commonHeroSection .heroHeader .headingContainer h1 {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.5;
  }

  .commonHeroSection .helpfulInfo .examDateItem {
    flex-grow: 1;
    text-align: center;
  }

  .commonHeroSection .ctaColumn .ctaRow {
    gap: 10px;
  }

  .commonHeroSection .ctaColumn .ctaRow .brochureButton,
  .commonHeroSection .ctaColumn .ctaRow .applyNowButton {
    max-width: unset;
    flex: 1;
  }

  .headerLogo {
    transform: scale(0.8);
  }

  .page-header,
  .topHeader {
    height: 46px;
  }

  .topHeader {
    padding: 2px 20px;
  }

  .pageData .latestUpdates {
    margin: 0;
  }

  .commonHeroSection .ctaColumn .ctaRow .applyNowButton,
  .commonHeroSection .ctaColumn .ctaRow .brochureButton {
    font-weight: 500;
  }

  .breadcrumbDiv {
    background: unset;
  }

  .breadcrumbDiv ul li {
    color: #282828;
  }

  .breadcrumbDiv ul li a {
    color: #282828;
  }

  .breadcrumbDiv ul li a::after {
    background-position: 707px -150px !important;
  }

  .commonHeroSection .ctaColumn .ctaRow .applyNowButton,
  .commonHeroSection .ctaColumn .ctaRow .brochureButton {
    font-size: 14px;
  }

  .pageData p,
  .pageData li,
  .pageData a {
    font-size: 14px;
  }

  .pageRedirectionLinks ul li .activeLink,
  .pageRedirectionLinks ul li a {
    padding: 7px 0;
  }

  .pageRedirectionLinks .btn_right,
  .pageRedirectionLinks .btn_left {
    height: 37px;
  }

  .pageRedirectionLinks .right_angle,
  .pageRedirectionLinks .left_angle {
    margin: 9px 0;
  }

  .questionPaperComponent h3 {
    font-size: 15px;
    font-weight: 600;
    padding: 8px 10px;
    margin-bottom: 10px;
  }

  .examInfoSlider h2.row,
  .clgWithCourse h2.row,
  .liveApllicationForms h2.row {
    text-transform: none;
    background: 0 0;
    padding: 0;
  }

  .latestInfoSection h2,
  .otherEntranceExams h2,
  .headingRevamp h2 {
    background: 0 0;
    padding: 0;
  }

  div.articleSidebarSection,
  div.newsSidebarSection {
    margin-bottom: 10px;
  }

  .latestInfoSection,
  .otherEntranceExams {
    margin-bottom: 10px;
  }

  .breadcrumbDiv {
    padding-top: 0;
    padding-bottom: 0;
  }

  .pageFooter {
    margin-top: 10px;
  }

  .removeFixedQuickLink {
    height: unset;
  }

  .getSupport {
    display: none;
  }

  .updated-info.row img {
    float: left;
  }

  .updated-info.row {
    margin-bottom: 10px;
  }

  .updated-info.row {
    border-radius: 4px;
    background: var(--color-white);
  }

  .updated-info.row {
    margin-top: 0;
  }

  .updated-info.row img {
    float: left;
  }

  .updated-info.row .authorAndDate .verifiedBlueTickIcon {
    transform: scale(0.6);
    margin-left: 0;
    vertical-align: middle;
  }

  .commonHeroSection .helpfulInfo {
    max-width: fit-content;
  }

  .examRelataedLinks ul,
  .pageRedirectionLinks ul {
    overflow-y: hidden;
  }

  iframe {
    width: 300px;
    height: 150px;
  }

  .blueBgDiv {
    display: none !important;
  }

  .course-borad-design {
    display: block !important;
  }

  .course-borad-design-breadcrumb {
    background-color: #0966c2;
  }

  .course-borad-design-breadcrumb ul li a {
    color: #fff;
  }

  .course-borad-design-breadcrumb ul li {
    color: #fff;
  }

  .course-borad-design-breadcrumb ul li a::after {
    background-position: -38px -119px !important;
  }

  .commonHeroSection .helpfulInfo .helpfuItem .calenderIcon:last-of-type {
    margin-left: 0;
  }

  .commonHeroSection .helpfulInfo .helpfuItem .calenderIcon:last-of-type::before {
    content: "\A";
    white-space: pre;
  }

  .pageData .latestUpdates {
    margin-bottom: 0;
  }

  .pageData h2 {
    background: 0 0;
    padding: 0;
  }

  .commonHeroSection .helpfulInfo {
    margin-top: 10px;
  }

  .commonHeroSection .helpfulInfo {
    max-width: 100%;
  }

  .commonHeroSection .helpfulInfo .helpfuItem:not(:first-of-type) .calenderIcon {
    margin-left: 0;
  }

  .getSupport .getSupport__subheading {
    display: none;
  }

  .button__row__container {
    min-width: 300px;
    width: 100%;
  }

  .quickLinksBoard ul li {
    padding: 10px 20px;
    border-bottom: 1px solid #d8d8d8;
    list-style-type: none;
  }

  .discussionForumSection {
    background: var(--color-white);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    border: var(--border-line);
  }

  .discussionForumSection h2 {
    font-size: 18px;
    line-height: 28px;
    padding: 8px 20px;
    margin: 0;
    margin-bottom: 20px;
    font-weight: 500;
    background: #f5f5f5;
    text-transform: uppercase;
    position: relative;
  }

  .authorInfoAndTranslateBtn {
    padding: 0px;
    gap: 12px;
  }

  .second-row-date {
    display: block;
  }

  table {
    border: 1px solid #eaeaea !important;
  }

  table td:first-child {
    border-right: 1px solid #eaeaea;
  }

  table tr td:last-child {
    border-right: 1px solid #eaeaea;
  }

  table td {
    border-right: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
  }

  .helpfuItem {
    white-space: nowrap;
  }

  .pageRedirectionLinks .btn_right,
  .pageRedirectionLinks .btn_left {
    display: none;
  }

  .authorInfoAndTranslateBtn {
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
  }

  .cta-mobile {
    width: 100%;
    margin-top: 0px
  }

  .commonHeroSection .heroHeader .headingContainer h1 {
    -webkit-line-clamp: 3;
  }

  .xs-h50 {
    height: 50px !important;
  }


  .pageData .latestUpdates ul li span,
  .pageData .latestUpdates ul li a {
    font-size: 14px;
  }

  .questionPapersDiv .questionPapersLinks {
    flex-basis: calc(100% - 60px);
  }

  .questionPapersDiv p {
    padding-bottom: 0;
  }

  .commonHeroSection {
    padding: 10px;
    margin-top: 0;
    margin: 0 -10px 10px -10px;
    background: #fff;
  }

  .commonHeroSection .heroHeader {
    flex-wrap: nowrap;
  }

  .commonHeroSection .heroHeader .imgContainer {
    max-width: 56px;
    max-height: 56px;
    flex-shrink: 0;
  }

  .commonHeroSection .heroHeader .headingContainer h1 {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.5;
  }

  /*.commonHeroSection .helpfulInfo {
      margin-top: 10px;
      margin-bottom: 10px;
    }*/
  .commonHeroSection .helpfulInfo .examDateItem {
    flex-grow: 1;
    text-align: center;
  }

  .commonHeroSection .ctaColumn .ctaRow {
    gap: 10px;
  }

  .commonHeroSection .ctaColumn .ctaRow .brochureButton,
  .commonHeroSection .ctaColumn .ctaRow .applyNowButton {
    max-width: unset;
    flex: 1;
  }

  .headerLogo {
    transform: scale(0.8);
  }

  .page-header,
  .topHeader {
    height: 46px;
  }

  .topHeader {
    padding: 2px 20px;
  }

  .pageData .latestUpdates {
    margin: 0;
  }

  .commonHeroSection .ctaColumn .ctaRow .applyNowButton,
  .commonHeroSection .ctaColumn .ctaRow .brochureButton {
    font-weight: 500;
  }

  .breadcrumbDiv {
    background: unset;
  }

  .breadcrumbDiv ul li {
    color: #282828;
  }

  .breadcrumbDiv ul li a {
    color: #282828;
  }

  .breadcrumbDiv ul li a::after {
    background-position: 707px -150px !important;
  }

  /*.commonHeroSection .helpfulInfo .helpfuItem {
      width: 100%;
    }*/
  .commonHeroSection .ctaColumn .ctaRow .applyNowButton,
  .commonHeroSection .ctaColumn .ctaRow .brochureButton {
    font-size: 14px;
  }

  .pageData p,
  .pageData li,
  .pageData a {
    font-size: 14px;
  }

  .pageRedirectionLinks ul li .activeLink,
  .pageRedirectionLinks ul li a {
    padding: 7px 0;
  }

  .pageRedirectionLinks .btn_right,
  .pageRedirectionLinks .btn_left {
    height: 37px;
  }

  .pageRedirectionLinks .right_angle,
  .pageRedirectionLinks .left_angle {
    margin: 9px 0;
  }

  .questionPaperComponent h3 {
    font-size: 15px;
    font-weight: 600;
    padding: 8px 10px;
    margin-bottom: 10px;
  }

  .examInfoSlider h2.row,
  .clgWithCourse h2.row,
  .liveApllicationForms h2.row {
    text-transform: none;
    background: none;
    padding: 0;
  }

  .latestInfoSection h2,
  .otherEntranceExams h2,
  .headingRevamp h2 {
    background: none;
    padding: 0;
  }

  div.articleSidebarSection,
  div.newsSidebarSection {
    margin-bottom: 10px;
  }

  .latestInfoSection,
  .otherEntranceExams {
    margin-bottom: 10px;
  }

  .breadcrumbDiv {
    padding-top: 0;
    padding-bottom: 0;
  }

  .pageFooter {
    margin-top: 10px;
  }

  .removeFixedQuickLink {
    height: unset;
  }

  .getSupport {
    display: none;
  }

  .updated-info.row img {
    float: left;
  }

  .updated-info.row {
    margin-bottom: 10px;
  }

  .updated-info.row {
    border-radius: 4px;
    background: var(--color-white);
  }

  .updated-info.row {
    margin-top: 0;
  }

  .updated-info.row img {
    float: left;
  }

  .updated-info.row .authorAndDate .verifiedBlueTickIcon {
    transform: scale(0.6);
    margin-left: 0px;
    vertical-align: middle;
  }

  .commonHeroSection .helpfulInfo {
    max-width: fit-content;
  }

  .examRelataedLinks ul,
  .pageRedirectionLinks ul {
    overflow-y: hidden;
    /* Iphone overflow issue*/
  }

  iframe {
    width: 300px;
    /*  Youtube Iframe*/
    height: 150px;
  }

  .blueBgDiv {
    display: none !important;
  }

  .course-borad-design {
    display: block !important;
  }

  .course-borad-design-breadcrumb {
    background-color: #0966c2;
  }

  .course-borad-design-breadcrumb ul li a {
    color: #fff;
  }

  .course-borad-design-breadcrumb ul li {
    color: #fff;
  }

  .course-borad-design-breadcrumb ul li a::after {
    background-position: -38px -119px !important;
  }


  /*Calender Icon*/
  .commonHeroSection .helpfulInfo .helpfuItem .calenderIcon:last-of-type {
    margin-left: 0px;
  }

  .commonHeroSection .helpfulInfo .helpfuItem .calenderIcon:last-of-type::before {
    content: "\A";
    white-space: pre;
  }

  .pageData .latestUpdates {
    margin-bottom: 0px;
  }

  .pageData h2 {
    background: none;
    padding: 0px;

  }

  .commonHeroSection .helpfulInfo {
    margin-top: 10px;
    /*margin-bottom: 10px;*/
  }

  .commonHeroSection .helpfulInfo {
    max-width: 100%;
  }

  .commonHeroSection .helpfulInfo .helpfuItem:not(:first-of-type) .calenderIcon {
    margin-left: 0px;
  }


  .applyWhiteIconCta {
    width: 20px;
    height: 20px;
    background-position: -191px -594px;
    vertical-align: middle;
    margin-left: 12px;
  }

  .applyWhiteIconCta {
    position: static;
    top: 127px;
    right: 62px;
  }

  /*# sourceMappingURL=boards.css.map */

  /************************* New Footer Design ***********************/

  .boardsMenu .nesteddiv>ul>li {
    width: 100%;
  }

  .boardsMenu .accordionLink li {
    padding: 5px 5px 5px 0px;
  }

  .boardsMenu .heading.accor-box-head {
    padding: 17px 0 0px 0;
  }

  .boardsMenu .popUp {
    padding: 0px 5px;
    border: 0;
  }

  .boardsMenu .nesteddiv li .toggle {
    padding: 0 5px;
    margin: 0px;
    width: 100%;
  }

  .boardsMenu .heading.accor-box-head h4 {
    margin-bottom: 16px;
  }

  .boardsMenu .popUp li a {
    white-space: nowrap;
  }
}
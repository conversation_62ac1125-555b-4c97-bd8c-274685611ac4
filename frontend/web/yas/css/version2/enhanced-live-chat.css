/* Enhanced Live Chat Widget - Clean Reset */

#enhanced-chat-widget {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin-bottom: 20px 0px;
    overflow: hidden;
    width: 100%;
    /* max-width: 600px; */
    height: auto;
}

/* Chat Header Enhanced */
.chat-header-enhanced {
    background: linear-gradient(135deg, #5f7fff, #374fff);
    color: #fff !important;
    padding: 20px;
    position: relative;
}

.live-indicator {
    position: absolute;
    top: 16px;
    left: 20px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.live-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff4444;
    animation: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
}

.live-text {
    font-size: 12px;
    font-weight: 600;
    color: #fff;
}

.chat-title-section {
    text-align: center;
    margin: 20px 0;
}

.chat-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #fff;
}

.chat-subtitle {
    font-size: 14px;
    opacity: 0.9;
    color: #fff;
}

.chat-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 0 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
}

.stat-icon {
    width: 16px;
    height: 16px;
}

.stat-number {
    font-size: 18px;
    font-weight: 700;
    color: #fff;
}

.active-number {
    font-size: 18px;
    font-weight: 700;
    color: #3ae478;
}

.stat-label {
    font-size: 12px;
    opacity: 0.9;
    color: #fff;
    margin-left: 4px;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Chat Content */
.chat-content {
    background: #fff;
    padding: 20px;
}

/* Lead Form Section */
.chat-lead-form {
    padding: 0;
}

/* Horizontal Layout */
.chat-horizontal-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.recent-messages-section {
    flex: 1;
    min-width: 0;
}

.join-form-section,
.otp-form-section {
    flex: 1;
    min-width: 0;
}

.recent-messages-section h5,
.recent-messages-preview h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
}

.recent-messages-preview {
    margin-bottom: 20px;
}

.message-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
}

.preview-item {
    padding: 12px;
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 8px;
}

.preview-item:last-child {
    margin-bottom: 0;
}

.preview-item strong {
    font-size: 13px;
    color: #333;
    font-weight: 600;
}

.preview-item .time {
    font-size: 11px;
    color: #999;
    float: right;
}

.preview-item p {
    font-size: 12px;
    color: #666;
    margin: 4px 0 0 0 !important;
    clear: both;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.join-form-section {
    text-align: center;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-top: 0;
}

.join-chat-button {
    margin-bottom: 12px;
}

.btn-join-chat {
    background: #3c83f6;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-join-chat:hover {
    background: #2563eb;
}

.students-count {
    font-size: 12px;
    margin-bottom: 16px !important;
    color: #666;
    text-align: center;
}

.students-count strong {
    color: #000;
    font-weight: 600;
}

.lead-form {
    text-align: left;
}

.lead-form .form-control {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 12px;
    display: block;
}

.lead-form .form-control:focus {
    border-color: #3c83f6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(60, 131, 246, 0.2);
}

.join-chat-submit {
    background: #3c83f6;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s;
    width: 100%;
    font-size: 14px;
    margin-top: 8px;
}

.join-chat-submit:hover {
    background: #2563eb;
}

/* Chat Interface */
.chat-interface {
    display: none;
    padding: 16px;
    background: #fff;
}

.chat-messages-area {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background: #f8f9fa;
}

.welcome-message {
    text-align: center;
    padding: 12px;
    background: rgba(66, 133, 244, 0.1);
    border-radius: 8px;
    margin-bottom: 16px;
}

.user-joined {
    color: #4285f4;
    font-weight: 500;
}

.chat-message {
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #4285f4;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-right: 10px;
    flex-shrink: 0;
}

.message-footer {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #888;
    margin-top: 4px;
    justify-content: flex-start !important;
}

.message-footer.current-user-live {
    justify-content: flex-end !important;
}

.message-footer .timestamp {
    display: flex;
    /* keep icon + text inline */
    align-items: center;
    gap: 4px;
}

.highlighted .message-text {
    background: #3c83f6;
    color: #fff;
}

.highlighted .chat-user-link {
    color: #fff;
}

.message-footer .tick-icon svg {
    stroke: #3ae478 !important;
}

.message-footer svg {
    width: 12px;
    height: 12px;
    stroke: #888;
}

.message-footer .timestamp p {
    margin: 0;
    /* remove default p spacing */
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.message-author {
    font-weight: 600;
    font-size: 13px;
    color: #333;
}

.message-role {
    background: #ff9800;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    text-transform: uppercase;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-left: auto;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f3f4f6;
    padding: 5px 10px;
    border-radius: 12px;
}

.attachment {
    display: flex;
    align-items: flex-start;
    /* icon aligns top if filename wraps */
    gap: 6px;
    max-width: 250px;
    /* adjust width as per chat bubble */
}

.highlighted .chat-attachment-link {
    color: #fff;
}

.chat-attachment-link {
    display: inline-flex;
    flex-direction: column;
    /* ensures filename wraps neatly */
    color: #0073e6;
    text-decoration: none;
}

.chat-attachment-link-icon {
    flex-shrink: 0;
    /* keep icon size fixed */
    font-size: 16px;
}

.filename,
.message-text {
    white-space: normal;
    /* ✅ allow wrapping */
    word-break: break-word;
    /* ✅ break very long strings */
    overflow: visible;
    /* ✅ no truncation */
}

.chat-input-area {
    border-top: 1px solid #eee;
    background: white;
}

.chat-tabs {
    display: flex;
    padding: 1px 16px;
    background: #f3f4f64d;
}

.chat-tabs-container .tab {
    padding: 10px 5px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    height: 30px;
    margin-top: 6px;
    margin-bottom: 6px;
}

.chat-tabs-container .tab.active {
    color: var(--primary-font-color);
    background: #fff;
    /* box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); */
    box-shadow: 0 1px 2px 0 #d9e0e8;
    border-radius: 5px;
}

.message-input-section {
    display: flex;
    padding: 12px 16px;
    gap: 8px;
    align-items: center;
}

.message-input:focus {
    border-color: #4285f4;
}

.send-btn {
    background: #4285f4;
    color: #fff;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 15%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s;
    right: -19px;
    position: relative;
}

.send-btn:hover {
    background: #3367d6;
}

/* Chat Guidelines Modal */
.chatGuidelinesModal {
    height: 678px;
    display: grid;
}

.chat-guidelines-modal {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-guidelines-modal .modal-header {
    text-align: center;
    border-bottom: none;
    padding: 24px 24px 0;
    position: relative;
}

.chat-guidelines-modal .close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    opacity: 0.5;
    border: none;
    background: none;
    cursor: pointer;
}

.chat-guidelines-modal .close:hover {
    opacity: 0.8;
}

.guidelines-icon {
    background: #4285f4;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
}

.chat-guidelines-modal h2 {
    color: #3c83f6;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 20px;
    line-height: 28px;
}

.chat-guidelines-modal p.guidelines-subtitle-p {
    color: #64748b;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 35px;
}

.chat-guidelines-modal .modal-body {
    padding: 0 24px 24px;
}

.guideline-item {
    display: flex;
    align-items: flex-start;
    gap: 17px;
    margin-bottom: 25px;
}

.guideline-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
    margin-top: 2px;
}

.guideline-icon.success, .guideline-icon.warning, .guideline-icon.info {
    background: #3ae4781a;
    color: white;
}

.guideline-content .guideline-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #333;
    line-height: 21px;
}

.guideline-content .guideline-description {
    font-size: 12px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

.safe-space-section {
    background: #f3f4f680;
    padding: 16px;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 24px;
}

.safe-space-icon {
    color: #2196f3;
    font-size: 20px;
    margin-top: 2px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: normal;
}

.safe-space-title {
    font-size: .875rem;
    line-height: 1.25rem;
    font-weight: 500;
    margin-right: 8px;
    margin-left: 8px;
}

.safe-space-content {
    font-size: 12px;
    color: #64748b;
    margin: 0;
    line-height: 16px;
}

.chat-guidelines-modal .modal-footer {
    border-top: none;
    padding: 0 24px 24px;
}

#start-chatting-btn {
    background: linear-gradient(135deg, #3F83F8, #1D4ED8);
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    color: white;
    width: 100%;
    font-size: 14px;
}

#start-chatting-btn:hover {
    background: #3c83f6e6;
}

/* CSS Reset for Widget */
#enhanced-chat-widget * {
    box-sizing: border-box;
}

#enhanced-chat-widget h1, #enhanced-chat-widget h2, #enhanced-chat-widget h3,
#enhanced-chat-widget h4, #enhanced-chat-widget h5, #enhanced-chat-widget h6 {
    margin: 0;
    padding: 0;
}

#enhanced-chat-widget p {
    margin: 0;
    padding: 0;
}

/* #enhanced-chat-widget ul, #enhanced-chat-widget li {
    margin: 0;
    padding: 0;
    list-style: none;
} */

#enhanced-chat-widget button {
    border: none;
    cursor: pointer;
}

#enhanced-chat-widget button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#enhanced-chat-widget input, #enhanced-chat-widget select {
    border: 1px solid #ddd;
    outline: none;
    width: 100%;
    height: 40px;
}

#enhanced-chat-widget .errorMsgEmailLive {
    font-size: 11px;
    color: red;
    margin-bottom: 5px;
}

.live-chat-messages {
    height: 500px;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

.welcome-notification {
    background: #3ae4781a;
    padding: 12px;
    text-align: center;
    font-size: 12px;
    color: #3ae478;
    border-bottom: 1px solid #e0e0e0;
    width: 100%;
    height: 45px;
}

.welcome-text-user-names {
    margin-top: 2px !important;
    font-size: 14px;
}

.welcome-notification i {
    margin-right: 5px;
}

.chat-tabs-container {
    background: #fff;
}

.tab .badge {
    background: #ff4444;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
    position: relative;
    right: 0px;
    margin-bottom: 13px;
    width: 20px;
    height: 20px;
    display: inline-flex;
    justify-content: center;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    background: #fff;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-top: 20px;
}

.messages-container::-webkit-scrollbar {
    display: none;
}

.message {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;
}

.moderator-avatar {
    background: #ff6b35;
}

.user-avatar-pk {
    background: #e91e63;
}

.user-avatar-rm {
    background: #4caf50;
}

.user-avatar-sp {
    background: #9c27b0;
}

.user-avatar-as {
    background: #ff9800;
}

.username {
    font-weight: 600;
    font-size: 13px;
    color: #333;
}

.mod-badge {
    background: #e7b008;
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    width: 43px;
    height: 18px;
    text-align: center;
}

/* .timestamp {
    font-size: 11px;
    color: #999;
    margin-left: auto;
} */

.moderator-text {
    background: #3ae478;
    color: white;
    padding: 8px 12px;
    border-radius: 12px;
    display: inline-block;
}

.highlighted {
    justify-content: flex-end;
}

.highlighted .message-content {
    max-width: 70%;
}

.message-status {
    color: #4285f4;
    font-size: 12px;
}

.message-input-container {
    background: #fff;
    border-top: 1px solid #e0e0e0;
    display: flex;
    padding-left: 0px !important;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
}

.input-wrapper {
    display: flex;
    align-items: center;
    /* background: #f5f5f5; */
    border-radius: 20px;
    padding: 4px;
    width: 100%;
}

.input-wrapper .attachment-btn {
    border: none;
    color: #000;
    padding: 5px;
    cursor: pointer;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
}

/* Chat Guidelines Modal */
.modal.fade {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    outline: 0;
}

.modal.fade.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    max-width: 500px;
    height: 678px;
    padding: 24px;
    display: flex;
    flex-direction: column;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.5);
}

#enhanced-chat-widget .select2-container--default .select2-selection--single {
    border: none !important;
}

#enhanced-chat-widget .select2-container {
    display: inline !important;
    top: -3px;
}

#enhanced-chat-widget .form-control.inputStreamLiveContainer.modalInputContainer.streamLiveClass.streamLiveCategory.inputStreamLive,
#enhanced-chat-widget .form-control.inputLevelLiveContainer.modalInputContainer.levelLiveClass.levelLiveCategory.inputLevelLive {
    height: 40px;
    width: 314px;
    background: #fff;
}

#enhanced-chat-widget .select2-container .select2-selection--single .select2-selection__rendered {
    padding-left: 0 !important;
}

.stream-dropdown.select2-dropdown--below,
.level-dropdown.select2-dropdown--below {
    left: -11px !important;
    top: -6px !important;
}

#enhanced-chat-widget .select2-container:focus-visible {
    border: none !important;
    outline: none !important;
}

.chat-stats .stat-item .usersIcon {
    background-position: 1149px 637px;
    width: 20px;
    height: 20px;
    display: inline-block;
}

.chat-stats .stat-item .statIcon {
    background-position: -676px -1137px;
    width: 20px;
    height: 20px;
    display: inline-block;
}

.otp-box {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
}

.otp-box .join-chat-button {
    margin-bottom: 12px;
}

.otp-box .students-count {
    font-size: 12px;
    margin-bottom: 16px !important;
    color: #666;
}

.otp-box .students-count strong {
    color: #000;
    font-weight: 600;
}

.otp-box span.otplabel {
    color: #64748b;
    font-size: 12px;
    display: block;
    margin: 8px 0;
}

.otp-box input[type="text"] {
    width: 100%;
    height: 48px;
    font-size: 16px;
    margin: 12px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    text-align: center;
    box-sizing: border-box;
}

.otp-box .verify-btn {
    width: 100%;
    height: 48px;
    background: #3c83f6;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin: 8px 0;
    transition: background 0.3s;
}

.otp-box .verify-btn:hover {
    background: #2563eb;
}

.otp-box .verify-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.otp-box .change-number {
    display: block;
    color: #3c83f6;
    text-decoration: none;
    font-size: 14px;
    margin-top: 12px;
    cursor: pointer;
}

.otp-box .change-number:hover {
    text-decoration: underline;
}

.otp-toast {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    font-size: 14px;
}

.otp-toast strong {
    display: block;
    margin-bottom: 4px;
}

#enhanced-chat-widget #verify-btn-live-chat:disabled {
    background: #7ca4e1;
}

#chat-content .errorMsgOtpLive {
    color: red;
    font-size: 12px;
}

.guidelinesClose {
    position: absolute;
    right: 10px;
    top: 9px;
    background: none;
    border: none;
}

.emoji-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    color: #000;
    font-size: 16px;
}

.chat-icon .fa-comment-o {

    width: 50px;
    height: 50px;
    background: #fff3;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 22px;
}

.attachment-btn:hover {
    background: #3ae478;
    /* green background */
    color: #fff;
    /* make icon white */
    border-radius: 15%;
    /* optional: make it circular on hover */
    transition: background 0.3s, color 0.3s;
}

.emoji-btn:hover {
    height: 20px;
    top: 50%;
    background: #3ae478;
    color: #fff;
    transition: background 0.3s, color 0.3s;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 13%;
}

/* Input field + emoji wrapper */
.input-with-emoji {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
    right: -10px;
}

.message-input {
    flex: 1;
    padding: 10px 12px;
    border-radius: 8px;
    border: 1px solid #ccc;
    font-size: 14px;
    min-width: 0;
    height: 40px;
}

.moderator-text a, .moderator-text .message-text {
    color: #fff !important;
}

.moderator-text .attachment, .moderator-text .message-text {
    background: none !important;
}

#input-hint-text {
    font-size: 10px;
    color: #767676;
    text-align: justify;
    margin-left: 52px !important;
    position: relative;
    top: -12px;
}

.moderator-text ol {
    margin: 0;
    padding: 0;
    padding-left: 20px;
}

.moderator-text ol li {
    margin: 0;
    padding: 0;
}

/* Mobile-specific flexible height adjustments */
@media (max-width: 768px) {
    #enhanced-chat-widget {
        max-width: 100%;
        margin: 0 16px 20px 16px;
        border-radius: 12px;
    }

    .chat-header-enhanced {
        padding: 16px;
    }

    .chat-title {
        font-size: 18px;
    }

    .chat-stats {
        padding: 0 10px;
        gap: 20px;
    }

    .stat-item {
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }

    .stat-number, .active-number {
        font-size: 16px;
    }

    .stat-label {
        font-size: 10px;
        margin-left: 0;
    }

    .chat-content {
        padding: 16px;
    }

    .chat-horizontal-layout {
        flex-direction: column;
        gap: 16px;
    }

    .join-form-section {
        padding: 16px;
        margin-top: 0;
    }

    .lead-form .form-control {
        height: 48px;
        font-size: 16px;
        margin-bottom: 12px;
    }

    .join-chat-submit {
        height: 48px;
        font-size: 16px;
    }

    .otp-box {
        margin: 16px 0;
        padding: 16px;
    }

    .otp-box input[type="text"] {
        height: 48px;
        font-size: 16px;
    }

    .otp-box .verify-btn {
        height: 48px;
        font-size: 16px;
    }

    .modal-dialog {
        margin: 16px;
        max-width: calc(100% - 32px);
    }

    .preview-item {
        padding: 8px;
        margin-bottom: 6px;
    }

    .message-preview {
        padding: 8px;
        margin-bottom: 12px;
    }
}

/* Additional styles for exact Figma match */
.chat-interface {
    background: #fff;
    border-radius: 0 0 16px 16px;
}

.messages-container {
    background: #fff;
    border-radius: 8px;
    max-height: 400px;
    overflow-y: auto;
}

.message-input-container {
    background: #fff;
    border-top: 1px solid #e5e7eb;
    padding: 16px;
    border-radius: 0 0 16px 16px;
}

.input-wrapper {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 24px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.message-input {
    background: transparent;
    border: none;
    outline: none;
    flex: 1;
    font-size: 14px;
    color: #374151;
}

.message-input::placeholder {
    color: #9ca3af;
}

.send-btn {
    background: #3c83f6;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s;
}

.send-btn:hover {
    background: #2563eb;
}

.send-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

/* Message styling improvements */
.message {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    color: #fff;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.username {
    font-size: 13px;
    font-weight: 600;
    color: #374151;
}

.message-text {
    background: #f3f4f6;
    padding: 8px 12px;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.4;
    color: #374151;
    word-wrap: break-word;
}

.message-footer {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
}

.timestamp {
    font-size: 11px;
    color: #9ca3af;
    display: flex;
    align-items: center;
    gap: 2px;
}

/* Highlighted messages (current user) */
.highlighted {
    flex-direction: row-reverse;
}

.highlighted .message-text {
    background: #3c83f6;
    color: #fff;
}

.highlighted .message-footer {
    justify-content: flex-end;
}

/* Welcome notification */
.welcome-notification {
    background: #ecfdf5;
    border: 1px solid #d1fae5;
    border-radius: 8px;
    padding: 12px;
    text-align: center;
    margin-bottom: 16px;
}

.welcome-text-user-names {
    color: #065f46;
    font-size: 14px;
    margin: 0;
}
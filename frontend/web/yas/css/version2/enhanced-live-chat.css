/* Enhanced Live Chat Widget - Clean Reset */

#enhanced-chat-widget {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 1px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin-bottom: 20px;
    overflow: hidden;
    width: 100%;
    /* max-width: 400px; */
    height: 100%;
}

/* Chat Header Enhanced */
.chat-header-enhanced {
    background: linear-gradient(135deg, #5f7fff, #374fff);
    color: #fff !important;
    padding: 24px;
    display: flex;
    flex-direction: column;
    width: 100%;
    /* height: 185px; */
}

.chat-title-section {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 7px;
}

.chat-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 10px;
    height: 40px;
}

.chat-icon i {
    font-size: 16px;
    display: block;
}

.chat-title {
    flex: 1;
    margin-left: 5px;
    display: flex;
    flex-direction: column;
}

.chat-title h4 {
    margin: 0 0 2px 0;
    font-size: 18px;
    font-weight: 600;
}

.exam-name {
    font-size: 14px;
    opacity: 0.9;
}

.chat-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2px;
    gap: 90px;
    width: 100%;
    height: 40px;
    margin-left: 18px;
    margin-top: 12px;
}

.chat-divider {
    border: none;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    margin: 0px 0;
}

.stat-item {
    text-align: center;
    width: 70px;
    height: 40px;
}

.stat-number-wrapper {
    display: flex;
    /* icon and number in a row */
    align-items: center;
    /* vertically align icon with number */
    gap: 6px;
    /* space between icon and number */
}

.stat-number {
    font-size: 18px;
    font-weight: 700;
    display: block;
}

.stat-label {
    font-size: 10px;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-top: 2px;
}

.chat-status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    justify-content: center;
    margin-top: 6px;
    padding-top: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #3ae478;
    animation: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

.status-text {
    font-size: 14px;
    font-weight: 500;
    margin-left: 8px;
}

/* Chat Content */
.chat-content {
    background: #fff;
}

/* Lead Form Section */
.chat-lead-form {
    padding: 16px;
}

.recent-messages-preview h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    height: 20px;
    margin-left: 8px !important;
}

.recent-messages-preview {
    max-height: 210px;
    height: auto;
}

.message-preview {
    margin-bottom: 20px;
    border-radius: 6px;
    padding: 8px;
    max-height: 172px;
    height: auto;
}

.preview-item {
    padding: 8px;
    background: #f3f4f680;
    border-color: #e2e8f0;
    border-radius: 4px;
    margin-top: 10px;
}

.preview-item:first-child {
    margin-top: 0;
    /* remove margin for the first one */
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-item strong {
    font-size: 13px;
    color: #333;
    font-weight: 600;
}

.preview-item .time {
    font-size: 11px;
    color: #999;
    float: right;
}

.preview-item p {
    font-size: 12px;
    color: #666;
    margin: 4px 0 0 0 !important;
    clear: both;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.join-form-section {
    text-align: center;
    padding: 12px;
    background: #3c83f60d;
    border-color: #3c83f633;
    border-radius: 8px;
    margin: 5px;
    margin-top: 20px;
}

.join-stats-transtion {
    font-size: 12px;
    color: #fff;
    padding: 4px 2px;
    font-weight: 600;
    background: #3c83f6;
    border-color: #0000;
    border-radius: 20px;
    width: 75px;
    height: 22px;
    display: block;
    margin: 0 auto 8px auto;
    text-align: center;
    line-height: 14px;
}

.join-stats-transtion:hover {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s;
    color: #fff;
    background: #e7b008cc;
}

.students-count {
    font-size: 12px;
    margin-bottom: 8px !important;
    color: #666
}

.students-count strong {
    color: #000;
    font-weight: 600;
    font-size: 12px;
}

.lead-form {
    text-align: left;
}

.lead-form .form-control {
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 8px;
    display: block;
}

.lead-form .form-control:focus {
    border-color: #4285f4;
    outline: none;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.join-chat-submit {
    background: #4285f4;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s;
    width: 100%;
    font-size: 14px;
    margin-top: 8px;
}

.join-chat-submit:hover {
    background: #3367d6;
}

/* Chat Interface */
.chat-interface {
    display: none;
    padding: 16px;
    background: #fff;
}

.chat-messages-area {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background: #f8f9fa;
}

.welcome-message {
    text-align: center;
    padding: 12px;
    background: rgba(66, 133, 244, 0.1);
    border-radius: 8px;
    margin-bottom: 16px;
}

.user-joined {
    color: #4285f4;
    font-weight: 500;
}

.chat-message {
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #4285f4;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-right: 10px;
    flex-shrink: 0;
}

.message-footer {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #888;
    margin-top: 4px;
    justify-content: flex-start !important;
}

.message-footer.current-user-live {
    justify-content: flex-end !important;
}

.message-footer .timestamp {
    display: flex;
    /* keep icon + text inline */
    align-items: center;
    gap: 4px;
}

.highlighted .message-text {
    background: #3c83f6;
    color: #fff;
}

.highlighted .chat-user-link {
    color: #fff;
}

.message-footer .tick-icon svg {
    stroke: #3ae478 !important;
}

.message-footer svg {
    width: 12px;
    height: 12px;
    stroke: #888;
}

.message-footer .timestamp p {
    margin: 0;
    /* remove default p spacing */
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.message-author {
    font-weight: 600;
    font-size: 13px;
    color: #333;
}

.message-role {
    background: #ff9800;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    text-transform: uppercase;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-left: auto;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f3f4f6;
    padding: 5px 10px;
    border-radius: 12px;
}

.attachment {
    display: flex;
    align-items: flex-start;
    /* icon aligns top if filename wraps */
    gap: 6px;
    max-width: 250px;
    /* adjust width as per chat bubble */
}

.highlighted .chat-attachment-link {
    color: #fff;
}

.chat-attachment-link {
    display: inline-flex;
    flex-direction: column;
    /* ensures filename wraps neatly */
    color: #0073e6;
    text-decoration: none;
}

.chat-attachment-link-icon {
    flex-shrink: 0;
    /* keep icon size fixed */
    font-size: 16px;
}

.filename,
.message-text {
    white-space: normal;
    /* ✅ allow wrapping */
    word-break: break-word;
    /* ✅ break very long strings */
    overflow: visible;
    /* ✅ no truncation */
}

.chat-input-area {
    border-top: 1px solid #eee;
    background: white;
}

.chat-tabs {
    display: flex;
    padding: 1px 16px;
    background: #f3f4f64d;
}

.chat-tabs-container .tab {
    padding: 10px 5px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    height: 30px;
    margin-top: 6px;
    margin-bottom: 6px;
}

.chat-tabs-container .tab.active {
    color: var(--primary-font-color);
    background: #fff;
    /* box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); */
    box-shadow: 0 1px 2px 0 #d9e0e8;
    border-radius: 5px;
}

.message-input-section {
    display: flex;
    padding: 12px 16px;
    gap: 8px;
    align-items: center;
}

.message-input:focus {
    border-color: #4285f4;
}

.send-btn {
    background: #4285f4;
    color: #fff;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 15%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s;
    right: -19px;
    position: relative;
}

.send-btn:hover {
    background: #3367d6;
}

/* Chat Guidelines Modal */
.chatGuidelinesModal {
    height: 678px;
    display: grid;
}

.chat-guidelines-modal {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-guidelines-modal .modal-header {
    text-align: center;
    border-bottom: none;
    padding: 24px 24px 0;
    position: relative;
}

.chat-guidelines-modal .close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    opacity: 0.5;
    border: none;
    background: none;
    cursor: pointer;
}

.chat-guidelines-modal .close:hover {
    opacity: 0.8;
}

.guidelines-icon {
    background: #4285f4;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
}

.chat-guidelines-modal h2 {
    color: #3c83f6;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 20px;
    line-height: 28px;
}

.chat-guidelines-modal p.guidelines-subtitle-p {
    color: #64748b;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 35px;
}

.chat-guidelines-modal .modal-body {
    padding: 0 24px 24px;
}

.guideline-item {
    display: flex;
    align-items: flex-start;
    gap: 17px;
    margin-bottom: 25px;
}

.guideline-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
    margin-top: 2px;
}

.guideline-icon.success, .guideline-icon.warning, .guideline-icon.info {
    background: #3ae4781a;
    color: white;
}

.guideline-content .guideline-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #333;
    line-height: 21px;
}

.guideline-content .guideline-description {
    font-size: 12px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

.safe-space-section {
    background: #f3f4f680;
    padding: 16px;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 24px;
}

.safe-space-icon {
    color: #2196f3;
    font-size: 20px;
    margin-top: 2px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: normal;
}

.safe-space-title {
    font-size: .875rem;
    line-height: 1.25rem;
    font-weight: 500;
    margin-right: 8px;
    margin-left: 8px;
}

.safe-space-content {
    font-size: 12px;
    color: #64748b;
    margin: 0;
    line-height: 16px;
}

.chat-guidelines-modal .modal-footer {
    border-top: none;
    padding: 0 24px 24px;
}

#start-chatting-btn {
    background: linear-gradient(135deg, #3F83F8, #1D4ED8);
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    color: white;
    width: 100%;
    font-size: 14px;
}

#start-chatting-btn:hover {
    background: #3c83f6e6;
}

/* CSS Reset for Widget */
#enhanced-chat-widget * {
    box-sizing: border-box;
}

#enhanced-chat-widget h1, #enhanced-chat-widget h2, #enhanced-chat-widget h3,
#enhanced-chat-widget h4, #enhanced-chat-widget h5, #enhanced-chat-widget h6 {
    margin: 0;
    padding: 0;
}

#enhanced-chat-widget p {
    margin: 0;
    padding: 0;
}

/* #enhanced-chat-widget ul, #enhanced-chat-widget li {
    margin: 0;
    padding: 0;
    list-style: none;
} */

#enhanced-chat-widget button {
    border: none;
    cursor: pointer;
}

#enhanced-chat-widget button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#enhanced-chat-widget input, #enhanced-chat-widget select {
    border: 1px solid #ddd;
    outline: none;
    width: 100%;
    height: 40px;
}

#enhanced-chat-widget .errorMsgEmailLive {
    font-size: 11px;
    color: red;
    margin-bottom: 5px;
}

.live-chat-messages {
    height: 500px;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

.welcome-notification {
    background: #3ae4781a;
    padding: 12px;
    text-align: center;
    font-size: 12px;
    color: #3ae478;
    border-bottom: 1px solid #e0e0e0;
    width: 100%;
    height: 45px;
}

.welcome-text-user-names {
    margin-top: 2px !important;
    font-size: 14px;
}

.welcome-notification i {
    margin-right: 5px;
}

.chat-tabs-container {
    background: #fff;
}

.tab .badge {
    background: #ff4444;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
    position: relative;
    right: 0px;
    margin-bottom: 13px;
    width: 20px;
    height: 20px;
    display: inline-flex;
    justify-content: center;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    background: #fff;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-top: 20px;
}

.messages-container::-webkit-scrollbar {
    display: none;
}

.message {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;
}

.moderator-avatar {
    background: #ff6b35;
}

.user-avatar-pk {
    background: #e91e63;
}

.user-avatar-rm {
    background: #4caf50;
}

.user-avatar-sp {
    background: #9c27b0;
}

.user-avatar-as {
    background: #ff9800;
}

.username {
    font-weight: 600;
    font-size: 13px;
    color: #333;
}

.mod-badge {
    background: #e7b008;
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    width: 43px;
    height: 18px;
    text-align: center;
}

/* .timestamp {
    font-size: 11px;
    color: #999;
    margin-left: auto;
} */

.moderator-text {
    background: #3ae478;
    color: white;
    padding: 8px 12px;
    border-radius: 12px;
    display: inline-block;
}

.highlighted {
    justify-content: flex-end;
}

.highlighted .message-content {
    max-width: 70%;
}

.message-status {
    color: #4285f4;
    font-size: 12px;
}

.message-input-container {
    background: #fff;
    border-top: 1px solid #e0e0e0;
    display: flex;
    padding-left: 0px !important;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
}

.input-wrapper {
    display: flex;
    align-items: center;
    /* background: #f5f5f5; */
    border-radius: 20px;
    padding: 4px;
    width: 100%;
}

.input-wrapper .attachment-btn {
    border: none;
    color: #000;
    padding: 5px;
    cursor: pointer;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
}

/* Chat Guidelines Modal */
.modal.fade {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    outline: 0;
}

.modal.fade.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    max-width: 500px;
    height: 678px;
    padding: 24px;
    display: flex;
    flex-direction: column;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.5);
}

#enhanced-chat-widget .select2-container--default .select2-selection--single {
    border: none !important;
}

#enhanced-chat-widget .select2-container {
    display: inline !important;
    top: -3px;
}

#enhanced-chat-widget .form-control.inputStreamLiveContainer.modalInputContainer.streamLiveClass.streamLiveCategory.inputStreamLive,
#enhanced-chat-widget .form-control.inputLevelLiveContainer.modalInputContainer.levelLiveClass.levelLiveCategory.inputLevelLive {
    height: 40px;
    width: 314px;
    background: #fff;
}

#enhanced-chat-widget .select2-container .select2-selection--single .select2-selection__rendered {
    padding-left: 0 !important;
}

.stream-dropdown.select2-dropdown--below,
.level-dropdown.select2-dropdown--below {
    left: -11px !important;
    top: -6px !important;
}

#enhanced-chat-widget .select2-container:focus-visible {
    border: none !important;
    outline: none !important;
}

.chat-stats .stat-item .usersIcon {
    background-position: 1149px 637px;
    width: 20px;
    height: 20px;
    display: inline-block;
}

.chat-stats .stat-item .statIcon {
    background-position: -676px -1137px;
    width: 20px;
    height: 20px;
    display: inline-block;
}

.otp-box {
    width: 360px;
    height: 250px;
    margin: 0 auto 15px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 8px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    background: #f6fcfe;
}

.otp-box h3 {
    background: #007bff;
    color: white;
    border-radius: 6px;
    font-size: 14px;
    padding: 6px 10px !important;
    display: inline-block;
    margin: 0 0 10px 0 !important;
}

.otp-box p {
    margin: 8px 0;
    font-size: 14px;
}

.otp-box span.otplabel {
    color: #64748b;
    font-size: 12px;
}

.otp-box span.otplabel p {
    color: #64748b;
    font-size: 12px;
}

.otp-box input[type="text"] {
    width: 334px;
    height: 36px;
    font-size: 14px;
    margin-top: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    text-align: center;
}

.otp-box .verify-btn,
.otp-box .change-number {
    width: 334px;
    height: 40px;
    display: block;
    margin: 12px auto 0;
    font-size: 15px;
    border-radius: 6px;
    text-align: center;
    line-height: 40px;
    transition: background 0.3s, color 0.3s;
}

.otp-box .verify-btn {
    background: #4d94ff;
    color: #fff;
    border: none;
    cursor: pointer;
}

.otp-box .verify-btn:hover {
    background: #1a75ff;
}

.otp-box .change-number {
    background: transparent;
    color: #007bff;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    font-size: 13px;
}

.otp-box .change-number:hover {
    background: #28a745;
    color: #fff;
}

.otp-toast {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    font-size: 14px;
}

.otp-toast strong {
    display: block;
    margin-bottom: 4px;
}

#enhanced-chat-widget #verify-btn-live-chat:disabled {
    background: #7ca4e1;
}

#chat-content .errorMsgOtpLive {
    color: red;
    font-size: 12px;
}

.guidelinesClose {
    position: absolute;
    right: 10px;
    top: 9px;
    background: none;
    border: none;
}

.emoji-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    color: #000;
    font-size: 16px;
}

.chat-icon .fa-comment-o {

    width: 50px;
    height: 50px;
    background: #fff3;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 22px;
}

.attachment-btn:hover {
    background: #3ae478;
    /* green background */
    color: #fff;
    /* make icon white */
    border-radius: 15%;
    /* optional: make it circular on hover */
    transition: background 0.3s, color 0.3s;
}

.emoji-btn:hover {
    height: 20px;
    top: 50%;
    background: #3ae478;
    color: #fff;
    transition: background 0.3s, color 0.3s;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 13%;
}

/* Input field + emoji wrapper */
.input-with-emoji {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
    right: -10px;
}

.message-input {
    flex: 1;
    padding: 10px 12px;
    border-radius: 8px;
    border: 1px solid #ccc;
    font-size: 14px;
    min-width: 0;
    height: 40px;
}

.moderator-text a, .moderator-text .message-text {
    color: #fff !important;
}

.moderator-text .attachment, .moderator-text .message-text {
    background: none !important;
}

#input-hint-text {
    font-size: 10px;
    color: #767676;
    text-align: justify;
    margin-left: 52px !important;
    position: relative;
    top: -12px;
}

.moderator-text ol {
    margin: 0;
    padding: 0;
    padding-left: 20px;
}

.moderator-text ol li {
    margin: 0;
    padding: 0;
}

/* Mobile-specific flexible height adjustments */
@media (max-width: 768px) {
    #enhanced-chat-widget {
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
        display: flex;
        flex-direction: column;
        max-height: 90vh;
        overflow: hidden;
    }

    .chat-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
    }

    .chat-lead-form {
        padding: 16px;
        display: flex;
        flex-direction: column;
        height: auto;
    }

    .recent-messages-preview {
        max-height: none !important;
        height: auto !important;
        flex-shrink: 0;
    }

    .message-preview {
        margin-bottom: 15px;
        max-height: none !important;
        height: auto !important;
        padding: 8px;
    }

    .recent-messages-preview:has(.message-preview:empty),
    .recent-messages-preview:not(:has(.preview-item)) {
        display: none;
    }

    .join-form-section {
        flex-shrink: 0;
        padding: 12px;
        margin: 10px 5px 5px 5px;
    }

    .lead-form {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .lead-form .form-control {
        margin-bottom: 0;
        height: 40px;
        flex-shrink: 0;
    }

    .otp-box {
        width: auto;
        max-width: 95%;
        height: auto !important;
        padding: 15px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin: 0 10px 10px !important;
    }

    .otp-box input[type="text"],
    #verify-btn-live-chat,
    #change-number-btn {
        width: 100%;
        margin: 0 auto;
    }

    .students-count {
        margin-bottom: 8px !important;
    }

    .join-stats-transtion {
        margin-bottom: 6px;
    }

    #enhanced-chat-widget {
        margin-bottom: 10px;
    }

    .modal-dialog {
        padding: 11px;
    }
}
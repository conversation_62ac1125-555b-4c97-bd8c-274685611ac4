// ============================================
// CONSTANTS AND CONFIGURATION
// ============================================
const CONFIG = {
    STREAMCHAT_CDN: 'https://cdn.skypack.dev/stream-chat@8.40.0',
    MEDIA_BASE_URL: 'https://media.getmyuni.com/assets/live_chat/files/',
    UPDATE_INTERVAL: 1000,
    OTP_LENGTH: 4,
    MAX_FILENAME_DISPLAY: 5,
    MAX_FILE_SIZE: 10 * 1024, // 10KB in bytes
    ALLOWED_FILE_TYPES: [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'application/zip',
        'application/x-zip-compressed',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
};

const SELECTORS = {
    WIDGET: '#enhanced-chat-widget',
    CHAT_CONFIG: '#live-chat-config',
    LEAD_NAME: '#lead-name',
    LEAD_MOBILE: '#lead-mobile',
    LEAD_EMAIL: '#lead-email',
    STREAM_SELECT: '#interested_stream_live_lead',
    LEVEL_SELECT: '#interested_level_live_lead',
    JOIN_BUTTON: '#join-chat-form-btn',
    MESSAGE_INPUT: '#message-input-enhanced',
    SEND_BUTTON: '#send-btn-enhanced',
    OTP_INPUT: '#otp-input-live-chat',
    VERIFY_BUTTON: '#verify-btn-live-chat',
    FILE_INPUT: '#file-input-live-chat',
    MESSAGES_CONTAINER: '#messages-container',
    CHAT_MESSAGES_AREA: '#chat-messages-area',
    ONLINE_COUNT: '#online-users-count',
    JOINED_USER: '#welcome-text-user-names',
    DISCUSSION_COUNT: '#online-users-discussion-count',
    DISCUSSION_COUNT_OTP: '#online-users-discussion-count-otp',
    OTP_BOX: '#otp-box-live-chat',
};

// ============================================
// UTILITY FUNCTIONS
// ============================================
const Utils = {
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    formatTime(date) {
        return date.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    },

    getInitials(name) {
        if (!name) return 'GU';
        return name.split(' ')
            .map(word => word.charAt(0).toUpperCase())
            .join('')
            .substring(0, 2);
    },

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    throttle(func, limit) {
        let inThrottle;
        return function () {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// ============================================
// STREAMCHAT INITIALIZATION
// ============================================
async function initializeStreamChat() {
    try {
        const { StreamChat } = await import(CONFIG.STREAMCHAT_CDN);
        window.StreamChat = StreamChat;
        console.log('✅ StreamChat loaded via ES modules:', StreamChat);

        const configEl = document.getElementById('live-chat-config');
        window.liveChatChannelId = configEl?.dataset?.channelId || '';
        updateOnlineUserCount();
        getAllMessageCount();
    } catch (error) {
        console.error('❌ Failed to load StreamChat:', error);
        throw error;
    }
}

// ============================================
// FORM VALIDATION HANDLERS
// ============================================
const FormValidators = {
    initializeValidation() {
        this.setupNameValidation();
        this.setupMobileValidation();
        this.setupEmailValidation();
        this.setupFormStateValidation();
        this.setupOtpValidation();
    },

    setupNameValidation() {
        const leadName = document.querySelector(SELECTORS.LEAD_NAME);
        if (leadName) {
            leadName.addEventListener("input", (e) => {
                e.target.value = e.target.value.replace(/[^A-Za-z ]/g, '');
            });
        }
    },

    setupMobileValidation() {
        const leadMobile = document.querySelector(SELECTORS.LEAD_MOBILE);
        if (leadMobile) {
            leadMobile.addEventListener("input", (e) => {
                e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '');
            });
        }
    },

    setupEmailValidation() {
        const leadEmail = document.querySelector(SELECTORS.LEAD_EMAIL);
        if (!leadEmail) return;

        leadEmail.addEventListener("input", (e) => {
            let val = e.target.value;
            val = val.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@_\-\.]|[^\w\d_\-@\.]$/g, '');

            const atCount = (val.match(/@/g) || []).length;
            const dotCount = (val.match(/\./g) || []).length;

            if (atCount && dotCount > 2) val = val.slice(0, -1);
            if (!atCount && dotCount > 1) val = val.slice(0, -1);

            e.target.value = val;
            this.validateEmail(val, leadEmail);
        });
    },

    validateEmail(val, emailElement) {
        let errorEl = document.querySelector(".errorMsgEmailLive");
        const strLst = val.slice(val.indexOf("@") + 1);
        const isInvalid = val.indexOf('@') === -1 || val.endsWith('@') || !/^[a-zA-Z]+\.[a-zA-Z]+$/.test(strLst);

        if (isInvalid) {
            if (!errorEl) {
                errorEl = document.createElement("p");
                errorEl.className = "errorMsgEmailLive";
                emailElement.insertAdjacentElement("afterend", errorEl);
            }
            errorEl.textContent = "Email is not a valid email address.";
        } else {
            errorEl?.remove();
        }
    },

    setupFormStateValidation() {
        const formFields = `${SELECTORS.LEAD_NAME}, ${SELECTORS.LEAD_MOBILE}, ${SELECTORS.LEAD_EMAIL}, ${SELECTORS.STREAM_SELECT}, ${SELECTORS.LEVEL_SELECT}`;

        $(formFields).on("change keyup", Utils.throttle(() => {
            if (!gmu.config.isLoggedIn) {
                const isFormValid = this.validateFormCompletion();
                $('#join-chat-form-btn-submit').prop('disabled', !isFormValid);
            }
        }, 300));
    },

    validateFormCompletion() {
        const nameFilled = $(SELECTORS.LEAD_NAME).val().trim();
        const emailFilled = $(SELECTORS.LEAD_EMAIL).val().trim();
        const mobileFilled = $(SELECTORS.LEAD_MOBILE).val().trim().length === 10;
        const streamFilled = $(SELECTORS.STREAM_SELECT).val()?.trim().length > 0;
        const levelFilled = $(SELECTORS.LEVEL_SELECT).val()?.trim().length > 0;
        const hasEmailError = $(".errorMsgEmailLive").length > 0;

        return nameFilled && emailFilled && streamFilled && levelFilled && mobileFilled && !hasEmailError;
    },

    setupOtpValidation() {
        $(document).on('input', SELECTORS.OTP_INPUT, (e) => {
            const otpLen = $(e.target).val().length;
            $(SELECTORS.VERIFY_BUTTON).prop('disabled', otpLen !== CONFIG.OTP_LENGTH);
        });
    }
};

// ============================================
// SELECT2 CONFIGURATION
// ============================================
const Select2Config = {
    initializeDropdowns() {
        this.setupStreamSelect();
        this.setupLevelSelect();
    },

    setupStreamSelect() {
        $('.inputStreamLiveContainer select').select2({
            dropdownCssClass: "stream-dropdown",
            containerCssClass: "stream-container",
            placeholder: "Stream Interested",
            name: 'inputStreamLive',
            ajax: {
                url: "/ajax/lead-stream",
                dataType: "json",
                type: "GET",
                data: (params) => ({
                    term: params.term,
                    entity: gmu.config.entity,
                    entity_id: gmu.config.entity_id,
                }),
                processResults: (data) => ({
                    results: $.map(data, (item) => ({
                        text: item.text,
                        id: item.id,
                    })),
                }),
            },
        });
    },

    setupLevelSelect() {
        $('.inputLevelLiveContainer select').select2({
            dropdownCssClass: "level-dropdown",
            containerCssClass: "level-container",
            placeholder: "Level Interested",
            name: 'inputLevelLive',
            ajax: {
                url: "/ajax/lead-level",
                dataType: "json",
                type: "GET",
                data: (params) => ({
                    term: params.term,
                    entity: gmu.config.entity,
                    stream_id: $(SELECTORS.STREAM_SELECT).val(),
                    entity_id: gmu.config.entity_id,
                    page_name: gmu.config.pageName ?? ''
                }),
                processResults: (data) => ({
                    results: $.map(data, (item) => ({
                        text: item.text,
                        id: item.id,
                    })),
                }),
            },
        });
    }
};

// ============================================
// SIMPLIFIED ONLINE STATUS TRACKER
// ============================================
class OnlineStatusTracker {
    constructor(channelId, userId) {
        this.channelId = channelId;
        this.userId = userId;
        this.isOnline = false;
        this.boundHandlers = new Map();

        console.log('🎯 OnlineStatusTracker initialized:', { channelId, userId });

        if (this.channelId && this.userId) {
            this.init();
        } else {
            console.warn('⚠️ Missing channelId or userId');
        }
    }

    init() {
        console.log('🟢 Starting simplified online tracking');
        this.bindVisibilityEvents();
        this.setOnlineStatus();
    }

    bindVisibilityEvents() {
        // Use bound handlers to enable proper cleanup
        this.boundHandlers.set('visibilitychange', () => {
            if (document.hidden) {
                console.log('🔴 TAB HIDDEN - Marking user offline');
                this.setOfflineStatus();
            } else {
                console.log('🟢 TAB VISIBLE - Marking user online');
                this.setOnlineStatus();
            }
        });

        this.boundHandlers.set('blur', () => {
            console.log('🔴 WINDOW BLUR - Marking user offline');
            this.setOfflineStatus();
        });

        this.boundHandlers.set('focus', () => {
            console.log('🟢 WINDOW FOCUS - Marking user online');
            this.setOnlineStatus();
        });

        this.boundHandlers.set('beforeunload', () => {
            console.log('🚪 PAGE UNLOAD - Marking user offline');
            this.setOfflineStatus();
        });

        // Bind all events
        document.addEventListener('visibilitychange', this.boundHandlers.get('visibilitychange'));
        window.addEventListener('blur', this.boundHandlers.get('blur'));
        window.addEventListener('focus', this.boundHandlers.get('focus'));
        window.addEventListener('beforeunload', this.boundHandlers.get('beforeunload'));

        console.log('✅ Visibility event listeners bound');
    }

    setOnlineStatus() {
        if (this.isOnline) return;
        this.isOnline = true;
        console.log('🟢 Setting user ONLINE');
        this.updateUserStatus('online');
    }

    setOfflineStatus() {
        if (!this.isOnline) return;
        this.isOnline = false;
        console.log('🔴 Setting user OFFLINE');
        this.updateUserStatus('offline');
    }

    updateUserStatus(status) {
        const data = {
            user_id: this.userId,
            channel_id: this.channelId,
            online_status: status === 'online' ? 1 : 0,
            timestamp: Date.now()
        };

        console.log(`📤 Updating status to: ${status.toUpperCase()}`, data);

        if (status === 'offline' && navigator.sendBeacon) {
            const formData = new FormData();
            Object.entries(data).forEach(([key, value]) => formData.append(key, value));
            const sent = navigator.sendBeacon('/live-stream-chat-user/update-user-status', formData);
            console.log('📡 Offline status sent via sendBeacon:', sent ? '✅ Success' : '❌ Failed');
        } else {
            fetch('/live-stream-chat-user/update-user-status', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data),
                keepalive: true
            })
                .then(response => response.json())
                .then(result => console.log('📤 Status update result:', result))
                .catch(err => console.warn('❌ Status update failed:', err));
        }
    }

    destroy() {
        console.log('🗑️ Destroying OnlineStatusTracker');
        this.setOfflineStatus();

        // Clean up event listeners
        this.boundHandlers.forEach((handler, event) => {
            if (event === 'visibilitychange') {
                document.removeEventListener(event, handler);
            } else {
                window.removeEventListener(event, handler);
            }
        });
        this.boundHandlers.clear();
    }
}

// ============================================
// ENHANCED LIVE CHAT CLASS
// ============================================
class EnhancedLiveChat {
    constructor(options = {}) {
        // Initialize properties
        Object.assign(this, {
            channelId: options.channelId || '',
            channelType: options.channelType || 'messaging',
            entityType: options.entityType || 'exam',
            entityId: options.entityId || '',
            isLoggedIn: options.isLoggedIn || false,
            currentUser: options.currentUser || null,
            currentUserId: options.currentUserId || '',
            leadData: null,
            streamClient: null,
            channel: null,
            messagesLoaded: false,
            pendingFiles: [],
            onlineStatusTracker: null,
            chatStarted: false,
        });

        this.init();
    }

    init() {
        this.bindEvents();
        this.checkUserStatus();

        console.log("this.isLoggedIn", this.isLoggedIn, this.currentUser);

        if (this.isLoggedIn && this.currentUser) {
            this.initializeGetStream();
        }
    }

    bindEvents() {
        const eventHandlers = [
            { selector: '#join-chat-form-btn', event: 'click', handler: this.showLeadForm.bind(this) },
            { selector: '#join-chat-form-btn-submit', event: 'click', handler: this.handleFormJoinChatClick.bind(this) },
            { selector: '#start-chatting-btn', event: 'click', handler: this.loginUser.bind(this) },
            { selector: SELECTORS.SEND_BUTTON, event: 'click', handler: this.sendMessage.bind(this) },
            { selector: SELECTORS.VERIFY_BUTTON, event: 'click', handler: this.verifyOTP.bind(this) },
            { selector: '.guidelinesClose', event: 'click', handler: this.closeGuidelinesModal.bind(this) },
            { selector: '.chat-tabs .tab', event: 'click', handler: (e) => this.switchTab($(e.target)) },
            { selector: '#change-number-btn', event: 'click', handler: this.showLeadForm.bind(this) },
            { selector: '.attachment-btn', event: 'click', handler: () => $(SELECTORS.FILE_INPUT).click() }
        ];

        // Bind all events
        eventHandlers.forEach(({ selector, event, handler }) => {
            $(document).on(event, selector, (e) => {
                e.preventDefault();
                handler(e);
            });
        });

        // Special handlers
        this.bindSpecialEvents();
    }

    bindSpecialEvents() {
        // Enter key for message input
        $(document).on('keypress', SELECTORS.MESSAGE_INPUT, (e) => {
            if (e.which === 13) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Message input state management
        $(document).on('input', SELECTORS.MESSAGE_INPUT, Utils.throttle((e) => {
            const hasContent = e.target.value.trim() !== '';
            $(SELECTORS.SEND_BUTTON).prop('disabled', !hasContent);
            $(SELECTORS.MESSAGE_INPUT).css("opacity", hasContent ? "1" : "0.5");
        }, 100));

        // File input handling
        $(SELECTORS.FILE_INPUT).on('change', this.handleFileSelection.bind(this));
    }

    handleFileSelection(event) {
        const files = Array.from(event.target.files);

        const validFiles = [];
        const invalidFiles = [];

        files.forEach(file => {
            // Check file size
            if (file.size > CONFIG.MAX_FILE_SIZE) {
                invalidFiles.push({
                    name: file.name,
                    reason: `File too large (${this.formatFileSize(file.size)}). Max allowed: ${this.formatFileSize(CONFIG.MAX_FILE_SIZE)}`
                });
                return;
            }

            // Check file type
            if (!CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
                invalidFiles.push({
                    name: file.name,
                    reason: `File type "${file.type}" not allowed`
                });
                return;
            }

            // Sanitize filename (for temp use)
            const safeName = file.name.replace(/\s+/g, "_").toLowerCase();

            file.tempName = `${Date.now()}_${Math.floor(Math.random() * 10000)}_${safeName}`;
            validFiles.push(file);
        });

        // Show errors for invalid files
        if (invalidFiles.length > 0) {
            const errorMessages = invalidFiles.map(f => `${f.name}: ${f.reason}`);
            this.showError(`Upload failed:\n${errorMessages.join('\n')}`);
        }

        if (this.pendingFiles.length > 5) {
            this.showError('You can only attach a maximum of 10 files.');
            return false;
        }

        // Add valid files
        if (validFiles.length > 0) {
            this.pendingFiles = [...(this.pendingFiles || []), ...validFiles];
            this.updateInputWithFiles(validFiles);
        }
    }

    // Add helper method to format file sizes
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }


    updateInputWithFiles(files) {
        const inputField = document.getElementById('message-input-enhanced');
        files.forEach(file => {
            const truncatedName = file.name.length > CONFIG.MAX_FILENAME_DISPLAY
                ? file.name.slice(0, CONFIG.MAX_FILENAME_DISPLAY) + '…'
                : file.name;
            inputField.value += ` 📂 ${truncatedName} `;
        });

        $(SELECTORS.SEND_BUTTON).prop('disabled', false);
        $(SELECTORS.MESSAGE_INPUT).css("opacity", "1");
    }

    initializeOnlineStatusTracker() {
        console.log('🎯 Checking online status tracker initialization...', {
            hasTracker: !!this.onlineStatusTracker,
            isLoggedIn: this.isLoggedIn,
            hasCurrentUser: !!this.currentUser,
            userId: this.currentUser?.id,
            channelId: this.channelId
        });

        if (!this.onlineStatusTracker && this.isLoggedIn && this.currentUser?.id && this.channelId) {
            console.log('🎯 Initializing online status tracker...');
            this.onlineStatusTracker = new OnlineStatusTracker(this.channelId, this.currentUser.id);
            console.log('✅ Online status tracker initialized successfully');
        } else {
            console.warn('⚠️ Online status tracker not initialized');
        }
    }

    // Add scroll to bottom method
    scrollToBottom() {
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            console.log('📜 Auto-scrolled to bottom of messages');
        }
    }


    async loginUser() {
        try {
            const response = await $.ajax({
                url: '/live-stream-chat-user/login-live-user',
                method: 'POST',
                data: {
                    mobile: $(SELECTORS.LEAD_MOBILE).val(),
                    otp: $(SELECTORS.OTP_INPUT).val(),
                }
            });

            if (response.success) {
                window.location.reload(true);
                console.log('User logged in successfully');
            }
        } catch (error) {
            this.showError('Network error. Please check your connection and try again.');
        }
    }

    checkUserStatus() {
        if (this.isLoggedIn) {
            this.showChatInterface();
        } else {
            this.showLeadForm();
        }
    }

    handleFormJoinChatClick() {
        const formDataLead = {
            name: $(SELECTORS.LEAD_NAME).val().trim(),
            phone: $(SELECTORS.LEAD_MOBILE).val().trim(),
            email: $(SELECTORS.LEAD_EMAIL).val().trim(),
            stream: $(SELECTORS.STREAM_SELECT).val(),
            level: $(SELECTORS.LEVEL_SELECT).val(),
            entity: gmu.config.entity,
            entity_id: gmu.config.entity_id,
            type: 'live_chat_widget',
            url: gmu.config.page_url,
            platform: gmu.config.isMobile == '1' ? 'wap' : 'web',
            entity_type: gmu.config.entity,
            entity_subtype: gmu.config.entity_subtype,
        };

        this.leadData = formDataLead;
        this.handleLeadFormSubmission(this.leadData);
    }

    async handleLeadFormSubmission(formData) {
        try {
            const response = await $.ajax({
                url: '/live-stream-chat-user/save-lead',
                method: 'POST',
                data: formData
            });

            if (response.success) {
                localStorage.setItem('studentId', response.student_id);
                const messageCount = localStorage.getItem('messageCount');
                var discussionText = '<strong>' + messageCount + ' students</strong> discussing';
                $(SELECTORS.DISCUSSION_COUNT_OTP).html(discussionText);
                this.showOtpBox();
            }
        } catch (error) {
            this.showError('Network error. Please check your connection and try again.');
        }
    }

    showOtpBox() {
        const mobile = $(SELECTORS.LEAD_MOBILE).val();
        $(".otplabel").show().text(`OTP sent to ${mobile}`);
        $('.join-form-section').hide();
        $(SELECTORS.OTP_BOX).show();

        this.sendOTP(mobile);
    }

    async sendOTP(mobile) {
        try {
            const response = await $.ajax({
                url: '/lead-v4/send-otp-lead',
                method: 'POST',
                data: { phone: mobile, is_lead: 1 }
            });

            if (response.success) {
                console.log('OTP sent successfully');
            }
        } catch (error) {
            this.showError('Network error. Please check your connection and try again.');
        }
    }

    async verifyOTP() {
        try {
            const response = await $.ajax({
                url: '/lead-v4/verify-otp',
                method: 'POST',
                data: {
                    otp: $(SELECTORS.OTP_INPUT).val(),
                    mobile: $(SELECTORS.LEAD_MOBILE).val(),
                    student_id: localStorage.getItem('studentId') ?? '',
                }
            });

            const otpLabel = $('.otplabel');
            if (response.success) {
                otpLabel.removeClass('errorMsgOtpLive').text("OTP Verified Successfully!");
                localStorage.setItem('studentName', response.studentName);
                localStorage.setItem('studentEmail', response.studentEmail);
                this.showGuidelinesModal();
            } else {
                otpLabel.addClass('errorMsgOtpLive').text("Invalid OTP");
            }
        } catch (error) {
            this.showError('Network error. Please check your connection and try again.');
        }
    }

    showGuidelinesModal() {
        $(".pageMask").show();
        $("body").css("overflowY", "hidden");

        if (typeof window.showGuidelinesModal === 'function') {
            window.showGuidelinesModal();
        } else {
            $('#chatGuidelinesModal').fadeIn(300).addClass('show');
        }
    }

    closeGuidelinesModal() {
        $(".pageMask").hide();
        $("body").css("overflowY", "unset");

        if (typeof window.hideGuidelinesModal === 'function') {
            window.hideGuidelinesModal();
        } else {
            $('#chatGuidelinesModal').fadeOut(300).removeClass('show');
        }
    }

    async initializeGetStream() {
        try {
            console.log('🚀 Initializing real GetStream connection...');
            const tokenResponse = await this.getUserToken();
            if (!tokenResponse.success) {
                throw new Error('Failed to get user token: ' + tokenResponse.message);
            }

            if (typeof StreamChat !== 'undefined') {
                this.streamClient = StreamChat.getInstance(tokenResponse.api_key);

                await this.streamClient.connectUser(
                    {
                        id: tokenResponse.user_id,
                        name: this.getUserName()
                    },
                    tokenResponse.token
                );

                this.channel = this.streamClient.channel(this.channelType, this.channelId, {
                    name: `Live Discussion - ${this.entityType}`,
                    members: [tokenResponse.user_id]
                });

                await this.channel.watch();
                this.setupGetStreamEventListeners();
                this.initializeOnlineStatusTracker();

                // Auto-scroll to bottom after initialization
                this.scrollToBottom();

                console.log('✅ GetStream initialized successfully');
                return true;
            } else {
                console.warn('⚠️ GetStream SDK not loaded, using fallback');
                this.initializeOnlineStatusTracker();
                return this.initializeFallback();
            }
        } catch (error) {
            console.error('❌ GetStream initialization failed:', error);
            this.initializeOnlineStatusTracker();
            return this.initializeFallback();
        }
    }

    async getUserToken() {
        const userId = this.getStreamUserId();
        const userName = this.getUserName();
        console.log('getUserToken', userId, userName);

        return await $.ajax({
            url: '/get-stream/get-user-token',
            method: 'POST',
            data: {
                user_id: userId,
                user_name: userName,
                user_email: this.currentUser?.email || null
            }
        });
    }

    initializeFallback() {
        console.log('🔄 Using fallback chat implementation');
        this.streamClient = { connected: false, fallback: true };
        this.channel = { id: this.channelId, fallback: true };
        return true;
    }

    setupGetStreamEventListeners() {
        if (!this.channel || this.channel.fallback) return;

        const eventHandlers = {
            'message.new': (event) => {
                console.log('📨 New message from GetStream:', event.message);
                this.handleGetStreamMessage(event.message);
            },
            'typing.start': (event) => {
                if (event.user.id !== this.getStreamUserId()) {
                    this.showTypingIndicator(event.user);
                }
            },
            'typing.stop': (event) => {
                if (event.user.id !== this.getStreamUserId()) {
                    this.hideTypingIndicator(event.user);
                }
            }
        };

        Object.entries(eventHandlers).forEach(([event, handler]) => {
            this.channel.on(event, handler);
        });

        this.streamClient.on('connection.changed', (event) => {
            console.log('🔌 GetStream connection changed:', event);
        });
    }

    showTypingIndicator(user) {
        const typingId = `typing-${user.id}`;
        if (!$(`#${typingId}`).length) {
            const typingHtml = `
                <div id="${typingId}" class="typing-indicator">
                    <span class="typing-user">${user.name}</span> is typing
                    <div class="typing-dots">
                        <span></span><span></span><span></span>
                    </div>
                </div>
            `;
            $('#typing-indicators').append(typingHtml);
        }
    }

    hideTypingIndicator(user) {
        $(`#typing-${user.id}`).remove();
    }

    handleGetStreamMessage(message) {
        // Check if the message text contains a URL
        const hasLink = /https?:\/\//.test(message.text);

        // Determine message type
        let messageType = 'text';
        if (message.attachments && message.attachments.length > 0) {
            messageType = 'file';
        } else if (hasLink) {
            messageType = 'link';
        }

        const messageObj = {
            user: {
                name: message.user.name,
                id: message.user.id,
                role: message.user.id === 'admin' ? 'moderator' : 'student'
            },
            text: message.text,
            created_at: new Date(message.created_at),
            id: message.id,
            files: message.attachments || [],
            type: messageType,  // Add the proper message type
            isCurrentUser: false // Messages from GetStream are never from current use
        };

        if (message.user.id !== this.getStreamUserId()) {
            this.addMessageToUI(messageObj);
        }
    }

    showLeadForm() {
        $('#chat-lead-form').show();
        $('#chat-interface').hide();
        $(SELECTORS.OTP_BOX).hide();
        $('.join-form-section').show();
    }

    showChatInterface() {
        $('#chat-lead-form').hide();
        $('#chat-interface').show();
        this.chatStarted = true;
    }

    renderMessages(messages) {
        const messagesArea = $(SELECTORS.CHAT_MESSAGES_AREA);
        const fragment = document.createDocumentFragment();

        messages.forEach(message => {
            const messageElement = $(this.createMessageHtml(message))[0];
            fragment.appendChild(messageElement);
        });

        messagesArea.append(fragment);
        messagesArea.scrollTop(messagesArea[0].scrollHeight);
    }

    sendMessage() {
        try {
            const messageInput = $(SELECTORS.MESSAGE_INPUT);
            if (!messageInput.length) {
                console.error('Message input not found');
                return;
            }

            const messageText = messageInput.val().trim();
            const hasFiles = this.pendingFiles && this.pendingFiles.length > 0;
            const hasLink = /https?:\/\//.test(messageText);

            if (!messageText && !hasFiles && !hasLink) return;

            const message = {
                id: Date.now().toString(),
                user: {
                    id: this.getStreamUserId(), // Add user ID
                    name: this.getUserName(),
                    role: 'student'
                },
                text: hasFiles ? 'Files Attached' : messageText,
                created_at: new Date(),
                avatar: Utils.getInitials(this.getUserName()),
                type: hasFiles ? 'file' : (hasLink ? 'link' : 'text'),
                files: this.pendingFiles || [],
                isCurrentUser: true // Mark as current user's message
            };

            this.addMessageToUI(message);
            messageInput.val('');
            this.sendToGetStream(message);
        } catch (error) {
            console.error('Error sending message:', error);
            this.showError('Failed to send message. Please try again.');
        }
    }

    addMessageToUI(message) {
        const messagesContainer = $(SELECTORS.MESSAGES_CONTAINER);
        if (messagesContainer.length > 0) {
            const messageHtml = this.createNewMessageHtml(message);
            messagesContainer.append(messageHtml);
            messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
        }
        $(SELECTORS.SEND_BUTTON).prop('disabled', true);
        $(SELECTORS.MESSAGE_INPUT).css("opacity", "0.5");
    }

    createNewMessageHtml(message) {
        const messageTime = message.created_at || new Date();
        const timeStr = Utils.formatTime(messageTime);
        const isoTimeStr = messageTime.toISOString();

        if (message.user.role === 'moderator') {
            return this.createModeratorMessageHtml(message);
        }

        return this.createUserMessageHtml(message, timeStr, isoTimeStr);
    }

    createUserMessageHtml(message, timeStr, isoTimeStr) {
        let contentHtml = '';

        // Build content based on message type
        if (message.type === 'file' && message.files && message.files.length > 0) {
            contentHtml = this.createFileAttachmentHtml(message.files);
        } else if (message.type === 'link' && message.text) {
            const safeText = Utils.escapeHtml(message.text);
            contentHtml = `<a class="chat-user-link" href="${safeText}" target="_blank">${safeText}</a>`;
        } else if (message.text && message.text.trim() !== '') {
            const safeTextHTML = Utils.escapeHtml(message.text).replace(/\n/g, '<br>');
            contentHtml = safeTextHTML;
        }

        // Determine if this is current user's message
        const isCurrentUser = message.user.id == this.currentUserId; // Default to true for backward compatibility
        const wrapperClass = isCurrentUser ? 'message user-message highlighted' : 'message user-message';
        const footerClass = isCurrentUser ? 'current-user-live' : '';

        // Generate avatar and username for other users (following your PHP template)
        let avatarAndHeaderHtml = '';
        if (!isCurrentUser) {
            const initials = Utils.getInitials(message.user.name);
            const palette = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA726', '#AB47BC', '#26A69A'];
            const avatarColor = palette[Math.abs(message.user.name.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % palette.length];

            avatarAndHeaderHtml = `
            <div class="message-avatar" style="background:${avatarColor};color:#fff;">${initials}</div>
        `;
        }
        return `
        <div class="${wrapperClass}" data-timestamp="${isoTimeStr}">
            ${avatarAndHeaderHtml}
            <div class="message-content">
                ${!isCurrentUser ? `
                    <div class="message-header">
                        <span class="username">${Utils.escapeHtml(message.user.name)}</span>
                    </div>
                ` : ''}
                <div class="message-text ${isCurrentUser ? 'info-text' : ''}">${contentHtml}</div>
                <div class="message-footer ${footerClass}">
                    ${isCurrentUser ? `
                    <span class="tick-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#3ae478" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-check h-3 w-3 text-accent">
                            <path d="M18 6 7 17l-5-5"></path>
                            <path d="m22 10-7.5 7.5L13 16"></path>
                        </svg>
                    </span>
                    ` : ''}
                    <span class="timestamp">
                        <svg xmlns="http://www.w3.org/2000/svg" width="${isCurrentUser ? 16 : 12}" height="${isCurrentUser ? 16 : 12}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock ${isCurrentUser ? 'h-3 w-3 text-muted' : ''}">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        ${timeStr}
                        </span>
                </div>
            </div>
        </div>
    `;
    }

    createFileAttachmentHtml(files, isModerator = false) {
        const attachmentsHtml = files.map(file => {

            let fileUrl = '';
            let fileName = '';

            if (file.asset_url) {
                fileUrl = file.asset_url;
                fileName = file.title;
            } else {
                fileUrl = `${CONFIG.MEDIA_BASE_URL}${encodeURIComponent(file.tempName)}`;
                fileName = file.name;
            }

            // if (isModerator) {
            //     fileUrl = file.asset_url;
            //     fileName = Utils.escapeHtml(file.title);
            // } else {
            //     fileUrl = `${CONFIG.MEDIA_BASE_URL}${encodeURIComponent(file.tempName)}`;
            //     fileName = Utils.escapeHtml(file.name);
            // }

            // Normalize filename (replace spaces with underscores + lowercase)
            if (fileName) {
                fileName = fileName.replace(/\s+/g, '_').toLowerCase();
            }

            const isImage = file.type.startsWith('image/');
            const icon = isImage ? '🖼️' : '📂';

            return `
                <div class="message-text attachment">
                    <a href="${fileUrl}" 
                       target="_blank" 
                       ${!isImage ? `download="${fileName}"` : ''} 
                       class="chat-attachment-link">
                        ${icon} ${fileName}
                    </a>
                </div>`;
        }).join('');

        return `
            <div class="message-text attachment">
                <div class="attachments-inline">
                    ${attachmentsHtml}
                </div>
            </div>
        `;
    }

    createModeratorMessageHtml(message) {
        const messageTime = message.created_at || new Date();
        const timeStr = Utils.formatTime(messageTime);
        const isoTimeStr = messageTime.toISOString();

        let contentHtml = '';

        if (message.text && message.text.trim() !== '') {
            contentHtml += message.text;
        }

        // If attachments exist
        if (message.files && message.files.length > 0) {
            contentHtml += this.createFileAttachmentHtml(message.files, true);
        }

        return `
        <div class="message moderator-message" data-timestamp="${isoTimeStr}">
            <div class="message-avatar" style="background:#d81b60;color:#fff;">AD</div>
            <div class="message-content">
                <div class="message-header">
                    <span class="username">Admin</span>
                    <span class="badge mod-badge">Mod</span>
                </div>
                <div class="message-text moderator-text">${contentHtml}</div>
                 <div class="message-footer">
                    <span class="tick-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#3ae478" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-check h-3 w-3 text-accent">
                            <path d="M18 6 7 17l-5-5"></path>
                            <path d="m22 10-7.5 7.5L13 16"></path>
                        </svg>
                    </span>
                    <span class="clock-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-3 w-3 text-muted">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                    </span>
                        <span class="timestamp">${timeStr}</span>
                </div>
            </div>
        </div>
    `;
    }

    async sendToGetStream(message) {
        try {
            console.log('🚀 Sending to GetStream:', message);

            if (this.streamClient && this.channel && !this.channel.fallback) {
                const streamMessage = this.prepareStreamMessage(message);
                const response = await this.channel.sendMessage(streamMessage);

                if (response.message.id) {
                    console.log('✅ Message sent to GetStream successfully:', response.message.id);
                    await this.sendToBackend({ ...message, files: streamMessage.attachments }, response.message.id);
                }
                return response;
            } else {
                console.log('⚠️ GetStream not available, message saved to database only');
                return { success: true, fallback: true };
            }
        } catch (error) {
            console.error('❌ GetStream send failed:', error);
            return { success: false, error: error.message };
        }
    }

    prepareStreamMessage(message) {
        let attachments = [];
        if (message.files && message.files.length > 0) {
            attachments = message.files.map((file) => {
                // Enhanced file type mapping for GetStream
                let streamType = 'file'; // default

                if (file.type.startsWith('image/')) {
                    streamType = 'image';
                } else if (file.type.startsWith('video/')) {
                    streamType = 'video';
                } else if (file.type === 'application/pdf') {
                    streamType = 'file';
                } else if (file.type === 'application/zip' ||
                    file.type === 'application/x-zip-compressed') {
                    streamType = 'file'; // ZIP files are treated as regular files in GetStream
                }

                return {
                    type: streamType,
                    asset_url: `${CONFIG.MEDIA_BASE_URL}${encodeURIComponent(file.tempName)}`,
                    title: file.name,
                    file_size: file.size || 0,
                    mime_type: file.type
                };
            });
        }

        return {
            text: this.pendingFiles.length === 0 ? message.text : 'Files Attached',
            attachments: attachments,
            custom_data: {
                entity_type: this.entityType,
                entity_id: this.entityId,
                local_message_id: message.id
            }
        };
    }

    async sendToBackend(message, streamMessageId) {
        try {
            const formData = this.createFormData(message, streamMessageId);

            const response = await $.ajax({
                url: '/live-stream-chat-user/save-chat-message',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 10000
            });

            if (response.success) {
                this.clearPendingFiles();
                console.log('✅ Message sent successfully to backend');
            } else {
                this.showError('Failed to send message: ' + (response.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('❌ Failed to send message:', error);
            this.showError('Failed to send message. Please try again.');
        }
    }

    createFormData(message, streamMessageId) {
        const formData = new FormData();
        const streamUserId = this.getStreamUserId();

        const baseData = {
            channel_id: this.channelId,
            message_text: message.text,
            user_name: message.user.name,
            user_email: this.currentUser?.email || null,
            entity_type: this.entityType,
            entity_id: this.entityId,
            user_id: streamUserId,
            stream_message_id: streamMessageId,
            type: message.type
        };

        Object.entries(baseData).forEach(([key, value]) => {
            formData.append(key, value);
        });
        console.log('baseData', this.pendingFiles, this.pendingFiles.length);
        if (this.pendingFiles && this.pendingFiles.length > 0) {
            this.pendingFiles.forEach((file, i) => {
                formData.append(`files[${i}]`, file);
                formData.append(`tempNames[${i}]`, file.tempName);
                formData.append(`types[${i}]`, file.type);
            });
        }
        console.log('formData', formData);
        return formData;
    }

    clearPendingFiles() {
        this.pendingFiles = [];
        const fileInput = document.getElementById('file-input-live-chat');
        if (fileInput) fileInput.value = '';
    }

    switchTab(tabElement) {
        $('.chat-tabs .tab').removeClass('active');
        tabElement.addClass('active');
        const tabText = tabElement.text();
        console.log('Switched to tab:', tabText);
    }

    getUserName() {
        return this.currentUser?.name ||
            this.leadData?.name ||
            document.getElementById('lead-name')?.value?.trim() ||
            'Guest User';
    }

    getStreamUserId() {
        if (this.currentUser?.id) {
            return `${this.currentUser.id}`;
        } else if (this.leadData?.name) {
            const hash = this.leadData.name.toLowerCase().replace(/\s+/g, '-');
            return `${hash}-${Date.now()}`;
        } else {
            return `anonymous-${Date.now()}`;
        }
    }

    showError(message) {
        if (typeof toastr !== 'undefined') {
            toastr.error(message);
        } else {
            console.error(message);
        }
    }

    destroy() {
        if (this.onlineStatusTracker) {
            this.onlineStatusTracker.destroy();
            this.onlineStatusTracker = null;
        }
    }
}

// ============================================
// HELPER FUNCTIONS
// ============================================
function toggleLevelSelectValue(level = '') {
    const container = $(".inputLevelLiveContainer");
    const select = $('.inputLevelLiveContainer select');

    if (level !== '' && level !== '12') {
        container.hide();
        select.append($('<option>', {
            value: level,
            selected: true,
        })).prop('disabled', false);
    } else {
        container.show();
        $(SELECTORS.LEVEL_SELECT).prop("disabled", false);
    }
    setTimeout(calculateAndSetWidgetHeight, 100);
}

function toggleBoardLevel() {
    const container = $(".inputLevelLiveContainer");
    const select = $('.inputLevelLiveContainer select');

    const levelMap = {
        "10": 4,
        "12": 1
    };

    const level = levelMap[gmu.config.board_level];
    if (level) {
        container.hide();
        select.append($('<option>', {
            value: level,
            selected: true,
        })).prop('disabled', false);
    }
    setTimeout(calculateAndSetWidgetHeight, 100);
}

function toggleStreamField(stream_id = '', level = '') {
    const highest_qualification_degree_id = ['9', '10', '11'];
    const container = $(".inputStreamLiveContainer");
    const select = $('.inputStreamLiveContainer select');

    if (stream_id === '' || stream_id === '22' || highest_qualification_degree_id.includes(level)) {
        container.show();
    } else {
        container.hide();
        select.append($('<option>', {
            value: stream_id,
            selected: true
        }));
    }
    toggleLevelSelectValue(level);
}

async function selectStreamLevelAutoFetch() {
    try {
        const response = await $.ajax({
            type: 'POST',
            url: '/ajax/lead-auto-pop-up',
            data: {
                entity: gmu.config.entity,
                auto_pop_entity_id: gmu.config.entity_id
            },
            dataType: "json"
        });

        if (response.success) {
            const { entity, pageName } = gmu.config;

            if ((entity === 'exam' && pageName !== "exam-category") ||
                ['course', 'course_stream'].includes(entity)) {
                if (response.stream_id !== "") {
                    toggleStreamField(response.stream_id, response.level, response.specialization_id);
                }
            } else if (entity === 'exam' && pageName === "exam-category") {
                toggleStreamField(gmu.config.entity_id, '', '');
            } else if (['articles', 'news'].includes(entity)) {
                if (response.stream_id !== "") {
                    toggleStreamField(response.stream_id, response.level);
                }
            } else if (entity === 'board') {
                toggleBoardLevel();
            }
        }
    } catch (error) {
        console.error('Failed to fetch stream/level data:', error);
    }
}

// ============================================
// ONLINE USER COUNT MANAGEMENT
// ============================================
async function updateOnlineUserCount() {
    try {
        const data = await $.ajax({
            url: '/live-stream-chat-user/get-online-users',
            method: 'GET',
            dataType: 'json',
            data: { channel_id: window.liveChatChannelId }
        });

        if (data.success) {
            $(SELECTORS.ONLINE_COUNT).text(data.online_count || 0);

            if (data.online_user_names.length > 0) {
                $('.welcome-notification').show();
                var userNameText = '🎉 ' + data.online_user_names
                    .map(user => `<strong>${user.name}</strong>`)
                    .join(', ') + ' joined the chat';

                $(SELECTORS.JOINED_USER).html(userNameText);
            } else {
                $('.welcome-notification').hide();
                $(SELECTORS.JOINED_USER).html('');
            }
        }
    } catch (error) {
        console.error('Error fetching online user count:', error);
    }
}

// ============================================
// TOTAL MESSAGE COUNT MANAGEMENT
// ============================================
async function getAllMessageCount() {
    try {
        const data = await $.ajax({
            url: '/live-stream-chat-user/get-all-message-count',
            method: 'GET',
            dataType: 'json',
            data: { channel_id: window.liveChatChannelId }
        });

        if (data.success) {
            console.log('Total messages:', data.message_count);
            localStorage.setItem('messageCount', data.message_count);
            var discussionText = '<strong>' + data.message_count + ' students</strong> discussing';
            $(SELECTORS.DISCUSSION_COUNT).html(discussionText);
        }
    } catch (error) {
        console.error('Error fetching all message count:', error);
    }
}

// ============================================
// WIDGET HEIGHT CALCULATION
// ============================================
const WidgetManager = {
    calculateAndSetWidgetHeight: Utils.debounce(function () {
        if (window.innerWidth <= 768) {
            const widget = document.getElementById('enhanced-chat-widget');
            if (!widget) return;

            const dimensions = this.calculateDimensions();
            const totalHeight = this.sumDimensions(dimensions);

            widget.style.height = totalHeight + 'px';
            widget.style.minHeight = totalHeight + 'px';

            this.logDimensions(dimensions, totalHeight);
        }
    }, 100),

    calculateDimensions() {
        return {
            header: 185,
            baseFormPadding: 40,
            recentMessages: this.calculateRecentMessagesHeight(),
            formFields: this.calculateFormFieldsHeight(),
            otpBox: this.calculateOtpBoxHeight()
        };
    },

    calculateRecentMessagesHeight() {
        const messagesPreview = document.querySelector('.recent-messages-preview');
        const messageItems = document.querySelectorAll('.preview-item');

        if (messagesPreview && messageItems.length > 0) {
            const titleHeight = 25;
            const itemHeight = 55;
            const previewPadding = 20;
            return titleHeight + (messageItems.length * itemHeight) + previewPadding;
        }
        return 0;
    },

    calculateFormFieldsHeight() {
        const formFields = document.querySelectorAll('.lead-form .form-control:not([style*="display: none"])');
        const visibleFields = Array.from(formFields).filter(field => field.offsetParent !== null);

        if (visibleFields.length > 0) {
            const fieldHeight = 40;
            const fieldGap = 8;
            const buttonHeight = 45;
            const formSectionPadding = 50;
            const statsHeight = 60;

            return statsHeight + (visibleFields.length * fieldHeight) +
                ((visibleFields.length - 1) * fieldGap) +
                buttonHeight + formSectionPadding;
        }
        return 0;
    },

    calculateOtpBoxHeight() {
        const otpBox = document.getElementById('otp-box-live-chat');
        return (otpBox && otpBox.style.display !== 'none') ? 220 : 0;
    },

    sumDimensions(dimensions) {
        return Object.values(dimensions).reduce((sum, value) => sum + value, 0);
    },

    logDimensions(dimensions, total) {
        const messageItems = document.querySelectorAll('.preview-item');
        const formFields = document.querySelectorAll('.lead-form .form-control:not([style*="display: none"])');
        const visibleFields = Array.from(formFields).filter(field => field.offsetParent !== null);

        console.log('Widget height calculated:', {
            ...dimensions,
            total,
            messageCount: messageItems.length,
            fieldCount: visibleFields.length
        });
    }
};

// Create global reference for backward compatibility
const calculateAndSetWidgetHeight = WidgetManager.calculateAndSetWidgetHeight.bind(WidgetManager);

// ============================================
// INITIALIZATION SEQUENCE
// ============================================
async function initializeApplication() {
    calculateAndSetWidgetHeight();

    try {
        await initializeStreamChat();
        const widget = document.querySelector(SELECTORS.WIDGET);

        if (widget) {
            const chatInstance = createChatInstance();
            if (chatInstance) {
                window.enhancedLiveChat = chatInstance;
                console.log('Enhanced live chat initialized successfully');
            }
        } else {
            console.log('Enhanced chat widget not found on this page');
        }
    } catch (error) {
        console.error('❌ Failed during initialization sequence:', error);
    }

    // Initialize form components
    FormValidators.initializeValidation();
    Select2Config.initializeDropdowns();
}

function createChatInstance() {
    const configEl = document.getElementById('live-chat-config');
    if (!configEl) return null;

    const loggedInUserId = localStorage.getItem('studentId') ?? '';
    const loggedInUserName = localStorage.getItem('studentName') ?? '';
    const loggedInUserEmail = localStorage.getItem('studentEmail') ?? '';

    const chatConfig = {
        channelId: configEl.dataset.channelId,
        channelType: configEl.dataset.channelType,
        entityType: configEl.dataset.entityType,
        entityId: parseInt(configEl.dataset.entityId, 10),
        isLoggedIn: gmu.config.isLoggedIn,
        currentUser: {
            id: loggedInUserId,
            name: loggedInUserName,
            email: loggedInUserEmail
        },
        currentUserId: loggedInUserId,
        leadData: null,
        streamClient: null,
        channel: null,
        messagesLoaded: false
    };

    console.log('Chat config:', chatConfig);
    return new EnhancedLiveChat(chatConfig);
}

// ============================================
// DOM READY INITIALIZATION
// ============================================
document.addEventListener('DOMContentLoaded', async () => {
    await initializeApplication();
});

// ============================================
// PERIODIC UPDATES
// ============================================
setInterval(updateOnlineUserCount, CONFIG.UPDATE_INTERVAL);

// ============================================
// AUTO-FETCH INITIALIZATION
// ============================================
selectStreamLevelAutoFetch();

// ============================================
// GLOBAL LOGGING
// ============================================
console.log('Enhanced live chat script loaded', window.liveChatChannelId);
<?php

return [
    'enablePrettyUrl' => true,
    'showScriptName' => false,
    'rules' => [

        // common application form
        'common-application-form' => 'common-application-form/index',
        'terms-and-conditions' => 'site/terms-and-conditions',

        // Live Chat Routes
        'stream-chat/chat/<channelId>' => 'stream-chat/chat',
        'stream-chat/<action>' => 'stream-chat/<action>',

        //for other language
        '<lang:(hi)>/author/<slug>' => 'site/author-profile',

        'author/<slug>' => 'site/author-profile',

        //exams in other language
        '<lang:(hi)>/exams' => 'exam/home',

        '<lang:(hi)>/exams/<discipline>-exams-in-<location>' => 'exam/filter',

        '<lang:(hi)>/exams/<exam>-<subject:(physics|chemistry|mathematics|drawing|planning|biology|english|botany|zoology|quantitative-aptitude|logical-reasoning-data-interpretation|verbal-ability-and-reading-comprehension|general-awareness|innovation-and-entrepreneurship|decision-making|general-knowledge|language-comprehension|agriculture)>-syllabus' => 'exam/detail',
        '<lang:(hi)>/exams/<exam>-<subject:(animal-husbandry-and-veterinary-science|anthropology|civil-engineering|commerce-accountancy|economics|electrical-engineering|geography|geology|history|law|management|mechanical-engineering|medical- science|philosophy|political-science-international-relations|psychology|public-administration|sociology|statistics)>-syllabus' => 'exam/detail',

        '<lang:(hi)>/exams/<exam>-<subject:(physics|chemistry|mathematics|drawing|planning|biology|english|botany|zoology|quantitative-aptitude|logical-reasoning-data-interpretation|verbal-ability-and-reading-comprehension|general-awareness|innovation-and-entrepreneurship|decision-making|general-knowledge|language-comprehension|agriculture)>-answer-key' => 'exam/detail',
        '<lang:(hi)>/exams/<exam>-<subject:(animal-husbandry-and-veterinary-science|anthropology|civil-engineering|commerce-accountancy|economics|electrical-engineering|geography|geology|history|law|management|mechanical-engineering|medical- science|philosophy|political-science-international-relations|psychology|public-administration|sociology|statistics)>-answer-key' => 'exam/detail',

        '<lang:(hi)>/exams/<exam>-<subject:(physics|chemistry|mathematics|drawing|planning|biology|english|botany|zoology|quantitative-aptitude|logical-reasoning-data-interpretation|verbal-ability-and-reading-comprehension|general-awareness|innovation-and-entrepreneurship|decision-making|general-knowledge|language-comprehension|agriculture)>-exam-pattern' => 'exam/detail',
        '<lang:(hi)>/exams/<exam>-<subject:(chapterwise-weightage|animal-husbandry-and-veterinary-science|anthropology|civil-engineering|commerce-accountancy|economics|electrical-engineering|geography|geology|history|law|management|mechanical-engineering|medical- science|philosophy|political-science-international-relations|psychology|public-administration|sociology|statistics)>-exam-pattern' => 'exam/detail',

        '<lang:(hi)>/exams/<exam:(bsnl-recruitment|rbi-recruitment|tnpsc-recruitment|ippb-recruitment)>-<page:(admit-card|answer-key-content|form|application-process|coaching|counselling|cut-off|eligibility|exam-centres|exam-pattern|important-dates|notification|previous-years-papers|recruitment|reference-books|result-cutoff|results|mock-sample-tests|syllabus|vacancy|answer-key)>' => 'exam/detail',

        '<lang:(hi)>/exams/<exam:(bsnl-recruitment|rbi-recruitment|tnpsc-recruitment|ippb-recruitment)>' => 'exam/detail',

        '<lang:(hi)>/exams/<exam>-<page:(admit-card|answer-key-content|form|application-process|coaching|counselling|cut-off|eligibility|exam-centres|exam-pattern|important-dates|notification|previous-years-papers|recruitment|reference-books|result-cutoff|results|mock-sample-tests|syllabus|vacancy|answer-key|registration|paper-analysis|preparation|marks-vs-rank|score-vs-percentile|participating-colleges)>' => 'exam/detail',

        '<lang:(hi)>/exams/<exam>-<page:(slot-booking|login|topper|response-sheet|rank-predictor|percentile-predictor|phase-1|phase-2|phase-3|reservation|photo-size-guidelines|form-correction|dress-code|exam-day-guidelines|study-material|2024-question-paper|2023-question-paper|2022-question-paper|2021-question-paper|merit-list|score-card|qualifying-marks|slot-booking|seat-allotment|seat-matrix|sample-papers|coaching-institutes|college-predictor|selection-process)>' => 'exam/detail',
        '<lang:(hi)>/exams/<exam>' => 'exam/detail',

        //exams
        'exams' => 'exam/home',

        'exams/<discipline>-exams-in-<location>' => 'exam/filter',

        // 'exams/<discipline>-exams-in-<location>' => 'exam/discipline',
        'exams/<exam>-<subject:(physics|chemistry|mathematics|drawing|planning|biology|english|botany|zoology|quantitative-aptitude|logical-reasoning-data-interpretation|verbal-ability-and-reading-comprehension|general-awareness|innovation-and-entrepreneurship|decision-making|general-knowledge|language-comprehension|agriculture)>-syllabus' => 'exam/detail',
        'exams/<exam>-<subject:(animal-husbandry-and-veterinary-science|anthropology|civil-engineering|commerce-accountancy|economics|electrical-engineering|geography|geology|history|law|management|mechanical-engineering|medical- science|philosophy|political-science-international-relations|psychology|public-administration|sociology|statistics)>-syllabus' => 'exam/detail',

        'exams/<exam>-<subject:(physics|chemistry|mathematics|drawing|planning|biology|english|botany|zoology|quantitative-aptitude|logical-reasoning-data-interpretation|verbal-ability-and-reading-comprehension|general-awareness|innovation-and-entrepreneurship|decision-making|general-knowledge|language-comprehension|agriculture)>-answer-key' => 'exam/detail',
        'exams/<exam>-<subject:(animal-husbandry-and-veterinary-science|anthropology|civil-engineering|commerce-accountancy|economics|electrical-engineering|geography|geology|history|law|management|mechanical-engineering|medical- science|philosophy|political-science-international-relations|psychology|public-administration|sociology|statistics)>-answer-key' => 'exam/detail',

        'exams/<exam>-<subject:(physics|chemistry|mathematics|drawing|planning|biology|english|botany|zoology|quantitative-aptitude|logical-reasoning-data-interpretation|verbal-ability-and-reading-comprehension|general-awareness|innovation-and-entrepreneurship|decision-making|general-knowledge|language-comprehension|agriculture)>-exam-pattern' => 'exam/detail',
        'exams/<exam>-<subject:(chapterwise-weightage|animal-husbandry-and-veterinary-science|anthropology|civil-engineering|commerce-accountancy|economics|electrical-engineering|geography|geology|history|law|management|mechanical-engineering|medical- science|philosophy|political-science-international-relations|psychology|public-administration|sociology|statistics)>-exam-pattern' => 'exam/detail',

        'exams/<exam:(bsnl-recruitment|rbi-recruitment|tnpsc-recruitment|ippb-recruitment)>-<page:(admit-card|answer-key-content|form|application-process|coaching|counselling|cut-off|eligibility|exam-centres|exam-pattern|important-dates|notification|previous-years-papers|recruitment|reference-books|result-cutoff|results|mock-sample-tests|syllabus|vacancy|answer-key)>' => 'exam/detail',

        'exams/<exam:(bsnl-recruitment|rbi-recruitment|tnpsc-recruitment|ippb-recruitment)>' => 'exam/detail',

        'exams/<exam>-<page:(admit-card|answer-key-content|form|application-process|coaching|counselling|cut-off|eligibility|exam-centres|exam-pattern|important-dates|notification|previous-years-papers|recruitment|reference-books|result-cutoff|results|mock-sample-tests|syllabus|vacancy|answer-key|registration|paper-analysis|preparation|marks-vs-rank|score-vs-percentile|participating-colleges)>' => 'exam/detail',

        'exams/<exam>-<page:(slot-booking|login|topper|response-sheet|rank-predictor|percentile-predictor|phase-1|phase-2|phase-3|reservation|photo-size-guidelines|form-correction|dress-code|exam-day-guidelines|study-material|2024-question-paper|2023-question-paper|2022-question-paper|2021-question-paper|merit-list|score-card|qualifying-marks|slot-booking|seat-allotment|seat-matrix|sample-papers|coaching-institutes|college-predictor|selection-process)>' => 'exam/detail',

        'exams/<exam>' => 'exam/detail',

        //article in other languages
        '<amp:(amp)>/articles/<slug>' => 'article/route',
        '<lang:(hi)>/articles' => 'article/index',
        '<lang:(hi)>/articles/<slug>' => 'article/route',
        '<amp:(amp)>/<lang:(hi)>/articles/<slug>' => 'article/route',

        // articles
        'articles' => 'article/index',
        'articles/<slug>' => 'article/route',
        'tag/<slug>' => 'article/tag',
        '<country>/articles' => 'article/study-abroad-index',
        '<country>/articles/<slug>' => 'article/study-abroad-detail',

        // ncert
        'ncert' => 'ncert/index',
        'ncert/<slug>' => 'ncert/route',

        //board in other language
        '<lang:(hi)>/boards' => 'board/index',
        '<lang:(hi)>/boards/<board>-<subject:(mathematics|physics|chemistry|biology|computer-science|economics|business-studies|accountancy|hindi|home-science|physical-education|history|geography|political- science|psychology|sanskrit|sociology|social-science|english)>-<page:(syllabus|notes|answer-key|question-paper|books|roll-number-finder|deleted-syllabus|previous-year-question-papers|exam-pattern|sample-papers)>' => 'board/detail',
        '<lang:(hi)>/boards/<board>-<subject:(science)>-<page:(syllabus|notes|answer-key|question-paper|books|roll-number-finder|deleted-syllabus|previous-year-question-papers|exam-pattern|sample-papers)>' => 'board/detail',

        '<lang:(hi)>/boards/<board>-<page:(supplementary)>-<subject:(time-table|date-sheet|admit-card|hall-ticket|result)>' => 'board/detail',
        '<lang:(hi)>/boards/<board>-<page:(time-table|hall-ticket|registration-form|results|syllabus|sample-papers|supplementary|admit-card|previous-year-question-papers|routine|application-form|registration-card|date-sheet|reference-books|exam-centres|marking-scheme|solved-question-papers|exam-pattern|preparation|answer-key|books|toppers|notes|question-paper|roll-number-finder)>' => 'board/detail',
        '<lang:(hi)>/boards/<boardSamplePaperSlug>/c' => 'board/sample-paper',
        '<lang:(hi)>/boards/<board>-<page:(reference-books)>' => 'board/detail',
        '<lang:(hi)>/boards/<board>-<page:(time-table|hall-ticket|registration-form|results|syllabus|sample-papers|supplementary|admit-card|previous-year-question-papers|routine|application-form|registration-card|date-sheet|exam-centres|marking-scheme|solved-question-papers|exam-pattern|preparation|answer-key|books|toppers|notes|question-paper|passing-marks|grading-system|marksheet|practical-exam-datesheet|roll-number-finder)>' => 'board/detail',
        '<lang:(hi)>/boards/<board>' => 'board/detail',

        // Boards base route
        'boards' => 'board/index',

        // Subject-specific pages (with content-specific pages like syllabus, notes, etc.)
        'boards/<board>-<subject:(mathematics|physics|chemistry|biology|computer-science|economics|business-studies|accountancy|hindi|home-science|physical-education|history|geography|political-science|psychology|sanskrit|sociology|social-science|english)>-<page:(syllabus|notes|answer-key|question-paper|books|roll-number-finder|deleted-syllabus|previous-year-question-papers|exam-pattern|sample-papers)>' => 'board/detail',

        'boards/<board>-<subject:(science)>-<page:(syllabus|notes|answer-key|question-paper|books|roll-number-finder|deleted-syllabus|previous-year-question-papers|exam-pattern|sample-papers)>' => 'board/detail',

        // Supplementary-specific nested pages
        'boards/<board>-<page:(supplementary)>-<subject:(time-table|date-sheet|admit-card|hall-ticket|result)>' => 'board/detail',

        // All general board pages (combined and deduplicated)
        'boards/<board>-<page:(time-table|hall-ticket|registration-form|results|deleted-syllabus|syllabus|sample-papers|supplementary|admit-card|previous-year-question-papers|routine|application-form|registration-card|date-sheet|reference-books|exam-centres|marking-scheme|solved-question-papers|exam-pattern|preparation|answer-key|books|toppers|notes|question-paper|roll-number-finder|passing-marks|grading-system|marksheet|practical-exam-datesheet)>' => 'board/detail',

        // Individual board page
        'boards/<board>' => 'board/detail',

        //course
        'courses' => 'course/index',
        'courses/<discipline>' => 'course/category',
        '<course>-<page:(syllabus-subjects|jobs-scope-salary|admission|specializations|fees|placements)>/<sub>' => 'course/detail',
        '<course>-<page:(syllabus-subjects|jobs-scope-salary|admission|specializations|fees|placements)>' => 'course/detail',
        '<course:course-[\w]+>-salary' => 'course/detail',
        '<course>-course' => 'course/detail',

        // college filter
        'all-colleges' => 'college/all-colleges',
        'all-colleges/<location>' => 'college/all-colleges',
        '<filter>-colleges' => 'college/college-filter',
        '<filter>-approved-colleges/india' => 'college/college-filter',
        '<filter>-colleges/<location>' => 'college/college-filter',
        'colleges-accepting-<filter>-score-in-india' => 'college/college-filter',
        'colleges-accepting-<filter>-score-in-<location>' => 'college/college-filter',
        'college/colleges-under-<filter>' => 'college/college-filter',
        'college/get-filter-data' => 'college/get-filter-data',
        'college/get-filter-data-college' => 'college/get-filter-data-college',
        'college/admissions' => 'college/admission-listing',
        'college/save-page-experience' => 'college/save-page-experience',


        //reviews
        'college/reviews' => 'review/index',
        'reviews/<reviewSlug>' => 'review/review-detail',

        //write-review
        'review/create' => 'writereview/index',
        'review/create/<reviewId>' => 'writereview/index',
        'review/create?<referralcode:\+d>' => 'writereview/index',

        //college-predictor
        'college-predictor' => 'college-predictor/index',

        //rank-predictor
        'rank-predictor' => 'college-predictor/rank-index',

        // college
        'college/<slug>-courses-fees/<courseSlug>/ci' => 'college/course',
        'college/<slug>-courses-fees/<programSlug>-pi' => 'college/program',
        'college/<slug>-courses-fees' => 'college/courses-fees',
        'college/<slug>-courses-fees/<courseProgramSlug>/<page:[\w]+>-<subpageSlug:[\w\-]+>-<year:\d+>-<id:\d+>' => 'college/courses-subpage-index',
        'college/<slug>-courses-fees/<courseProgramSlug>/<page>-<subpageSlug>-<id>' => 'college/courses-subpage-index',
        'college/<slug>-<page:(placements|facilities|scholarships|cut-off|result|images-videos|ranking|alumni|hostel|application-form|syllabus|verdict|timings|average-package|address|previous-year-pappers|admission-form|marksheet|nacc-grade|dress-code|refund|cgpa-to-percentage|fees|eligibility)>' => 'college/index',
        'college/<slug>/reviews?<page:\+d>' => 'college/review',
        'college/<slug>/reviews' => 'college/reviews',
        'college/<slug>/<page:(admission)>' => 'college/index',
        'college-compare' => 'college/compare-college',
        'program-fees-structure' => 'college/program-fees-structure',
        'course-fees-structure' => 'college/course-fees-structure',

        'college/<slug>-<type:(ug|pg)>-<page:(admission)>' => 'college/index',
        'college/<slug>-<page:(syllabus|cut-off)>/<type>' => 'college/index',
        'college/<slug>' => 'college/index',
        'college/<slug>/news' => 'college/news',

        //q-n-a
        'college/<slug>/questions' => 'college/qna-landing-page',
        'course/<slug>/questions' => 'course/qna-landing-page',
        'q-n-a/' => 'qna/qna-landing-page',
        'q-n-a/<slug>' => 'qna/qna-details',
        'exams/<slug>/questions' => 'exam/qna-landing-page',
        'boards/<slug>/questions' => 'board/qna-landing-page',

        // utils
        'qns-pdf' => 'ajax/qns-pdf',
        'get-primary-navigation' => 'ajax/primary-navigation',
        '<lang:(en|hi)>/get-secondary-navigation' => 'site/secondary-navigation',
        'get-secondary-navigation' => 'site/secondary-navigation',
        'lead-form-new' => 'site/lead-form',
        '<lang:(hi)>/lead-form' => 'site/lead-form',
        'lead-form' => 'site/lead-form',
        'sa-lead-form' => 'site/sa-lead-form',
        'news-lead-form' => 'site/news-lead-form',
        'comment-reply-form' => 'site/comment-reply-form',
        'contact-us' => 'site/contact-us',
        'contact-us-mail' => 'site/contact-us-mail',
        'user-profile' => 'user-profile/index',
        'banner-popup' => 'site/banner-popup',
        'privacy-policy' => 'site/privacy-policy',
        'about-us' => 'site/about-us',

        // sitemap
        // 'sitemap/<lang:(hi)>/article' => 'sitemap/article',
        // 'sitemap/<lang:(hi)>/news' => 'sitemap/news',
        // 'sitemap/<lang:(hi)>/google-news' => 'sitemap/google-news',
        // 'sitemap/<lang:(hi)>/boards' => 'sitemap/boards',

        'sitemap' => 'sitemap/index',
        // 'sitemap/user-review-sitemap/<page:\d+>' => 'sitemap/user-review-sitemap',
        'sitemap/listing/<page:\d+>' => 'sitemap/listing',
        // 'sitemap/<category>/<slug>/<page:\d+>' => 'sitemap/college-page',
        // 'sitemap/<category>/<slug>' => 'sitemap/college-page',

        // login signup
        'logout' => 'site/logout',
        'signup' => 'site/signup',
        'send-otp' => 'site/send-otp',

        //career
        'careers' => 'career/index',
        'careers/<career>-<page:(salary)>' => 'career/detail',
        'careers/<career:career-[\w]+>-salary' => 'career/detail',
        'careers/<career>' => 'career/detail',
        'careers/<slug>' => 'career/category',

        //scholarship-program
        'scholarship-program' => 'scholarship-program/index',

        //scholarship
        'scholarships' => 'scholarship/index',
        'scholarships/<scholarship>' => 'scholarship/detail',
        'scholarships/<scholarship:scholarship-[\w]+>-pivot' => 'scholarship/detail',
        'scholarships/<scholarship>' => 'scholarship/detail',
        'scholarships/<slug>' => 'scholarship/category',

        //olympiad
        'olympiad' => 'olympiad/index',
        'olympiad/<slug>-<page:(overview|awards|scholarships|admit-card|syllabus|registration-form|sample-papers|reference-books|results|preparation|career|exam-date|answer-sheets|answer-key)>' => 'olympiad/detail',
        'olympiad/<slug>' => 'olympiad/detail',

        //immigration
        'immigration' => 'immigration/index',
        'immigration/<slug:(pr-permanent-residence|job-seeker-visa)>' => 'immigration/category',
        'immigration/category' => 'immigration/category-page', // need to remove
        'immigration/get-testimonial' => 'immigration/get-testimonial',
        'immigration/get-chart-data' => 'immigration/get-chart-data',
        'immigration/pr-permanent-residence/<slug:(canada|australia)>' => 'immigration/country',
        'immigration/job-seeker-visa/<slug:(germany|austria|sweden|uae)>' => 'immigration/country',
        'immigration/canada/pr-eligibility-calculator' => 'calculator/pr-calculator',
        'immigration/canada/sinp-points-calculator' => 'calculator/saskatchewan-calculator',
        'immigration/canada/ielts-to-clb-converter' => 'calculator/clb-converter',
        'immigration/australia/pr-point-calculator' => 'calculator/australia-pr-points-calculator',
        'immigration/job-seeker-visa/germany-opportunity-card-calculator' => 'calculator/germany-opp-calculator',
        'immigration/canada/crs-calculator' => 'calculator/crs-calculator',

        //study-abroad
        '<countrySlug>' => 'study-abroad/index',
        '<countrySlug>/university/<collegeSlug>/<page:(admission|programs-tuition|ranking)>' => 'study-abroad/detail',
        '<countrySlug>/university/<collegeSlug>' => 'study-abroad/detail',

        // getgis articles
        'blog' => 'getgis-article/index',
        'blog/<slug>' => 'getgis-article/detail',

        'college-admissions/<slug>' => 'custom-landing-page/templates',

        'stream-chat/chat/<channelId>' => 'stream-chat/chat',
    ],
];

<?php

namespace frontend\controllers;

use Yii;
use yii\web\Controller;
use yii\web\Response;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;
use common\models\LiveChatGroup;
use common\services\GetStreamService;

/**
 * GetStream Integration Controller
 * Handles GetStream authentication, tokens, and real-time chat integration
 */
class GetStreamController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'get-user-token' => ['POST', 'GET'],
                    'connect-user' => ['POST'],
                    'join-channel' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Generate GetStream user token for authentication
     * This implements the connectUser() method mentioned in GetStream docs
     */
    public function actionGetUserToken()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $userId = Yii::$app->request->get('user_id') ?: Yii::$app->request->post('user_id');
            $userName = Yii::$app->request->get('user_name') ?: Yii::$app->request->post('user_name');
            $userEmail = Yii::$app->request->get('user_email') ?: Yii::$app->request->post('user_email');

            if (empty($userId) || empty($userName)) {
                return [
                    'success' => false,
                    'message' => 'User ID and name are required'
                ];
            }

            /** @var GetStreamService $getStreamService */
            $getStreamService = Yii::$app->getStreamService;

            // Create or update user in GetStream
            $userData = [
                'id' => $userId,
                'name' => $userName,
                'role' => 'user'
            ];

            if ($userEmail) {
                $userData['email'] = $userEmail;
            }

            // Upsert user in GetStream
            $getStreamService->upsertUser($userId, $userData);
            // Generate user token for authentication
            $token = $getStreamService->createUserToken($userId);

            return [
                'success' => true,
                'user_id' => $userId,
                'token' => $token,
                'api_key' => $getStreamService->apiKey, // Frontend needs this
                'message' => 'User token generated successfully'
            ];
        } catch (\Exception $e) {
            Yii::error('GetStream token generation failed: ' . $e->getMessage(), __METHOD__);
            return [
                'success' => false,
                'message' => 'Failed to generate user token: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Connect user to GetStream and join channel
     * This implements the channel joining process
     */
    public function actionJoinChannel()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $channelId = Yii::$app->request->post('channel_id');
            $userId = Yii::$app->request->post('user_id');
            $userName = Yii::$app->request->post('user_name');

            if (empty($channelId) || empty($userId)) {
                return [
                    'success' => false,
                    'message' => 'Channel ID and User ID are required'
                ];
            }

            /** @var GetStreamService $getStreamService */
            $getStreamService = Yii::$app->getStreamService;

            // Find the chat group
            $group = LiveChatGroup::findOne(['channel_id' => $channelId]);
            if (!$group) {
                return [
                    'success' => false,
                    'message' => 'Chat group not found'
                ];
            }

            // Add user to channel
            $result = $getStreamService->addUsersToChannel(
                $group->channel_type ?: 'livestream',
                $channelId,
                [$userId]
            );

            return [
                'success' => true,
                'channel_id' => $channelId,
                'user_id' => $userId,
                'channel_type' => $group->channel_type ?: 'livestream',
                'message' => 'Successfully joined channel'
            ];
        } catch (\Exception $e) {
            Yii::error('GetStream channel join failed: ' . $e->getMessage(), __METHOD__);
            return [
                'success' => false,
                'message' => 'Failed to join channel: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get channel information and recent messages
     */
    public function actionGetChannelInfo()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $channelId = Yii::$app->request->get('channel_id');

            if (empty($channelId)) {
                return [
                    'success' => false,
                    'message' => 'Channel ID is required'
                ];
            }

            // Find the chat group
            $group = LiveChatGroup::findOne(['channel_id' => $channelId]);
            if (!$group) {
                return [
                    'success' => false,
                    'message' => 'Chat group not found'
                ];
            }

            /** @var GetStreamService $getStreamService */
            $getStreamService = Yii::$app->getStreamService;

            // Get channel info and messages from GetStream
            $channelInfo = $getStreamService->getMessages(
                $group->channel_type ?: 'livestream',
                $channelId,
                ['limit' => 50]
            );

            return [
                'success' => true,
                'channel_id' => $channelId,
                'channel_type' => $group->channel_type ?: 'livestream',
                'group_name' => $group->group_name,
                'messages' => $channelInfo['messages'] ?? [],
                'channel' => $channelInfo['channel'] ?? []
            ];
        } catch (\Exception $e) {
            Yii::error('GetStream channel info failed: ' . $e->getMessage(), __METHOD__);
            return [
                'success' => false,
                'message' => 'Failed to get channel info: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Webhook endpoint for GetStream events
     * This handles real-time message synchronization
     */
    public function actionWebhook()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $payload = Yii::$app->request->getRawBody();
            $data = json_decode($payload, true);

            if (!$data) {
                return ['success' => false, 'message' => 'Invalid payload'];
            }

            /** @var GetStreamService $getStreamService */
            $getStreamService = Yii::$app->getStreamService;

            // Handle different event types
            $eventType = $data['type'] ?? '';

            switch ($eventType) {
                case 'message.new':
                    // Sync new message to database
                    $message = $getStreamService->syncMessage($data);
                    if ($message) {
                        Yii::info('Message synced from GetStream: ' . $message->id, __METHOD__);
                    }
                    break;

                case 'user.presence.changed':
                    // Handle user presence changes
                    Yii::info('User presence changed: ' . json_encode($data), __METHOD__);
                    break;

                default:
                    Yii::info('Unhandled GetStream event: ' . $eventType, __METHOD__);
            }

            return ['success' => true, 'message' => 'Webhook processed'];
        } catch (\Exception $e) {
            Yii::error('GetStream webhook error: ' . $e->getMessage(), __METHOD__);
            return [
                'success' => false,
                'message' => 'Webhook processing failed'
            ];
        }
    }
}

<?php

namespace frontend\controllers;

use common\models\Degree;
use common\models\Exam;
use common\models\LiveChatUserStatus;
use common\models\StudentActivity;
use common\models\StudentPreference;
use common\services\LeadService;
use common\services\S3Service;
use common\helpers\DataHelper;
use common\models\LiveChatUserMessage;
use common\models\Student;
use Exception;
use Yii;
use yii\web\Controller;
use ZipArchive;

class LiveStreamChatUserController extends Controller
{

    public function __construct(
        $id,
        $module,
        $config = []
    ) {
        parent::__construct($id, $module, $config);
    }

    /**
     * Save lead from live chat widget
     */
    public function actionSaveLead()
    {
        Yii::$app->response->format = 'json';

        if (!Yii::$app->request->isAjax) {
            return ['success' => false, 'message' => 'Invalid request'];
        }

        $request  = Yii::$app->request->post();
        $userCity = DataHelper::getUserLocation();

        $student = Student::findOne(['phone' => $request['phone'] ?? null]);

        if (!$student) {
            $student = new Student([
                'phone'          => $request['phone'] ?? '',
                'name'           => $request['name'] ?? '',
                'email'          => $request['email'] ?? '',
                'source_url'     => $request['url'] ?? '',
                'source'         => DataHelper::$leadSource['organic'],
            ]);
        } else {
            // Update only name/email for existing students
            $student->name  = $request['name'] ?? $student->name;
            $student->email = $request['email'] ?? $student->email;
        }

        // Reset city/state always
        $student->current_city     = null;
        $student->current_state    = null;
        $student->current_city_ip  = $userCity['cityId']  ?? null;
        $student->current_state_ip = $userCity['stateId'] ?? null;

        if ($student->save()) {
            $this->updateStudentActivity($request, $student);
        }

        return ['success' => true, 'message' => 'Lead saved successfully', 'student_id' => $student->id];
    }

    /**
     * Update student activity + preference
     */
    public function updateStudentActivity(array $request, Student $student): void
    {
        $defaults     = DataHelper::$utmDefaultMedium[$request['entity']] ?? ['medium' => '', 'source' => ''];
        $studentActivity = new StudentActivity([
            'student_id'       => $student->id,
            'parent_id'        => null,
            'url'              => $request['url'] ?? $student->source_url,
            'cta_position'     => $request['type'] ?? '',
            'cta_text'         => 'Live Chat Widget',
            'platform'         => $request['platform'] ?? '',
            'utm_source'       => $defaults['source'],
            'utm_medium'       => $defaults['medium'],
            'utm_campaign'     => null,
            'source'           => DataHelper::$leadSource['organic'],
            'category'         => $request['entity_type'] ?? '',
            'category_sub_page' => $request['entity_subtype'] ?? '',
        ]);

        if (!$studentActivity->save()) {
            return;
        }

        $course = $parentId = $courseId = null;
        if (in_array($request['entity'], ['course', 'course_stream'], true)) {
            $course = (new LeadService)->getCourseData($request['entity_id']);
            if ($course) {
                $parentId = $course->parent_id ?? $course->id;
                $courseId = $course->id;
            }
        }

        // Safe degree lookup
        $degreeId = null;
        if (!empty($course['degree'])) {
            $degree = Degree::find()->select('id')->where(['like', 'slug', $course['degree']])->one();
            $degreeId = $degree['id'] ?? null;
        }

        $studentPreference = new StudentPreference([
            'activity_id'             => $studentActivity->id,
            'student_id'              => $student->id,
            'level'                   => (int)($request['level'] ?? 0) ?: null,
            'stream'                  => (int)($request['stream'] ?? 0) ?: null,
            'course'                  => $parentId ?: '',
            'child_course_id'         => $courseId ?: '',
            'program_id'              => null,
            'degree'                  => $degreeId,
            'highest_qualification'   => $course['highest_qualification'] ?? null,
            'user_highest_qualification' => null,
            'exam'                    => ($request['entity'] === Exam::ENTITY_EXAM && !empty($request['entity_id']))
                ? $request['entity_id']
                : null,
        ]);
        $studentPreference->save();
    }

    public function actionSaveChatMessage()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return ['success' => false, 'message' => 'Invalid request'];
        }

        try {
            $request = Yii::$app->request;

            $channelId = $request->post('channel_id');
            $messageText = $request->post('message_text');
            $messageType = $request->post('type', 'text');
            $userName = $request->post('user_name');
            $userEmail = $request->post('user_email');
            $streamUserId = $request->post('user_id');
            $streamMessageId = $request->post('stream_message_id');

            if (empty($channelId) || empty($userName)) {
                return ['success' => false, 'message' => 'Missing required fields'];
            }

            $groupId = Yii::$app->db->createCommand(
                'SELECT id FROM live_chat_group WHERE channel_id = :channel_id'
            )->bindValue(':channel_id', $channelId)->queryScalar();

            if (!$groupId) {
                return ['success' => false, 'message' => "No chat group found for channel: {$channelId}"];
            }

            // File upload processing
            $uploadedFiles = [];
            $now = date('Y-m-d H:i:s');

            if (!empty($_FILES['files'])) {
                $s3 = new S3Service();
                $tempNames = $request->post('tempNames', []);
                $types = $request->post('types', []);
                foreach ($_FILES['files']['tmp_name'] as $i => $tmpName) {
                    if (empty($tmpName) || !file_exists($tmpName)) {
                        continue;
                    }

                    $originalName = $_FILES['files']['name'][$i];
                    $fileSize = $_FILES['files']['size'][$i];
                    $type = $types[$i] ?? $_FILES['files']['type'][$i];
                    $tempName = $tempNames[$i] ?? $originalName;

                    // Basic ZIP validation
                    if (in_array($type, ['application/zip', 'application/x-zip-compressed'])) {
                        $zip = new ZipArchive();
                        if ($zip->open($tmpName) !== true) {
                            continue; // Skip invalid ZIP
                        }
                        $zip->close();
                    }

                    // Upload to S3
                    $s3Path = DataHelper::s3Path($tempName, 'live_chat');

                    if (empty($s3Path)) {
                        continue;
                    }

                    $uploadResult = $s3->uploadFile($s3Path, $tmpName);
                    if (!$uploadResult) {
                        continue;
                    }

                    $bucketBaseUrl = 'https://media.getmyuni.com/';
                    $uploadedFiles[] = [
                        'name' => $originalName,
                        'type' => $type,
                        'url' => $bucketBaseUrl . ltrim($s3Path, '/'),
                        'size' => $fileSize,
                        'temp_name' => $tempName
                    ];
                }
            }

            // Save to database
            Yii::$app->db->createCommand(
                'INSERT INTO live_chat_user_message
                (group_id, user_id, message_id, message_text, message_type, attachments, user_name, user_email, is_admin_message, is_deleted, created_at, updated_at)
             VALUES (:group_id, :user_id, :message_id, :message_text, :message_type, :attachments, :user_name, :user_email, 0, 0, :created_at, :updated_at)'
            )->bindValues([
                ':group_id' => $groupId,
                ':message_id' => $streamMessageId,
                ':message_text' => $messageText ?? '',
                ':message_type' => $messageType,
                ':attachments' => json_encode($uploadedFiles, JSON_UNESCAPED_SLASHES),
                ':user_name' => $userName,
                ':user_id' => $streamUserId,
                ':user_email' => $userEmail,
                ':created_at' => $now,
                ':updated_at' => $now
            ])->execute();

            return [
                'success' => true,
                'message' => 'Message saved successfully',
                'uploadedFiles' => $uploadedFiles,
                'file_count' => count($uploadedFiles)
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to save message: ' . $e->getMessage()
            ];
        }
    }

    public function actionLoginLiveUser()
    {
        Yii::$app->response->format = 'json';
        $request = Yii::$app->request->post();

        if (!Yii::$app->request->isAjax) {
            return false;
        }

        $student = Student::findByPhone($request['mobile']);

        if (!empty($student)) {
            Yii::$app->user->login($student, 3600 * 24 * 30);
            return [
                'success' => true,
            ];
        } else {
            return [
                'success' => false,
            ];
        }
    }

    /**
     * NEW: Simplified online status update (replaces complex heartbeat system)
     */
    /**
     * Update user status with last seen tracking
     */
    public function actionUpdateUserStatus()
    {
        Yii::$app->response->format = 'json';

        $contentType = Yii::$app->request->getContentType();
        if (strpos($contentType, 'application/json') !== false) {
            $request = json_decode(Yii::$app->request->getRawBody(), true);
        } else {
            $request = Yii::$app->request->post();
        }

        $userId = $request['user_id'] ?? null;
        $channelId = $request['channel_id'] ?? null;
        $onlineStatus = (int)($request['online_status'] ?? 0);
        $timestamp = isset($request['timestamp']) ? date('Y-m-d H:i:s', $request['timestamp'] / 1000) : date('Y-m-d H:i:s');

        if (!$userId || !$channelId) {
            return ['success' => false, 'error' => 'Missing required parameters'];
        }

        try {
            $userStatus = LiveChatUserStatus::find()
                ->where(['user_id' => $userId, 'channel_id' => $channelId])
                ->one();

            if (!$userStatus) {
                $userStatus = new LiveChatUserStatus([
                    'user_id' => $userId,
                    'channel_id' => $channelId,
                    'online_status' => $onlineStatus,
                    'last_seen_at' => $timestamp, // Set initial last seen
                    'created_at' => $timestamp,
                    'updated_at' => $timestamp,
                ]);
            } else {
                // Store last seen when user goes offline
                if ($onlineStatus == 0) {
                    $userStatus->last_seen_at = $timestamp;
                }

                $userStatus->online_status = $onlineStatus;
                $userStatus->updated_at = $timestamp;
            }

            if ($userStatus->save()) {
                return [
                    'success' => true,
                    'status' => $onlineStatus ? 'online' : 'offline',
                    'last_seen_at' => $userStatus->last_seen_at,
                    'user_id' => $userId,
                    'channel_id' => $channelId,
                    'timestamp' => $timestamp
                ];
            } else {
                return ['success' => false, 'errors' => $userStatus->getErrors()];
            }
        } catch (Exception $e) {
            print_r($e->getMessage());
            return ['success' => false, 'error' => 'Database error'];
        }
    }

    /**
     * Get user's last seen time
     */
    public function actionGetLastSeen()
    {
        Yii::$app->response->format = 'json';

        $userId = Yii::$app->request->get('user_id');
        $channelId = Yii::$app->request->get('channel_id');

        if (!$userId || !$channelId) {
            return ['success' => false, 'error' => 'Missing required parameters'];
        }

        try {
            $userStatus = LiveChatUserStatus::find()
                ->where(['user_id' => $userId, 'channel_id' => $channelId])
                ->one();

            return [
                'success' => true,
                'last_seen_at' => $userStatus ? $userStatus->last_seen_at : null,
                'user_id' => $userId,
                'channel_id' => $channelId
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Database error'];
        }
    }

    /**
     * Get online users count
     */
    public function actionGetOnlineUsers()
    {
        Yii::$app->response->format = 'json';

        $channelId = Yii::$app->request->get('channel_id');

        if (!$channelId) {
            return ['success' => false, 'error' => 'Channel ID required'];
        }

        try {
            // Simply count users marked as online
            $onlineUsers = LiveChatUserStatus::find()
                ->select(['student.name as name', 'user_id'])
                ->where(['channel_id' => $channelId])
                ->andWhere(['online_status' => LiveChatUserStatus::STATUS_ONLINE])
                ->innerJoin('student', 'student.id = user_id') //student is alias of user table
                ->asArray()
                ->all();

            return [
                'success' => true,
                'online_count' => empty($onlineUsers) ? '' : count($onlineUsers),
                'online_user_names' => empty($onlineUsers) ? [] : $onlineUsers,
                'channel_id' => $channelId,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        } catch (Exception $e) {
            Yii::error('Error getting online users: ' . $e->getMessage(), 'chat-presence');
            return ['success' => false, 'error' => 'Database error'];
        }
    }

    /**
     * Get total message count for a channel
     */
    public function actionGetAllMessageCount()
    {
        Yii::$app->response->format = 'json';

        $channelId = Yii::$app->request->get('channel_id');

        if (!$channelId) {
            return ['success' => false, 'error' => 'Channel ID required'];
        }

        try {
            $messageCount = (new \yii\db\Query())
                ->from('live_chat_user_message um')
                ->innerJoin('live_chat_group g', 'g.id = um.group_id')
                ->where(['g.channel_id' => $channelId])
                ->andWhere(['>=', 'um.created_at', date('Y-m-d H:i:s', strtotime('-30 minutes'))])
                ->count('*', Yii::$app->db);

            return [
                'success' => true,
                'message_count' => $messageCount,
                'channel_id' => $channelId,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        } catch (Exception $e) {
            Yii::error('Error getting message count: ' . $e->getMessage(), 'chat-presence');
            return ['success' => false, 'error' => 'Database error'];
        }
    }
}

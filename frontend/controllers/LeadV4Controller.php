<?php

namespace frontend\controllers;

use common\helpers\DataHelper;
use common\models\City;
use common\models\Lead;
use common\models\Student;
use common\models\StudentActivity;
use common\models\StudentOtp;
use common\services\CollegeService;
use common\services\SmsService;
use common\services\UserService;
use common\models\College as CollegeSQL;
use common\models\CtaThankYou;
use common\models\Degree;
use common\models\Exam;
use common\models\LeadBucket;
use common\models\LeadEducationBudget;
use common\models\LeadGmailDomain;
use common\models\SpecializationNew;
use common\models\StudentAcademicDetials;
use common\models\StudentCollegeShortlist;
use common\models\StudentPreference;
use common\models\StudentPreferencePreferredLocation;
use common\models\StudentPreferenceSpecialization;
use common\services\ExamService;
use common\services\LeadService;
use frontend\helpers\Util;
use Yii;
use yii\web\Controller;
use frontend\models\LeadForm;
use yii\db\Query;
use yii\helpers\Json;
use yii\web\Response;

class LeadV4Controller extends Controller
{
    protected $collegeService;
    protected $examService;
    protected $leadService;
    const CATEGORY = 'lead';

    public function __construct(
        $id,
        $module,
        CollegeService $collegeService,
        ExamService $examService,
        LeadService $leadService,
        $config = []
    ) {
        $this->collegeService = $collegeService;
        $this->examService = $examService;
        $this->leadService = $leadService;
        parent::__construct($id, $module, $config);
    }

    /**
     * Undocumented function
     *
     * @return void
     */
    public function actionScreenOne()
    {
        if (!Yii::$app->request->isAjax) {
            return false;
        }

        Yii::$app->response->format = 'json';
        $request = Yii::$app->request->post();

        $leadEmail = LeadService::getCorrectedEmailDomain($request);

        $lead = Lead::find()->where(['id' => $request['lead_id']])->one();

        if (!empty($request['activity_id']) && !empty($request['lead_id']) && !empty($request['student_id']) && !empty($request['preference_id']) && !empty($lead) && isset($request['phone']) && $request['phone'] == $lead['mobile']) {
            self::updateLeadStudentData($request);
        }

        $modelStudent = new Student();
        $modelStudent->phone = $request['phone'] ?? '';
        $modelStudent->name = $request['name'] ?? '';
        $modelStudent->email = $leadEmail ?? '';
        $modelStudent->current_city = empty($request['current_city']) ? null : $request['current_city'];
        $modelStudent->current_state = empty($request['current_city']) ? null : (new UserService)->getCityId('', $request['current_city'])['stateId'];
        $modelStudent->source_url = $request['url'] ?? '';
        $modelStudent->source = DataHelper::$leadSource['organic'];
        $modelStudent->current_city_ip = !empty($request['current_city_ip']) ? $request['current_city_ip'] : null;
        $modelStudent->current_state_ip = !empty($request['current_state_ip']) ? $request['current_state_ip'] : null;

        if (!$modelStudent->validate() || empty($request['inputStream']) || empty($request['inputLevel'])) {
            foreach ($modelStudent->errors as $key => $error) {
                $errors[$key] = $error[0];
            }
            if (empty($request['inputStream'])) {
                $errors['inputStream'] = 'Stream cannot be blank.';
            }

            if (empty($request['inputLevel'])) {
                $errors['inputLevel'] = 'Level cannot be blank.';
            }

            $response = [
                'success' => false,
                'message' => $errors ?? 'Something went wrong, please try again!',
            ];
            return $response;
        } else {
            $lead = Lead::find()->select(['is_mobile_verified'])->where(['mobile' => $request['phone']])->andWhere(['is_mobile_verified' => 1])->one();
            $student = Student::find()->where(['phone' => $request['phone']])->one();

            if (empty($student)) {
                if ($modelStudent->save()) {
                    self::updateLead($request, $modelStudent, !$lead ? 0 : 1);
                }
            } else {
                $student->name = $request['name'];
                $student->email = $leadEmail;
                $student->current_city = empty($request['current_city']) ? null : $request['current_city'];
                $student->current_state = !empty($request['current_city']) ? (new UserService)->getCityId('', $request['current_city'])['stateId'] : '';
                $student->current_city_ip = !empty($request['current_city_ip']) ? $request['current_city_ip'] : null;
                $student->current_state_ip = !empty($request['current_state_ip']) ? $request['current_state_ip'] : null;

                if ($student->save()) {
                    self::updateLead($request, $student, !$lead ? 0 : 1);
                }
            }
        }
    }

    /**
     * Undocumented function
     *
     * @return void
     */
    public function actionScreenTwo()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        if (!Yii::$app->request->isAjax) {
            return false;
        }

        $studentPreferenceModel = StudentPreference::find()->where(['id' => $request['preference_id']])->one();
        // $studentDetail = Student::find()->where(['id' => $studentPreferenceModel->student_id])->one();
        $studentActivity = StudentActivity::find()->where(['id' => $studentPreferenceModel->activity_id])->one();

        if (!empty($studentPreferenceModel)) {
            $studentPreferenceModel->education_budget = !empty($request['budget']) ? $request['budget'] : '';
            $studentPreferenceModel->admission = !empty($request['admissionYearSelection']) ? (int) $request['admissionYearSelection'] : '';
            $studentPreferenceModel->distance_education = !empty($request['distanceEducation']) ? (int) $request['distanceEducation'] : StudentPreference::IS_DISTANCE_NO;
            $studentPreferenceModel->is_study_abroad = !empty($request['studyAbroad']) ? (int) $request['studyAbroad'] : StudentPreference::IS_STUDY_ABROAD_NO;
            $studentPreferenceModel->save();
        }

        if (!empty($request['inputSpecialization'])) {
            foreach ($request['inputSpecialization'] as $id) {
                $studentPreferenceSpecialization = StudentPreferenceSpecialization::find()
                    ->where(['student_preference_id' => (int) $request['preference_id']])
                    ->andWhere(['specialization_id' => (int) $id])
                    ->one();

                if (empty($studentPreferenceSpecialization)) {
                    $studentPreferenceSpecialization = new StudentPreferenceSpecialization();
                }

                $studentPreferenceSpecialization->student_preference_id = (int) $request['preference_id'];
                $studentPreferenceSpecialization->specialization_id = $id ?? '';
                if ($studentPreferenceSpecialization->save()) {
                } else {
                    print_r($studentPreferenceSpecialization->getErrors());
                }
            }
        }

        if (!empty($request['college_location'])) {
            foreach ($request['college_location'] as $id) {
                $stateId = empty($id) ? null : (new UserService)->getCityId('', $id);
                $studentPreferenceCollegeLocation = StudentPreferencePreferredLocation::find()
                    ->where(['student_preference_id' => (int) $request['preference_id']])
                    ->andWhere(['preferred_city_id' => (int) $id])
                    ->one();

                if (empty($studentPreferenceCollegeLocation)) {
                    $studentPreferenceCollegeLocation = new StudentPreferencePreferredLocation();
                }

                $studentPreferenceCollegeLocation->student_preference_id = (int) $request['preference_id'];
                $studentPreferenceCollegeLocation->preferred_city_id = $id ?? '';
                $studentPreferenceCollegeLocation->preferred_state_id =  $stateId['stateId'] ?? '';
                if ($studentPreferenceCollegeLocation->save()) {
                } else {
                    print_r($studentPreferenceCollegeLocation->getErrors());
                }
            }
        }

        $sponsorColleges = $this->collegeService->sponsorCollege($request);
        $checkSponsoredCollege = !empty($request['entity']) && !empty($request['entity_id']) && ($request['entity'] == CollegeSQL::ENTITY_COLLEGE || $request['entity'] == CollegeSQL::ENTITY_COLLEGE_LISTING) ? $this->collegeService->getSponsoredStatus($request['entity_id']) : CollegeSQL::SPONSORED_NO;
        $result = [];

        if (!empty($sponsorColleges['colleges']) && $checkSponsoredCollege == CollegeSQL::SPONSORED_NO) {
            if (!empty($sponsorColleges['colleges'])) {
                $result = $this->renderPartial('/lead/lead_v2/_sponsor_college', [
                    'colleges' => Json::decode(Json::encode($sponsorColleges['colleges']), false),
                    'streamName' => $sponsorColleges['stream_name']
                ]);
            }
        }

        $response = [
            'success' => true,
            'sponsorCollege' => $result ?? []
        ];

        echo json_encode($response);
        exit;
    }

    /**
     * Undocumented function
     *
     * @return void
     */
    public function actionScreenThree()
    {
        $success = true;
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        if (empty($request) || empty($request['college_id'])) {
            return $success;
        }
        foreach (array_unique($request['college_id']) as $value) {
            $model = StudentCollegeShortlist::find()
                ->where(['student_id' => $request['student_id']])
                ->andWhere(['activity_id' => $request['activity_id']])
                ->andWhere(['college_id' => $value])
                ->one();
            if (empty($model)) {
                $model = new StudentCollegeShortlist();
            }

            $model->student_id = $request['student_id'];
            $model->activity_id = $request['activity_id'];
            $model->college_id = $value;
            $model->sponsored = 1;

            if ($model->save()) {
                $success = true;
            } else {
                $success = false;
            }
        }

        $response = [
            'success' => $success
        ];

        echo json_encode($response);
        exit;
    }

    public function updateLeadStudentData($request)
    {
        if ($request['entity'] == 'college-listing' || $request['entity'] == 'exam' || $request['page_name'] == 'ci' || $request['page_name'] == 'program' || $request['page_name'] == 'courses-fees') {
            $course = !empty($request['course_id']) ? (new LeadService)->getCourseData($request['course_id']) : '';
            if (!empty($course)) {
                $courseId = $course->id;
            }
        } elseif ($request['entity'] == 'course' || $request['entity'] == 'course_stream') {
            $course = !empty($request['course_id']) ? (new LeadService)->getCourseData($request['course_id']) : '';
            if (!empty($course)) {
                $courseId = $course->id;
            }
        }

        $qualificationValue = empty($courseId) ? '' : LeadService::getCourseData($courseId);
        $qualification = !empty($qualificationValue) && !empty($qualificationValue['highest_qualification']) ?  $qualificationValue['highest_qualification'] : '';
        $lead = Lead::find()->where(['id' => $request['lead_id']])->one();

        $leadEmail = LeadService::getCorrectedEmailDomain($request);

        $lead->name = $request['name'] ?? '';
        $lead->email = $leadEmail ?? '';
        $lead->mobile = $request['phone'] ?? '';
        $lead->interested_course = $courseId ?? '';
        $lead->qualification = empty($qualification) ? '' : array_search($qualification, DataHelper::$highestQualificationIdMapping);
        $lead->current_location = $request['current_city'] ?? '';
        $lead->specialization_id = $qualificationValue['specialization_id']  ?? '';
        $lead->save();

        $student = Student::find()->where(['id' => $request['student_id']])->one();
        $student->name = $request['name'] ?? '';
        $student->email = $leadEmail ?? '';
        $student->current_city = $request['current_city'] ?? '';
        $student->current_state = !empty($request['current_city']) ? (new UserService)->getCityId('', $request['current_city'])['stateId'] : '';
        $student->source_url = $request['url'];
        $student->source = empty($request['source'] ? 0 : $request['source']);

        if ($student->save()) {
            self::updateStudentActivity($request, $student, 1, $request['lead_id']);
        } else {
            print_r($student->getErrors());
        }
    }

    public function updateLead($request, $student, $is_mobile_verified)
    {
        if ($request['entity'] == 'college-listing' || $request['entity'] == 'exam' || $request['page_name'] == 'ci' || $request['page_name'] == 'program' || $request['page_name'] == 'courses-fees') {
            $course = !empty($request['course_id']) ? (new LeadService)->getCourseData($request['course_id']) : '';
            if (!empty($course)) {
                $courseId = $course->id;
            }
        } elseif ($request['entity'] == 'course' || $request['entity'] == 'course_stream') {
            $course = !empty($request['course_id']) ? (new LeadService)->getCourseData($request['course_id']) : '';
            if (!empty($course)) {
                $courseId = $course->id;
            }
        }

        $leadEmail = LeadService::getCorrectedEmailDomain($request);
        $qualificationValue = empty($courseId) ? '' : LeadService::getCourseData($courseId);
        $qualification = !empty($qualificationValue) && !empty($qualificationValue['highest_qualification']) ?  $qualificationValue['highest_qualification'] : '';

        $modelLead = new LeadForm();
        $modelLead->name = $request['name'] ?? '';
        $modelLead->user_id = $student->id;
        $modelLead->email = $leadEmail ?? '';
        $modelLead->mobile = $request['phone'] ?? '';
        $modelLead->entity = $request['entity'] ?? '';
        $modelLead->entity_id = $request['entity_id'] ?? '';
        $modelLead->interested_course = $courseId ?? '';
        $modelLead->qualification = empty($qualification) ? '' : array_search($qualification, DataHelper::$highestQualificationIdMapping);
        $modelLead->current_location = empty($request['current_city']) ? (!empty($request['current_city_ip']) ? $request['current_city_ip'] : null) : $request['current_city'];
        $modelLead->interested_location = empty($request['interested_location']) ? null : $request['interested_location'];
        $modelLead->specialization_id = $qualificationValue['specialization_id'] ?? '';
        $modelLead->utm_campaign = $request['utm_campaign'] ?? '';
        $modelLead->utm_source = $request['utm_source'] ?? '';
        $modelLead->utm_medium = $request['utm_medium'] ?? '';
        $modelLead->cta_location = $request['cta_location'] ?? '';
        $modelLead->cta_text = strip_tags($request['cta_text']) ?? '';
        $modelLead->url = $request['url'] ?? '';
        $modelLead->platform = $request['platform'] ?? '';
        $modelLead->is_mobile_verified = $is_mobile_verified;

        if ($modelLead->save()) {
            self::updateStudentActivity($request, $student, $is_mobile_verified, $modelLead->id);
        } else {
            print_r($modelLead->getErrors());
        }
    }

    public function updateStudentActivity($request, $student, $is_mobile_verified, $lead_id = '')
    {
        $studentActivity = null;
        $course = null;
        $utm_source = '';
        $utm_medium = '';

        if ($request['student_id'] == $student->id && (!empty($request['activity_id']) && !Yii::$app->user->isGuest || isset($request['verified']) && $request['verified'] == '1')) {
            $studentActivity = StudentActivity::find()->where(['id' => $request['activity_id']])->one();
        }

        if (empty($studentActivity)) {
            $studentActivity = new StudentActivity();
        }
        // $autoFetchScreenTwoData = self::getScreenTwoDetails($request);

        if (empty($request['utm_medium'])) {
            $utm_medium = isset(DataHelper::$utmDefaultMedium[$request['entity']]) ? DataHelper::$utmDefaultMedium[$request['entity']]['medium'] : '';
        } else {
            $utm_medium = $request['utm_medium'];
        }

        if (empty($request['utm_source'])) {
            $utm_source = isset(DataHelper::$utmDefaultMedium[$request['entity']]) ? DataHelper::$utmDefaultMedium[$request['entity']]['source'] : '';
        } else {
            $utm_source = $request['utm_source'];
        }

        $studentActivity->student_id = $student->id;
        if (!empty($request['student_activity_parent_id']) && $request['student_activity_parent_id'] != 'null') {
            $studentActivity->parent_id = $request['student_activity_parent_id'];
        }
        $studentActivity->url = $request['url'] ?? $student->source_url;
        $studentActivity->cta_position = $request['cta_location'] ?? '';
        if ($studentActivity->cta_position == 'auto-popup') {
            $studentActivity->cta_text = 'Auto Pop Up';
        } else {
            $studentActivity->cta_text = strip_tags($request['cta_text']);
        }

        $studentActivity->platform = $request['platform'] ?? '';
        $studentActivity->utm_source = $utm_source;
        $studentActivity->utm_medium = $utm_medium;
        $studentActivity->utm_campaign = $request['utm_campaign'] ?? ($request['utm_campaign'] ?? '');
        $studentActivity->source = empty($request) || empty($request['source']) ? 0 : $request['source'];
        $studentActivity->category = $request['entity_type'] ?? '';
        $studentActivity->category_sub_page = $request['entity_subtype'] ?? '';

        if ($studentActivity->save()) {
            $studentPreference = StudentPreference::find()->where(['id' => $request['preference_id']])
                ->andWhere(['activity_id' => $studentActivity->id])
                ->one();

            if (!$studentPreference) {
                $studentPreference = new StudentPreference();
            }

            if ($request['entity'] == 'college-listing' || $request['entity'] == 'college' || $request['entity'] == 'exam' || $request['page_name'] == 'ci' || $request['page_name'] == 'program' || $request['page_name'] == 'courses-fees') {
                $course = (new LeadService)->getCourseData($request['course_id']);
                if (!empty($course)) {
                    $parent_id = $course->parent_id ?? $course->id;
                    $courseId = $course->id;
                }
                $programId = $request['program_id'] ?? '';
                $degreeId = !empty($request['degree_id']) ? $request['degree_id'] : '';
            } elseif ($request['entity'] == 'course' || $request['entity'] == 'course_stream') {
                $course = (new LeadService)->getCourseData($request['entity_id']);
                if (!empty($course)) {
                    $parent_id = $course->parent_id ?? $course->id;
                    $courseId = $course->id;
                }
            }

            $degree = empty($course) && empty($course['degree']) ? null : Degree::find()->select('id')->where(['like', 'slug', $course['degree']])->one();
            $degreeId = !empty($degree) ? $degree['id'] : '';

            $studentPreference->activity_id = $studentActivity->id ?? '';
            $studentPreference->student_id = $studentActivity->student_id;
            $studentPreference->level = empty($request['inputLevel']) ? null : (int) $request['inputLevel'] ?? null;
            $studentPreference->stream = empty($request['inputStream']) ? null : (int) $request['inputStream'] ?? null;
            $studentPreference->course = empty($parent_id) ? '' : $parent_id;
            $studentPreference->child_course_id = empty($courseId) ? '' : $courseId;
            $studentPreference->program_id = empty($programId) ? '' : $programId;
            $studentPreference->degree = empty($degreeId) ? null : $degreeId;
            $studentPreference->highest_qualification = !empty($course) && !empty($course['highest_qualification']) ? $course['highest_qualification'] : null;
            $studentPreference->user_highest_qualification =  null;

            if (!empty($request) && $request['entity'] == Exam::ENTITY_EXAM) {
                $studentPreference->exam = (!empty($request) && !empty($request['entity_id'])) ? $request['entity_id'] : null;
            } else if (isset($request['product_mapping_entity']) && !empty($request['product_mapping_entity']) && $request['product_mapping_entity'] == 'exams') {
                $studentPreference->exam = (!empty($request) && !empty($request['product_mapping_entity_id'])) ? $request['product_mapping_entity_id'] : null;
            } elseif (!empty($request) && $request['entity'] == CollegeSQL::ENTITY_COLLEGE_LISTING) {
                $studentPreference->exam = !empty($request['college_filter_exam']) ? $request['college_filter_exam'] : '';
            } else {
                $studentPreference->exam = null;
            }

            if ($studentPreference->save()) {
                if (!empty($request) && $request['entity'] == CollegeSQL::ENTITY_COLLEGE || $request['entity'] == 'all-colleges' || $request['entity'] == 'college-listing' || (isset($request['product_mapping_entity']) && $request['product_mapping_entity'] == 'colleges')) {
                    $studentCollegeShortlist = null;
                    if (!empty($request['student_college_shortlist_id'])) {
                        $studentCollegeShortlist = StudentCollegeShortlist::find()->where(['id' => $request['student_college_shortlist_id']])->one();
                    }

                    if (empty($studentCollegeShortlist)) {
                        $studentCollegeShortlist = new StudentCollegeShortlist();
                    }

                    $studentCollegeShortlist->student_id = $studentPreference->student_id;
                    $studentCollegeShortlist->activity_id = $studentPreference->activity_id;
                    $studentCollegeShortlist->college_id = (isset($request['product_mapping_entity_id']) && $request['product_mapping_entity_id']) ? $request['product_mapping_entity_id'] : ($request['entity_id'] ?? null);
                    $studentCollegeShortlist->sponsored = empty($is_sponsorCollege) ? 0 : 1;
                    $studentCollegeShortlist->save();
                }
            } else {
                print_r($studentPreference->getErrors());
                throw new \Exception('Validation failed: ' . json_encode($studentPreference->getErrors()));
            }

            $budgetBasedOnstreamLevel = self::getBudget($request);

            $response = [
                'success' => true,
                'courseId' => $courseId ?? '',
                'highest_qualification' => $studentPreference->highest_qualification ?? '',
                'specialization' => $studentPreference->specialization ?? '',
                'source_url' => $student->source_url ?? '',
                'source' => $student->source ?? '',
                'user_status' => $student->user_status ?? '',
                'user_type' => $student->user_type,
                'lead_id' => $lead_id ?? '',
                'user_id' => $student->id ?? null,
                'entity' => $request['entity'],
                'entity_id' => $request['entity_id'],
                'cta_location' => $request['cta_location'],
                'student_id' => $studentActivity->student_id,
                'student_activity_id' => $studentActivity->id,
                'student_prefrence_id' => $studentPreference->id,
                'is_mobile_verified' => $is_mobile_verified,
                'numberChange' => !empty($request['phone']) && !empty(Yii::$app->user->identity) ? ($request['phone'] == Yii::$app->user->identity->phone ? 0 : 1) : 1,
                'nameChange' => !empty($request['name']) && !empty(Yii::$app->user->identity) ? ($request['name'] == Yii::$app->user->identity->name ? 0 : 1) : 1,
                // 'screenTwo' => $autoFetchScreenTwoData,
                'budget' => $budgetBasedOnstreamLevel,
            ];

            echo json_encode($response);
            exit;
        } else {
            print_r($studentActivity->getErrors());
            throw new \Exception('Validation failed: ' . json_encode($studentActivity->getErrors()));
        }
    }

    public function actionVerifyOtp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();

        if (strlen($request['otp']) < 4) {
            return ['success' => false, 'message' => 'Otp Field required'];
        }

        $lead = Lead::find()
            ->where(['mobile' => Util::cleanMobile($request['mobile'])])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        $studentOtp = StudentOtp::find()->andWhere(['student_id' => $request['student_id']])->orderBy(['id' => SORT_DESC])->one();

        if (!$studentOtp || ($studentOtp->otp !== (int) $request['otp'])) {
            $errors['inputOTP'] = 'Please enter the valid otp';
            return ['success' => false, 'message' => $errors];
        }

        if (!empty($lead)) {
            $lead->otp = $request['otp'];
            $lead->is_mobile_verified = Lead::MOBILE_VERIFIED_YES;
            $lead->save();

            if ($lead->cta_location == 'auto-popup' || $lead->cta_location == 'all-colleges') {
                $entity = '';
            }
        }

        $studentOtp->otp_status = StudentOtp::STATUS_OTP_USED;
        $studentOtp->save();
        $entity = $lead->entity ?? '';

        $is_sponsorCollege = false;
        if (isset($request['entity_id'])) {
            if ($entity == CollegeSQL::ENTITY_COLLEGE) {
                $is_sponsorCollege = CollegeSQL::find()
                    ->where(['id' => $request['entity_id'], 'is_sponsored' => 1])
                    ->exists();
            }
        }

        $student = Student::findByPhone($request['mobile']);

        if (!empty($student)) {
            $student->is_mobile_verified = Student::IS_MOBILE_VERIFIED_YES;
            $student->save();
        }

        $sponsorColleges = isset($request->sponsorColleges) ? true : false;

        return [
            'success' => true,
            'studentName' => $student->name ?? '',
            'studentEmail' => $student->email ?? '',
            'entity' => $entity ?? '',
            'model' => $sponsorColleges,
            'message' => 'Thank you for submitting the form.',
            'interested_course' => !empty($lead) && isset($lead->interested_course) ? $lead->interested_course : '',
            'cta_location' => !empty($lead) && isset($lead->cta_location) ? $lead->cta_location : '',
            'is_sponsorCollege' => $is_sponsorCollege ?? [],
        ];
    }

    public function getScreenTwoDetails($request)
    {
        $query = new Query();
        $query->select(['sps.specialization_id as id', 's.name', 'sppl.preferred_city_id', 'c.name as cityName'])
            ->from([StudentActivity::tableName() . ' as sa'])
            ->leftJoin(StudentPreference::tableName() . ' as sp', 'sp.activity_id = sa.id')
            ->leftJoin(StudentPreferenceSpecialization::tableName() . ' as sps', 'sps.student_preference_id = sp.id')
            ->leftJoin(SpecializationNew::tableName() . ' as s', 's.id = sps.specialization_id')
            ->leftJoin(StudentPreferencePreferredLocation::tableName() . ' as sppl', 'sppl.student_preference_id=sp.id')
            ->leftJoin(City::tableName() . ' as c', 'c.id = sppl.preferred_city_id')
            ->where(['sp.student_id' => $request['student_id']])
            ->andWhere(['sa.category' => $request['entity']])
            ->andWhere(['not', ['sps.specialization_id' => null]])
            ->groupBy(['sps.specialization_id', 'sppl.preferred_city_id'])
            ->orderBy(['sa.id' => SORT_DESC]);

        $studentPreferenceScreenTwo = $query->all();

        $studentPreference = StudentPreference::find()
            ->select(['admission', 'education_budget'])
            ->where(['student_id' => $request['student_id']])
            ->andWhere(['not', ['admission' => null]])
            ->andWhere(['not', ['education_budget' => null]])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        return [
            'studentPreferenceScreenTwo' => $studentPreferenceScreenTwo,
            'student_preference' => [
                'admission' => !empty($studentPreference) ? $studentPreference->admission : '',
                'education_budget' => !empty($studentPreference) ? $studentPreference->education_budget : '',
            ]
        ];
    }

    public function getStateByCityId($cityId = '')
    {
        $result = [
            'stateName' => '',
            'stateId' => null
        ];

        if (empty($cityId)) {
            return $result;
        }

        $query = new Query();
        $query->select(['state.name', 'state.id', 'c.state_id'])->from(['state'])
            ->leftJoin('city as c', 'c.state_id = state.id')
            ->where(['c.id' => $cityId]);

        $city = $query->one();

        return [
            'stateId' => $city['id'],
            'stateName' => $city['name'],
        ];
    }

    public function actionResendOtpLead()
    {
        Yii::$app->response->format = 'json';
        $request = Yii::$app->request->post();

        if (!Yii::$app->request->isAjax) {
            return false;
        }

        $model = StudentOtp::find()->where(['student_id' => $request['student_id']])->orderBy(['id' => SORT_DESC])->one();

        if (!$model) {
            return ['success' => false];
        }
        $model->otp = rand(1000, 9999);

        if ($model->save()) {
            SmsService::sendOtp($request['phone'], $model->otp);

            return ['success' => true];
        }

        return ['success' => false];
    }

    public function actionSendOtpLead()
    {
        if (Yii::$app->request->isAjax) {
            Yii::$app->response->format = 'json';
            $studentData = Yii::$app->request->post();
            if (!isset($studentData['ctaCheck'])) {
                $studentData['ctaCheck'] = 0;
            }
            $lead = Lead::find()->select(['is_mobile_verified'])->where(['mobile' => $studentData['phone']])->andWhere(['is_mobile_verified' => 1])->one();
            if (isset($studentData['is_lead']) && !empty($lead) && !empty(Yii::$app->user->identity) && $studentData['phone'] == Yii::$app->user->identity->phone && $studentData['ctaCheck'] == 0) {
                $response['success'] = true;
                $response['is_registered_user'] = true;
                $response['numberChange'] = 0;
                echo json_encode($response);
                exit;
            }

            //Validate Phone Number
            if (empty($studentData['phone']) or !preg_match('/^[6-9][0-9]{9}$/', $studentData['phone'])) {
                return [
                    'success' => false,
                    'message' => 'Invalid Phone Number!',
                ];
            }

            $student = Student::find()->where(['phone' => $studentData['phone']])->one();


            if (empty($student) || !isset($studentData['is_lead'])) {
                return [
                    'success' => false,
                    'message' => 'Phone Number is not registered!',
                ];
            }

            $studentOtp = new StudentOtp;
            $studentOtp->student_id = $student->id;
            $studentOtp->otp = rand(1356, 8675);
            SmsService::sendOtp($student->phone, $studentOtp->otp);
            $studentOtp->save();
            return [
                'success' => true,
                'message' => '',
                'numberChange' => (!empty($studentData) && !empty(Yii::$app->user->identity)) ? ($studentData['phone'] == Yii::$app->user->identity->phone ? 0 : 1) : 1,
            ];
        }
    }

    public function actionStudentSessionActivate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();

        if (empty($request['activity_id'])) {
            return [
                'success' => false
            ];
        }
        $student = Student::findIdentity($request['student_id']);
        if (!empty($student)) {
            Yii::$app->user->login($student, 3600 * 24 * 30);

            return [
                'success' => true
            ];
        } else {
            return [
                'success' => false
            ];
        }
    }

    public function getBudget($request)
    {
        $item = [];
        $budgets = LeadEducationBudget::find()
            ->select(['display_name', 'id'])
            ->where(['stream_id' => $request['inputStream']])
            ->andWhere(['degree_id' => $request['inputLevel']])
            ->all();

        if (empty($budgets)) {
            return $item;
        }

        foreach ($budgets as $key => $budget) {
            $item[] = [
                'id' => $budget->id,
                'name' => $budget->display_name
            ];
        }

        $budgetHtml =  $this->renderPartial(
            '/lead/lead_v2/partials/_lead_budget',
            [
                'data' => $item
            ]
        );
        return $budgetHtml;
    }

    public function actionThankYouMessage()
    {
        Yii::$app->response->format = 'json';
        $request = Yii::$app->request->post();

        $entityId = isset(DataHelper::$ctEntityIdMapping[$request['entity']]) ? DataHelper::$ctEntityIdMapping[$request['entity']] : '';
        $entityMapping = $request['entity'] == 'article' ? LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE : $entityId;

        // Define the replacement mappings
        $replacements = [
            '<span class="spriteIcon applyRedIcon"></span>' => '{list}',
            '<span class="spriteIcon whiteDownloadIcon redDownloadIcon"></span>' => '{download}',
            '<span class="spriteIcon applyWhiteIconCta"></span>' => '{white_list}'
        ];

        if (isset($request['cta_text'])) {
            $original_cta_text = $request['cta_text'];
            $cta_text = str_replace(array_keys($replacements), array_values($replacements), $original_cta_text);

            // If no replacements were made, keep the original text
            if ($cta_text === $original_cta_text) {
                $cta_text = $original_cta_text;
            }

            if ($cta_text == date('Y') . ' Timetable {download}') {
                $cta_text = '{current year} Timetable {download}';
            }
        } else {
            $cta_text = '';
        }

        $thankYouModel = CtaThankYou::find()
            ->where(['cta_text' => $cta_text])
            ->andWhere(['entity_type' => $entityMapping])
            ->one();

        if (!empty($thankYouModel) && !empty($thankYouModel->thank_you_text)) {
            return [
                'success' => true,
                'thankYouMessage' => $thankYouModel->thank_you_text,
            ];
        } else {
            return [
                'success' => false,
                'thankYouMessage' => '',
                'cta_text' => $cta_text,
                'entity_id' => $entityMapping
            ];
        }
    }

    public function actionLoginLiveUser()
    {
        Yii::$app->response->format = 'json';
        $request = Yii::$app->request->post();

        if (!Yii::$app->request->isAjax) {
            return false;
        }

        $student = Student::findByPhone($request['mobile']);

        if (!empty($student)) {
            Yii::$app->user->login($student, 3600 * 24 * 30);
            return [
                'success' => true,
            ];
        } else {
            return [
                'success' => false,
            ];
        }
    }
}

<?php

namespace frontend\controllers;

use common\helpers\DataHelper;
use common\models\Article;
use common\models\College;
use common\models\Course;
use common\models\Degree;
use common\models\Exam;
use common\models\ExamContent;
use common\models\Lead;
use common\models\News;
use common\models\NewsContent;
use common\models\Stream;
use common\models\StudentActivity;
use common\models\StudentOtp;
use common\models\StudentPreference;
use common\models\User;
use common\services\CollegeService;
use common\services\SmsService;
use common\services\StudentService;
use common\services\UserService;
use frontend\controllers\Controller;
use frontend\models\ContactForm;
use frontend\models\PasswordResetRequestForm;
use frontend\models\ResendVerificationEmailForm;
use frontend\models\ResetPasswordForm;
use frontend\models\Student;
use frontend\models\VerifyEmailForm;
use Yii;
use yii\base\InvalidArgumentException;
use yii\data\ActiveDataProvider;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use frontend\helpers\Url;
use common\models\UserTranslation;
use common\services\HomeService;
use common\services\LeadService;
use common\services\S3Service;
use frontend\services\ArticleService;
use yii\widgets\ListView;
use common\services\WebEngage;
use yii\caching\TagDependency;

/**
 * Site controller
 */
class SiteController extends Controller
{
    protected $homeService;
    protected $articleService;

    public function __construct(
        $id,
        $module,
        HomeService $homeService,
        ArticleService $articleService,
        $config = []
    ) {
        $this->homeService = $homeService;
        $this->articleService = $articleService;
        parent::__construct($id, $module, $config);
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'only' => ['logout', 'send-otp', 'login'],
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'actions' => ['send-otp', 'login'],
                        'allow' => true,
                        'roles' => ['?'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'logout' => ['post'],
                    'send-otp' => ['post'],
                ],
            ],
        ];
    }

    public function beforeAction($action)
    {
        if (in_array($action->id, ['logout', 'send-otp'])) {
            if (!Yii::$app->request->isPost) {
                throw new NotFoundHttpException('Page not found.');
            }
        }
        return parent::beforeAction($action);
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
        ];
    }

    /**
     * Displays homepage.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $funArguments = func_get_args();
        $funArguments['pageType'] = 'site-index';
        $funArguments['bodyParams'] = Yii::$app->request->bodyParams;

        $key = $this->getHash($funArguments);
        // yii::$app->cache->flush();
        $data = Yii::$app->cache->getOrSet($key, function () {
            return [
                'trendingToday' => $this->homeService->getTrendingPageData('others', null, false, 10),
                'homeSlides' => $this->homeService->homepageNewSlides(),
                'trendingCollegeCities' => $this->homeService->getTrendingCollegeCities(),
                'trendingExams' => $this->homeService->getTrendingPageData('exam', null, false, 20),
                'trendingSubpageExams' => $this->homeService->getTrendingPageData('exam', null, true, 18),
                'trendingCourses' => $this->homeService->getTrendingPageData('course', null, false, 20),
                'trendingSubpageCourses' => $this->homeService->getTrendingPageData('course', null, true, 18),
                'trendingBoards' => $this->homeService->getTrendingPageData('board', null, false, 20),
                'trendingSubpageBoards' => $this->homeService->getTrendingPageData('board', null, true, 18),
                'popularColleges' => $this->homeService->getPopularColleges(),
                'collegeCountbyStream' => $this->homeService->getCollegeCountbyStream(),
                'latestArticles' => $this->homeService->getLatestArticles(10),
                'sponsoredColleges' => $this->homeService->getSponsoredColleges(),
                'topOlympiads' => $this->homeService->getTrendingPageData('olympiad', null, false, 20),
                'courseCountbyStream' => $this->homeService->getCourseCountbyStream(),
                'examCountbyStream' => $this->homeService->getExamCountbyStream(),
                'latestNews' => $this->homeService->getLatestNews(),
                'stats' => [
                    'totalRatings' => $this->homeService->getRatingStats(),
                    'totalReviews' => $this->homeService->geRreviewStats(),
                    'totalStudents' => $this->homeService->getStudentStats(),
                    'totalColleges' => $this->homeService->getCollegeStats(),
                    'totalQuestions' => $this->homeService->getQuestionCount(),
                    'total_competitions' => 50
                ],
                'homePageArticleCategory' => $this->articleService->postsByStreams()
            ];
        }, 60 * 60 * 3, new TagDependency(['tags' => DataHelper::generateCacheKey()]));

        return $this->render('index', $data);
    }

    /**
     * Logs in a user.
     *
     * @return mixed
     */
    public function actionLogin()
    {
        if (!\Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        if (Yii::$app->request->isAjax) {
            Yii::$app->response->format = 'json';
            $loginData = Yii::$app->request->post();

            if (empty($loginData['phone']) or empty($loginData['otp'])) {
                return [
                    'success' => false,
                    'message' => 'Phone Number and OTP is required for Login',
                ];
            }

            $student = Student::findByPhone($loginData['phone']);

            if ($student == null) {
                return [
                    'success' => false,
                    'message' => 'Phone Number is not registered.',
                ];
            } else {
                $studentOtp = new StudentOtp;
                $existingOtp = $studentOtp->getStudentOtp($student->id);

                if (empty($existingOtp) or $existingOtp->otp != $loginData['otp']) {
                    sleep(1);
                    return [
                        'success' => false,
                        'message' => 'Invalid OTP supplied.',
                    ];
                }
                $student->is_mobile_verified = Student::IS_MOBILE_VERIFIED_YES;
                $student->save();
                if (Yii::$app->user->login($student, 3600 * 24 * 30)) {
                    WebEngage::pushEventLoginLogout('login', 'yes', $loginData['phone']);
                    $existingOtp->otp_status = $studentOtp::STATUS_OTP_USED;
                    $student->source_url = (Url::base(true) . Url::current());
                    StudentService::logActivity($student, [], 'student-login');
                    $existingOtp->save();
                    return [
                        'success' => true,
                        'message' => 'Login Successfull!',
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Something went wrong, please try again!',
                    ];
                }
            }
        }

        return $this->renderAjax('student-login');
    }

    public function actionLoginPopup()
    {
        return $this->renderAjax('student-login-popup');
    }

    /**
     * Logs out the current user.
     *
     * @return mixed
     */
    public function actionLogout()
    {
        $student =  Student::find()->select(['phone'])->where(['id' => Yii::$app->user->id])->one();
        WebEngage::pushEventLoginLogout('logout', 'yes', $student->phone);
        Yii::$app->user->logout();
        if (str_contains($_SERVER['HTTP_REFERER'], '/user-profile') == true) {
            return $this->redirect('/site/login');
        }

        return $this->redirect($_SERVER['HTTP_REFERER'] ?? '/');
    }

    /**
     * Logs out the current user.
     *
     * @return mixed
     */
    public function actionSaLogout()
    {
        Yii::$app->saUser->logout();

        return $this->redirect($_SERVER['HTTP_REFERER'] ?? '/');
    }

    /**
     * Displays contact page.
     *
     * @return mixed
     */
    public function actionContact()
    {
        $model = new ContactForm();
        if ($model->load(Yii::$app->request->post()) && $model->validate()) {
            if ($model->sendEmail(Yii::$app->params['adminEmail'])) {
                Yii::$app->session->setFlash('success', 'Thank you for contacting us. We will respond to you as soon as possible.');
            } else {
                Yii::$app->session->setFlash('error', 'There was an error sending your message.');
            }

            return $this->refresh();
        } else {
            return $this->render('contact', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Displays about page.
     *
     * @return mixed
     */
    public function actionAbout()
    {
        return $this->render('about');
    }

    /**
     * Signs user up.
     *
     * @return mixed
     */
    public function actionSignup()
    {
        if (Yii::$app->request->isAjax) {
            Yii::$app->response->format = 'json';
            $formData = Yii::$app->request->post();
            $leadEmail = LeadService::getCorrectedEmailDomain($formData);

            if (isset($formData['entity']) && $formData['entity'] == 'news') {
                $isLead = Lead::find()->select(['is_mobile_verified'])
                    ->where(['mobile' => $formData['phone']])
                    ->orderBy(['id' => SORT_DESC])
                    ->one();

                $loggedIn = Student::findByPhone($formData['phone']);
                $nameChange = empty($loggedIn) ? 0 : ($loggedIn->name !== $formData['name'] ? 1 : 0);
            }

            $existingStudent = Student::findByPhone($formData['phone']);

            if (!empty($existingStudent) && !isset($formData['entity'])) {
                return [
                    'success' => false,
                    'message' => [
                        'phone' => 'This phone number is already registered.'
                    ]
                ];
            }
            $student = new Student;
            $student->phone = $formData['phone'] ?? '';
            $student->name = $formData['name'] ?? '';
            $student->email = $leadEmail ?? '';
            $student->current_city = !empty($formData['current_city']) ? $formData['current_city'] : '';
            $student->current_state = !empty($formData['current_city']) ? (new UserService)->getCityId('', $formData['current_city'])['stateId'] : '';
            $student->user_type = Student::USER_TYPE_NORMAL;
            $student->current_city_ip = !empty($formData['current_city_ip']) ? $formData['current_city_ip'] : null;
            $student->current_state_ip = !empty($formData['current_state_ip']) ? $formData['current_state_ip'] : null;

            if ($formData['source'] != null) {
                $student->source = $formData['source'];
            } else {
                $student->source = DataHelper::$leadSource['organic'];
            }

            if (isset($formData['entity']) && $formData['entity'] !== 'news' && !$student->validate()) {
                foreach ($student->errors as $key => $error) {
                    $errors[$key] = $error[0];
                }
                if (empty($formData['stream'])) {
                    $errors['stream'] = 'Interested stream cannot be blank.';
                }

                if (empty($formData['level'])) {
                    $errors['level'] = 'level cannot be blank.';
                }

                $response = [
                    'success' => false,
                    'message' => $errors ?? 'Something went wrong, please try again!',
                ];
            } elseif (!empty($formData['phone']) && strlen($formData['phone']) !== 10) {
                $errors['phone'] = 'Invalid Phone Number.';
                $response = [
                    'success' => false,
                    'message' => $errors ?? 'Something went wrong, please try again!',
                ];
            } elseif (isset($formData['entity']) && $formData['entity'] == 'news' && empty($formData['stream']) || empty($formData['level']) || empty($formData['name']) || empty($formData['email'] || empty($formData['phone']))) {
                if (empty($formData['stream'])) {
                    $errors['stream'] = 'Interested stream cannot be blank.';
                }

                if (empty($formData['level'])) {
                    $errors['level'] = 'level cannot be blank.';
                }

                if (empty($formData['name'])) {
                    $errors['name'] = 'Name cannot be blank.';
                }

                if (empty($formData['email'])) {
                    $errors['email'] = 'Email cannot be blank.';
                }

                if (empty($formData['phone'])) {
                    $errors['phone'] = 'Phone cannot be blank.';
                }
                $response = [
                    'success' => false,
                    'message' => $errors ?? 'Something went wrong, please try again!',
                ];
            } else {
                if (!empty($loggedIn)) {
                    $loggedIn->name = $formData['name'] ?? '';
                    $loggedIn->email = $leadEmail ?? '';
                    $loggedIn->current_city = $formData['current_city'] ?? null;
                    $loggedIn->current_state = !empty($formData['current_city']) ? (new UserService)->getCityId('', $formData['current_city'])['stateId'] : '';
                    $loggedIn->current_city_ip = !empty($formData['current_city_ip']) ? $formData['current_city_ip'] : null;
                    $loggedIn->current_state_ip = !empty($formData['current_state_ip']) ? $formData['current_state_ip'] : null;
                    $loggedIn->save();
                } else {
                    $student->save();
                }
                // StudentService::logActivity($student, $formData, $formData['entity'] == 'news' ? 'news' : 'student-signup');
                StudentService::logActivity((!empty($loggedIn) ? $loggedIn : $student), $formData, Student::STUDENT_CATEGORY);
                $response = [
                    'success' => true,
                    'isLead' => empty($isLead) && $existingStudent ? 1 : (!empty($isLead) ? $isLead['is_mobile_verified'] : 1),
                    'loggedIn' => isset($loggedIn) && !empty($loggedIn) ? 1 : 0,
                    'nameChange' => $nameChange ?? 0,
                    'numberChange' => !empty(Yii::$app->user->identity) && (Yii::$app->user->identity->phone == $formData['phone']) ? 0 : 1,
                    'message' => 'Account registered, verify OTP to Log In.',
                    'sessionStart' => empty(Yii::$app->user->identity) ? 1 : 0,
                ];
            }

            if (isset($formData['entity']) && $formData['entity'] == 'news') {
                $highestEducationByCourseId = empty($formData['course']) ? null : Course::find()->select(['highest_qualification'])->where(['id' => $formData['course']])->one();

                $model = new Lead();

                $model->name = $formData['name'] ?? '';
                $model->user_id = $student->id ?? ($loggedIn->id ?? '');
                $model->email = $leadEmail ?? '';
                $model->mobile = $formData['phone'] ?? '';
                $model->entity = $formData['entity'] ?? 0;
                $model->entity_id = $formData['entity_id'] ?? '';
                $model->interested_course = $formData['course'] ?? '';
                $model->qualification = empty($highestEducationByCourseId) && empty($highestEducationByCourseId['highest_qualification']) ? null : array_search($highestEducationByCourseId['highest_qualification'], DataHelper::$highestQualificationIdMapping) ?? '';
                $model->current_location = $formData['current_city'] ?? '';
                $model->cta_location = $formData['cta_location'];
                $model->cta_text = strip_tags($formData['cta_text']) ?? '';
                $model->url = $formData['url'] ?? '';
                $model->platform = $formData['platform'];
                $model->is_mobile_verified = empty($isLead) && $existingStudent ? 1 : (!empty(Yii::$app->user->identity) && (Yii::$app->user->identity->phone == $formData['phone']) ? 1 : 0);

                if ($model->save()) {
                    $response = [
                        'success' => true,
                        'isLead' => empty($isLead) && $existingStudent ? 1 : (!empty($isLead) ? $isLead['is_mobile_verified'] : 0),
                        'loggedIn' => isset($loggedIn) && !empty($loggedIn) ? 1 : 0,
                        'nameChange' => $nameChange ?? 0,
                        'message' => 'Lead Account registered, verify OTP to Log In.',
                        'numberChange' => !empty(Yii::$app->user->identity) && (Yii::$app->user->identity->phone == $formData['phone']) ? 0 : 1,
                        'sessionStart' => $model->is_mobile_verified == 0 ? 1 : 0,
                    ];

                    if (!empty(Yii::$app->user->identity) && (Yii::$app->user->identity->phone == $formData['phone'])) {
                        Yii::$app->user->login(!empty($loggedIn) ? $loggedIn : $student, 3600 * 24 * 30);
                    }
                } else {
                    // print_r($model->getErrors());
                }
            }

            return $response;
        } else {
            return $this->goHome();
        }
    }

    public function actionVerifyOtpNews()
    {
        if (Yii::$app->request->isAjax) {
            Yii::$app->response->format = 'json';
            $loginData = Yii::$app->request->post();

            $lead = Lead::find()->where(['mobile' => $loginData['phone']])->orderBy(['id' => SORT_DESC])->one();

            if ($lead) {
                $lead->is_mobile_verified = 1;
                $lead->save();
            }

            if (empty($loginData['phone']) or empty($loginData['otp'])) {
                return [
                    'success' => false,
                    'message' => 'Phone Number and OTP is required for Login',
                ];
            }

            $student = Student::findByPhone($loginData['phone']);

            if ($student == null) {
                return [
                    'success' => false,
                    'message' => 'Phone Number is not registered.',
                ];
            } else {
                $studentOtp = new StudentOtp;
                $existingOtp = $studentOtp->getStudentOtp($student->id);
                if (empty($existingOtp) or $existingOtp->otp != $loginData['otp']) {
                    sleep(1);
                    return [
                        'success' => false,
                        'message' => 'Invalid OTP supplied.',
                    ];
                }
                $student->is_mobile_verified = Student::IS_MOBILE_VERIFIED_YES;
                $student->save();
                Yii::$app->user->login($student, 3600 * 24 * 30);
                return [
                    'success' => true,
                    'message' => 'OTP supplied.',
                ];
            }
        }
    }

    public function actionLogActivity()
    {
        if (Yii::$app->request->isAjax) {
            Yii::$app->response->format = 'json';
            $formData = Yii::$app->request->post();
            $utm_source = '';
            $utm_medium = '';

            if (!Yii::$app->user->isGuest) {
                if (empty($request['utm_medium'])) {
                    $utm_medium = isset(DataHelper::$utmDefaultMedium[$formData['entity']]) ? DataHelper::$utmDefaultMedium[$formData['entity']]['medium'] : '';
                } else {
                    $utm_medium = $formData['utm_medium'];
                }

                if (empty($formData['utm_source'])) {
                    $utm_source = isset(DataHelper::$utmDefaultMedium[$formData['entity']]) ? DataHelper::$utmDefaultMedium[$formData['entity']]['source'] : '';
                } else {
                    $utm_source = $formData['utm_source'];
                }

                $url = !empty($formData['page_url']) ? $formData['page_url'] : 'https://getmyuni.com';
                $studentActivity = new StudentActivity();
                $studentActivity->student_id = Yii::$app->user->identity->id;
                if (!empty($formData['parent_activity_id']) && $formData['parent_activity_id'] != 'null') {
                    $studentActivity->parent_id = $formData['parent_activity_id'];
                }
                $studentActivity->url = $url;
                $studentActivity->category = $formData['entity_type'] ?? '';
                $studentActivity->category_sub_page = $formData['entity_subtype'] ?? '';
                $studentActivity->cta_position = 'log-activity';
                $studentActivity->cta_text = 'Log Activity';
                $studentActivity->utm_source = $utm_source ?? '';
                $studentActivity->utm_medium = $utm_medium ?? '';
                $studentActivity->utm_campaign = $formData['utm_campaign'] ?? '';
                $studentActivity->save();
                return $studentActivity->id;
            }
        }
    }

    /**
     * API endpoint to check user is logged in or not
     *
     * @return mixed
     */

    public function actionLoggedIn()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        // WebEngage::pushEventLoginLogout('login','yes');

        if (!Yii::$app->user->isGuest) {
            Yii::$app->response->statusCode = 200;
            $studentName = explode(' ', Yii::$app->user->identity->name)[0] ?? '';
            Yii::$app->user->identity->name = $studentName;
            Yii::$app->response->data = ['success' => true, 'data' => Yii::$app->user->identity];
        } else {
            Yii::$app->response->statusCode = 401;
            Yii::$app->response->data = ['sucess' => false, 'error' => 'You are not logged in!'];
        }
        return Yii::$app->response;
    }

    /**
     * API endpoint to check user is logged in or not
     *
     * @return mixed
     */

    public function actionSaLoggedIn()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        if (!Yii::$app->saUser->isGuest) {
            Yii::$app->response->statusCode = 200;
            $studentName = explode(' ', Yii::$app->saUser->identity->name)[0] ?? '';
            Yii::$app->saUser->identity->name = $studentName;
            Yii::$app->response->data = ['success' => true, 'data' => Yii::$app->saUser->identity];
        } else {
            Yii::$app->response->statusCode = 401;
            Yii::$app->response->data = ['sucess' => false, 'error' => 'You are not logged in!'];
        }
        return Yii::$app->response;
    }

    /**
     * Requests password reset.
     *
     * @return mixed
     */
    public function actionRequestPasswordReset()
    {
        $model = new PasswordResetRequestForm();
        if ($model->load(Yii::$app->request->post()) && $model->validate()) {
            if ($model->sendEmail()) {
                Yii::$app->session->setFlash('success', 'Check your email for further instructions.');

                return $this->goHome();
            } else {
                Yii::$app->session->setFlash('error', 'Sorry, we are unable to reset password for the provided email address.');
            }
        }

        return $this->render('requestPasswordResetToken', [
            'model' => $model,
        ]);
    }

    /**
     * Resets password.
     *
     * @param string $token
     * @return mixed
     * @throws BadRequestHttpException
     */
    public function actionResetPassword($token)
    {
        try {
            $model = new ResetPasswordForm($token);
        } catch (InvalidArgumentException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        if ($model->load(Yii::$app->request->post()) && $model->validate() && $model->resetPassword()) {
            Yii::$app->session->setFlash('success', 'New password saved.');

            return $this->goHome();
        }

        return $this->render('resetPassword', [
            'model' => $model,
        ]);
    }

    /**
     * Verify email address
     *
     * @param string $token
     * @throws BadRequestHttpException
     * @return yii\web\Response
     */
    public function actionVerifyEmail($token)
    {
        try {
            $model = new VerifyEmailForm($token);
        } catch (InvalidArgumentException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        if ($user = $model->verifyEmail()) {
            if (Yii::$app->user->login($user)) {
                Yii::$app->session->setFlash('success', 'Your email has been confirmed!');
                return $this->goHome();
            }
        }

        Yii::$app->session->setFlash('error', 'Sorry, we are unable to verify your account with provided token.');
        return $this->goHome();
    }

    /**
     * Resend verification email
     *
     * @return mixed
     */
    public function actionResendVerificationEmail()
    {
        $model = new ResendVerificationEmailForm();
        if ($model->load(Yii::$app->request->post()) && $model->validate()) {
            if ($model->sendEmail()) {
                Yii::$app->session->setFlash('success', 'Check your email for further instructions.');
                return $this->goHome();
            }
            Yii::$app->session->setFlash('error', 'Sorry, we are unable to resend verification email for the provided email address.');
        }

        return $this->render('resendVerificationEmail', [
            'model' => $model,
        ]);
    }

    public function actionAuthorProfile($slug, $categoryId = 27)
    {

        $lang_code = DataHelper::getLangId();
        $categories = [
            'exams',
            'articles',
            'news',
        ];
        $postRequest = Yii::$app->request->post();

        $user = User::find()
            ->with(['profile'])
            ->where(['slug' => $slug])
            //->andWhere(['status' => User::STATUS_ACTIVE])
            ->one();
        if (empty($user) || empty($user->profile->about)) {
            throw new NotFoundHttpException();
        }

        if (!empty($postRequest) && isset($postRequest['isScroll']) && $postRequest['isScroll'] == true) {
            if (!empty($postRequest['tab']) && $postRequest['tab'] == 'articles') {
                $data = $this->getNewArticle($user->id, $categoryId, $lang_code, $postRequest['offset']);
                $file = '/partials/_authorArticleCard';
            }
            if (!empty($postRequest['tab']) && $postRequest['tab'] == 'exams') {
                $data = $this->getExamArticle($user->entity_id, $postRequest['offset']);
                $file = '/partials/_authorExamCard';
            }
            if (!empty($postRequest['tab']) && $postRequest['tab'] == 'news') {
                $data = $this->getNewsArticle($user->id, $lang_code, $postRequest['offset']);
                $file = '/partials/_authorNewsCard';
            }
            if (empty($data)) {
                return false;
            }
            $data->prepare();
            $data->pagination  = false;
            $count = $data->getCount();
            if (!$count) {
                \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
                $record['count'] = 0;
                return $record;
            }
            return ListView::widget([
                'dataProvider' =>  $data,
                'itemView' => $file,
                'layout' => "\n{items}\n{pager}",
                'id'           => false,
                // 'class' => false,
                'itemOptions' => ['tag' => null, 'class' => null, 'id' => null]
                // 'options' => ['class' => false]
            ]);
        }
        $user_translation = UserTranslation::find()->select(['user_name', 'about', 'job_role'])->where(['tag_user_id' => $user->id])->andWhere(['lang_code' => $lang_code])->one();
        if (!empty($user_translation)) {
            $user->name = $user_translation->user_name;
            $user->profile->name = $user_translation->user_name;
            $user->profile->about = $user_translation->about;
            $user->profile->job_role = $user_translation->job_role;
        }

        // news
        $query = NewsContent::find()
            ->innerJoin(News::tableName(), NewsContent::tableName() . '.news_id = ' . News::tableName() . '.id')
            ->where([NewsContent::tableName() . '.author_id' => $user->id])
            ->andWhere([News::tableName() . '.lang_code' => $lang_code])
            ->andWhere([NewsContent::tableName() . '.status' => NewsContent::STATUS_ACTIVE])
            ->andWhere([News::tableName() . '.status' => News::STATUS_ACTIVE])
            ->orderBy([NewsContent::tableName() . '.created_at' => SORT_DESC]);

        $news = new ActiveDataProvider([
            'query' => $query,
            'pagination' => ['pageSize' => 20],
        ]);
        $news->prepare();

        // exams
        $query = ExamContent::find()
            ->where(['author_id' => $user->id])
            //            ->andWhere(['<>', 'author_id', null])
            ->andWhere(['status' => ExamContent::STATUS_ACTIVE])
            ->with('exam')
            ->groupBy('exam_id');

        $exams = new ActiveDataProvider([
            'query' => $query,
            'pagination' => ['pageSize' => 20],
        ]);
        if (!empty($user->entity_id)) {
            $exams->prepare();
        }
        // articles
        $articles = Article::find()->where(['author_id' => $user->id])->andWhere(['lang_code' => $lang_code])->andWhere(['not', ['category_id' => $categoryId]])->active();
        $articles = new ActiveDataProvider([
            'query' => $articles,
            'pagination' => ['pageSize' => 20],
        ]);
        $articles->prepare();

        foreach ($categories as $key => $value) {
            if ($value == 'exams' && $exams->pagination->totalCount < 1) {
                unset($categories[$key]);
            } else if ($value == 'articles' && $articles->pagination->totalCount < 1) {
                unset($categories[$key]);
            } else if ($value == 'news' && $news->pagination->totalCount < 1) {
                unset($categories[$key]);
            }
        }

        // if (empty($categories)) {
        //     throw new NotFoundHttpException();
        // }
        return $this->render('/author/index', [
            'user' => $user,
            'exams' => $exams,
            'articles' => $articles,
            'categories' => $categories,
            'news' => $news ?? '',
        ]);
    }

    public function actionSecondaryNavigation()
    {
        return $this->renderPartial('/layouts/secondary-navigation-web');
    }

    public function actionSecondaryNavigations()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->get();

        if (!isset($request['streamName'])) {
            return $this->renderPartial('../partials/_featured-colleges', [
                'featuredColleges' => [],
            ]);
        }

        $streamName = $request['streamName'];
        $streamIds = Stream::find()->list() ?? [];
        foreach ($streamIds as $streamId => $value) {
            $value = str_replace(' ', '', $value);
            if ($streamName == $value) {
                $featuredColleges = CollegeService::getFeaturedColleges($streamId);
                break;
            }
        }
        if (!empty($featuredColleges)) {
            return $this->renderPartial('../partials/_featured-colleges', [
                'featuredColleges' => $featuredColleges,
            ]);
        } else {
            return $this->renderPartial('../partials/_featured-colleges', [
                'featuredColleges' => [],
            ]);
        }
    }

    public function actionLeadForm()
    {
        return $this->renderPartial('/lead/lead_v2/_form_v4');
    }

    public function actionBannerPopup()
    {
        $data = Yii::$app->request->get();
        $checkSponsorCollege = 0;
        if (isset($data['entity']) && $data['entity'] == 'college') {
            $checkSponsorCollege = COllege::find()->where(['id' => $data['entity-id']])->andWhere(['is_sponsored' => College::SPONSORED_YES])->exists();
            if ($checkSponsorCollege) {
                return  $this->renderPartial('/partials/banner-popup', ['checkSponsorCollege' => $checkSponsorCollege]);
            }
        }
        return $this->renderPartial('/partials/banner-popup', ['checkSponsorCollege' => $checkSponsorCollege]);
    }

    public function actionSaLeadForm()
    {
        return $this->renderPartial('/lead/_sa-form');
    }

    public function actionNewsLeadForm()
    {
        return $this->renderPartial('/lead/_news-form_v2');
    }

    public function actionCommentReplyForm()
    {
        return $this->renderPartial('/partials/comment/_reply-form');
    }

    //Google stories integration
    public function actionGoogleStories()
    {
        $this->layout = false;
        $fetchStories = file_get_contents('https://www.visualstories.dev/api/v1/MTQ4MTQ2NzAtZ2V0bXl1bmktMTQ4NTE3MjItMTU/homepage/get?url=//www.getmyuni.com/visual-stories');

        $data = json_decode($fetchStories);
        return $this->render('/site/google-stories', ['html' => $data->Html]);
    }

    public function actionGoogleStoriesXml()
    {
        $this->layout = false;
        $fetchStories = file_get_contents('https://www.visualstories.dev/api/v1/MTQ4MTQ2NzAtZ2V0bXl1bmktMTQ4NTE3MjItMTU/get?url=https://www.getmyuni.com/visual-stories/storylists.xml');
        $data = json_decode($fetchStories);
        header('Content-Type: application/xml;charset=UTF-8');
        echo $data->Html;
        exit;
    }

    public function actionGoogleStory($slug)
    {
        $url = 'https://www.visualstories.dev/api/v1/MTQ4MTQ2NzAtZ2V0bXl1bmktMTQ4NTE3MjItMTU/get?url=//www.getmyuni.com' . rtrim(strtok($_SERVER['REQUEST_URI'], '?'), '/');

        try {
            $fetchStory = file_get_contents($url);
            $data = json_decode($fetchStory);
            echo $data->Html;
            exit;
        } catch (\Throwable $th) {
            throw new NotFoundHttpException();
        }
    }

    public function actionSendOtp()
    {
        if (Yii::$app->request->isAjax) {
            Yii::$app->response->format = 'json';
            $studentData = Yii::$app->request->post();
            $lead = Lead::find()->select(['is_mobile_verified'])->where(['mobile' => $studentData['phone']])->andWhere(['is_mobile_verified' => 1])->one();
            if (isset($studentData['is_lead']) && !empty($lead) && !empty(Yii::$app->user->identity) && $studentData['phone'] == Yii::$app->user->identity->phone) {
                $response['success'] = true;
                $response['is_registered_user'] = true;
                $response['numberChange'] = 0;
                echo json_encode($response);
                exit;
            }
            //Validate Phone Number
            if (empty($studentData['phone']) or !preg_match('/^[6-9][0-9]{9}$/', $studentData['phone'])) {
                return [
                    'success' => false,
                    'message' => 'Invalid Phone Number!',
                ];
            }

            $student = Student::findByPhone($studentData['phone']);

            if ($student == null && !isset($request['is_lead'])) {
                return [
                    'success' => false,
                    'message' => 'Phone Number is not registered!',
                ];
            }

            $studentOtp = new StudentOtp;

            $existingOtp = $studentOtp->getStudentOtp($student->id);

            $data['expiresIn'] = $studentOtp::OTP_RESEND_SECONDS;

            if ($existingOtp) {
                $currentTimestamp = (new \yii\db\Query)->select(new \yii\db\Expression('NOW()'))->scalar();
                $resendExpiry = date('Y-m-d H:i:s', strtotime("$existingOtp->created_at + " . $data['expiresIn'] . ' seconds'));
                $resend = ($resendExpiry < $currentTimestamp) ? true : false;
            }

            if (empty($existingOtp) or !empty($resend)) {
                $studentOtp->student_id = $student->id;
                $studentOtp->otp = rand(1356, 8675);
                SmsService::sendOtp($student->phone, $studentOtp->otp);
                $studentOtp->save();
                return [
                    'success' => true,
                    'message' => '',
                    'data' => $data,
                    'numberChange' => (!empty($studentData) && !empty(Yii::$app->user->identity)) ? ($studentData['phone'] == Yii::$app->user->identity->phone ? 0 : 1) : 1,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Please wait before requesting OTP again!',
                    'data' => $data,
                ];
            }
        }
    }

    public function actionAmpLead()
    {
        Yii::$app->response->format = 'json';
        $loginData = Yii::$app->request->post();
        $model = new Lead();

        $model->name = $loginData['name'] ?? '';
        $model->email = $loginData['email'] ?? '';
        $model->mobile = $loginData['phone'] ?? '';
        $model->entity = $loginData['entity'] ?? 0;
        $model->entity_id = $loginData['entity_id'] ?? '';
        $model->interested_course = '';
        $model->qualification = '';
        $model->current_location = $loginData['current_city'] ?? '';
        $model->cta_location = $loginData['cta_location'] ?? 'auto-pop-up';
        $model->cta_text = $loginData['id'] == 1 ? 'Auto Pop Up' : 'top-sticky';
        $model->url = $loginData['url'] ?? '';
        $model->platform = 'wap';
        $model->is_mobile_verified = 1;

        if ($model->save()) {
            $student = Student::findByPhone($loginData['phone']);

            if (empty($student)) {
                $student = new Student;
                $student->phone = $loginData['phone'];
                $student->name = $loginData['name'];
                $student->email = $loginData['email'];
                $student->current_city = !empty($loginData['current_city']) ? $loginData['current_city'] : '';
                $student->current_state = !empty($loginData['current_city']) ? (new UserService)->getCityId('', $loginData['current_city'])['stateId'] : '';
                $student->current_city_ip = !empty($loginData['current_city_ip']) ? $loginData['current_city_ip'] : null;
                $student->current_state_ip = !empty($loginData['current_state_ip']) ? $loginData['current_state_ip'] : null;
                $student->source_url = $loginData['url'];
                $student->source = 0;
                $student->user_type = Student::USER_TYPE_NORMAL;
                if ($student->save()) {
                    $model->user_id = $student->id;
                    $model->save();
                    self::updateStudentActivityPreferenceAmp($student, $loginData, $model);
                } else {
                    print_r($student->getErrors());
                }
            } elseif (!empty($student)) {
                $student->name = $loginData['name'];
                $student->email = $loginData['email'];
                $student->current_city = !empty($loginData['current_city']) ? $loginData['current_city'] : '';
                $student->current_state = !empty($loginData['current_city']) ? (new UserService)->getCityId('', $loginData['current_city'])['stateId'] : '';
                $student->current_city_ip = !empty($loginData['current_city_ip']) ? $loginData['current_city_ip'] : null;
                $student->current_state_ip = !empty($loginData['current_state_ip']) ? $loginData['current_state_ip'] : null;
                // $student->source_url = $loginData['url'];
                // $student->source = 0;
                // $student->user_type = Student::USER_TYPE_NORMAL;

                if ($student->save()) {
                } else {
                    print_r($student->getErrors());
                }
                $model->user_id = $student->id;
                $model->save();
                self::updateStudentActivityPreferenceAmp($student, $loginData, $model);
            } else {
                return null;
            }
            return ['success' => true];
        } else {
            print_r($model->getErrors());
            exit;
            return ['success' => false];
        }
    }

    public function updateStudentActivityPreferenceAmp($student, $request)
    {
        $studentActivity = new StudentActivity;
        $studentActivity->student_id = $student->id;
        $studentActivity->url = $request['url'] ?? '';
        $studentActivity->cta_text = $request['cta_text'] ?? 'Auto Pop Up';
        $studentActivity->cta_position = $request['cta_location'] ?? 'auto-pop-up';
        $studentActivity->source = 0;
        $studentActivity->category = $request['category'] ?? 'news-detail';
        $studentActivity->category_sub_page = $request['category_sub_page'] ?? 'news-amp';
        $studentActivity->platform = 'wap';

        if ($studentActivity->save()) {
            $studentPreference = new StudentPreference();
            $studentPreference->activity_id = $studentActivity->id;
            $studentPreference->student_id = $student->id;
            $studentPreference->interested_city = null;
            $studentPreference->interested_state = null;
            $studentPreference->course = '';
            $studentPreference->child_course_id = '';
            $studentPreference->program_id = '';
            $studentPreference->degree = '';
            $studentPreference->highest_qualification = '';
            $studentPreference->distance_education = 0;
            $studentPreference->specialization = '';
            $studentPreference->exam = null;
            $studentPreference->stream = $request['interested_stream'] ?? null;
            $studentPreference->level = $request['interested_level'] ?? null;

            if ($studentPreference->save()) {
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    //Dispalys contact-us page to get user queries
    public function actionContactUs()
    {
        return $this->render('/layouts/contact-us');
    }

    //Triggers contact-us mail containg user input data
    public function actionContactUsMail()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->post();
        return Yii::$app->sysMailer->compose()
            ->setFrom('<EMAIL>')
            ->setTo('<EMAIL>')
            ->setSubject('New Contact Us Page Enquiry')
            ->setHtmlBody('Name : ' . $request['userName'] . '<br>' . 'Email Id : ' . $request['userMailId'] . '<br>' . 'Mobile Number : ' . $request['userMobile'] . '<br>' . 'Query : ' . $request['queryContent'])
            ->send();
    }

    //Triggers logout from Ci Homepage
    public function actionLogoutCi()
    {
        $csrfToken = Yii::$app->request->getCsrfToken();
        return $this->renderPartial('/layouts/logout-ci', [
            'csrfToken' => $csrfToken
        ]);
    }

    public function actionCsrf()
    {
        return json_encode(['csrf' => Yii::$app->request->getCsrfToken()]);
    }

    public function getNewArticle($userID, $categoryId, $lang_code, $offset)
    {
        $articles = Article::find()->where(['author_id' => $userID])->andWhere(['lang_code' => $lang_code])
            ->andWhere(['not', ['category_id' => $categoryId]])
            ->offset($offset)
            ->limit(20)
            ->active();
        $articles = new ActiveDataProvider([
            'query' => $articles,
            'pagination' => false,
        ]);
        return $articles;
    }

    public function getExamArticle($userID, $offset)
    {
        $query = ExamContent::find()
            ->where(['author_id' => $userID])
            //            ->andWhere(['<>', 'author_id', null])
            ->andWhere(['status' => ExamContent::STATUS_ACTIVE])
            ->with('exam')
            ->offset($offset)
            ->limit(20)
            ->groupBy('exam_id');

        $exams = new ActiveDataProvider([
            'query' => $query,
            'pagination' => false,
        ]);

        return $exams;
    }

    public function getNewsArticle($userId, $lang_code, $offset)
    {
        $query = NewsContent::find()
            ->innerJoin(News::tableName(), NewsContent::tableName() . '.news_id = ' . News::tableName() . '.id')
            ->where([NewsContent::tableName() . '.author_id' => $userId])
            ->andWhere([News::tableName() . '.lang_code' => $lang_code])
            ->andWhere([NewsContent::tableName() . '.status' => NewsContent::STATUS_ACTIVE])
            ->andWhere([News::tableName() . '.status' => News::STATUS_ACTIVE])
            ->offset($offset)
            ->limit(20)
            ->orderBy([NewsContent::tableName() . '.created_at' => SORT_DESC]);

        $news = new ActiveDataProvider([
            'query' => $query,
            'pagination' => false,
        ]);

        return $news;
    }

    public function actionGetTrendingPageData()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->post();
        $entityId = $request['entityid'] ?? null;
        $subPage = $request['subPage'] ?? false;
        $result = $this->homeService->getTrendingPageData($request['entity'], $entityId, $subPage, null);
        return $result ?? [];
    }

    /**
     * Displays Privacy page.
     *
     * @return mixed
     */
    public function actionPrivacyPolicy()
    {
        return $this->render('privacy');
    }

    public function actionGetHomePageArticles()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->get();
        $result = [];
        if (!empty($request['stream'])) {
            if (!empty($request['isPopular'])) {
                $result = $this->homeService->getHomePageArticles($request['stream'], 10, true);
            } else {
                $result = $this->homeService->getHomePageArticles($request['stream'], 10, null);
            }
        }
        return $result ?? [];
    }

    public function actionTermsAndConditions()
    {
        return $this->render('terms-and-conditions');
    }

    public function actionAboutUs()
    {
        return $this->render('/layouts/about-us');
    }
}

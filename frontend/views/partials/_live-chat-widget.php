<?php
/* @var $this yii\web\View */
/* @var $liveChatGroup array */
/* @var $compact bool */

use common\helpers\ContentHelper;
use frontend\assets\AppAsset;
use yii\helpers\Html;

$compact = $compact ?? false;
$widgetClass = $compact ? 'live-chat-widget-compact' : 'live-chat-widget';

$group = $liveChatGroup['liveChatGroup'];
$activeUsers = $liveChatGroup['activeUsersCount'];

$loggedInUser = Yii::$app->user->identity;

// All messages for chat window (ASC order so chat reads naturally)
$allMessages = $liveChatGroup['allMessages'];


//get Online user
$onlineUsers = $liveChatGroup['onlineUsers'];

// Messages for previews/names (DESC to get most recent first)
$latestMessages = $liveChatGroup['latestMessages'];

// Extract unique user names (ignoring Admin + logged in user)
$recentUserNames = [];
foreach ($onlineUsers as $user) {
    $name = $user['name'];
    $id = $user['user_id'];
    if (strtolower($name) !== 'admin' &&
        (!$loggedInUser || $id !== $loggedInUser->id) &&
        !in_array($name, $recentUserNames)
    ) {
        $recentUserNames[] = $name;
    }
    if (count($recentUserNames) >= 3) {
        break;
    }
}

// Unique latest messages for preview
$uniqueMessages = [];
foreach ($latestMessages as $msg) {
    if (strtolower($msg['user_name']) !== 'admin' &&
        (!$loggedInUser || $msg['user_name'] !== $loggedInUser->username) &&
        !isset($uniqueMessages[$msg['user_name']])
    ) {
        $uniqueMessages[$msg['user_name']] = $msg;
    }
    if (count($uniqueMessages) >= 3) {
        break;
    }
}

$displayMessages = array_values($uniqueMessages);

$this->registerCssFile(Yii::$app->params['cssPath'] . 'enhanced-live-chat.css', ['depends' => [AppAsset::class]]);
?>

<div class="pageMask" style="display: none;"></div>
<!-- Enhanced Live Chat Widget -->
<div id="enhanced-chat-widget" class="<?= $widgetClass ?>">
    <!-- Chat Header with Stats -->
    <div class="chat-header-enhanced">
        <div class="chat-title-section">
            <div class="chat-icon">
                <!-- <i class="fa fa-comment-o"></i> -->
                <?=
                /** Chat SVG */
                '' ?>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                    viewBox="0 0 24 24" fill="none" stroke="#fff"
                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                </svg>
            </div>
            <div class="chat-title">
                <h4>Live Discussion</h4>
                <span class="exam-name"><?= Html::encode($group['group_name']) ?></span>
            </div>
        </div>

        <div class="chat-stats">
            <div class="stat-item">
                <div class="stat-number-wrapper">
                    <!-- <span class="spriteIcon usersIcon"></span> -->
                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-users h-4 w-4">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg> -->
                    <img src="/yas/images/live-user-icon.svg" alt="Users" class="icon h-4 w-4" style="width: 16px; height: 16px;">
                    <div class="stat-number" id="online-users-count">0</div>
                </div>
                <div class="stat-label">Online</div>
            </div>
            <div class="stat-item">
                <div class="stat-number-wrapper">
                    <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                        stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-trending-up h-4 w-4">
                        <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                        <polyline points="16 7 22 7 22 13"></polyline>
                    </svg> -->
                    <img src="/yas/images/live-stat-icon.svg" alt="Active" class="icon h-4 w-4" style="width: 16px; height: 16px;">
                    <div class="stat-number"><?= $activeUsers ?></div>
                </div>
                <div class="stat-label">Active</div>
            </div>
        </div>
        <hr class="chat-divider">
        <div class="chat-status-indicator">
            <div class="status-dot live"></div>
            <span class="status-text">Live Chat Active</span>
        </div>
    </div>

    <!-- Chat Content Area -->
    <div class="chat-content" id="chat-content">
        <!-- Initial State: Lead Form for Logged Out Users -->
        <div class="chat-lead-form" id="chat-lead-form">
            <div class="recent-messages-preview">
                <h5>Recent Messages</h5>
                <div class="message-preview">

                    <?php foreach ($displayMessages as $message): ?>
                        <div class="preview-item">
                            <strong><?= $message['user_name'] ?></strong>
                            <span class="time"><?= Yii::$app->formatter->asRelativeTime($message['created_at']) ?></span>
                            <p class="preview-text-live-user"><?= $message['message_text'] ?></p>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="join-form-section">
                <div class="join-stats-transtion">Join Chat</div>
                <p class="students-count" id="online-users-discussion-count"></p>

                <form class="lead-form" id="chat-lead-form-inputs" onsubmit="return false;">
                    <input type="text" placeholder="Your name" class="form-control" id="lead-name" autocomplete="off" maxlength="50" required>
                    <input type="tel" placeholder="Mobile number" class="form-control" id="lead-mobile" maxlength="10" autocomplete="off" maxlength="50" required>
                    <input type="email" placeholder="Email address" class="form-control" id="lead-email" autocomplete="off" maxlength="50">
                    <div class="form-control inputStreamLiveContainer modalInputContainer streamLiveClass streamLiveCategory inputStreamLive">
                        <select class="inputContainerField select2HookClass streamLive data-gtm data-gtm-change" data-user-input="streamLive" id="interested_stream_live_lead" name="inputStreamLive">
                            <option></option>
                        </select>
                    </div>
                    <div class="form-control inputLevelLiveContainer modalInputContainer levelLiveClass levelLiveCategory inputLevelLive">
                        <select class="inputContainerField select2HookClass levelLive data-gtm data-gtm-change" data-user-input="levelLive" id="interested_level_live_lead" name="inputLevelLive" disabled>
                            <option></option>
                        </select>
                    </div>

                    <button type="button" class="btn btn-primary join-chat-submit" disabled id="join-chat-form-btn">Join Chat</button>
                </form>
            </div>
        </div>

        <div class="otp-box" id="otp-box-live-chat" style="display: none;">
            <div class="join-stats-transtion">Join Chat</div>
            <p class="students-count" id="online-users-discussion-count-otp"></p>
            <input type="text" placeholder="Enter OTP" id="otp-input-live-chat" autocomplete="off" maxlength="4">
            <span class="otplabel" style="display: none;"></span>
            <button class="verify-btn" id="verify-btn-live-chat" disabled>Verify</button>
            <a href="#" class="change-number" id="change-number-btn">Change Number</a>
        </div>

        <div class="otp-toast" id="otp-toast-live-chat" style="display: none;">
            <strong>OTP Sent!</strong>
            Verification code sent to <p class="user-phone-live"></p>
        </div>

        <!-- Chat Interface (Hidden Initially) -->
        <div class="chat-interface" id="chat-interface" style="display: none;">
            <!-- Live Chat Messages Interface -->
            <div class="live-chat-messages" id="live-chat-messages">
                <!-- Welcome Message -->
                <?php if (!empty($recentUserNames)): ?>
                    <div class="welcome-notification">
                        <p class="welcome-text-user-names" id="welcome-text-user-names">🎉 <strong><?= implode(', ', $recentUserNames) ?></strong> joined the chat</p>
                    </div>
                <?php else: ?>
                    <div class="welcome-notification">
                        <p class="welcome-text-user-names" id="welcome-text-user-names">🎉 Welcome to the chat!</p>
                    </div>
                <?php endif; ?>

                <!-- Chat Tabs -->
                <div class="chat-tabs-container">
                    <div class="chat-tabs">
                        <span class="tab active" data-tab="messages">
                            <!-- <i class="fa fa-comment"></i>  -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="8" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4 mr-1"
                                data-lov-id="src/components/chat/ChatInterface.tsx:28:12" data-lov-name="MessageSquare" data-component-path="src/components/chat/ChatInterface.tsx"
                                data-component-line="28" data-component-file="ChatInterface.tsx" data-component-name="MessageSquare" data-component-content="%7B%22className%22%3A%22h-4%20w-4%20mr-1%22%7D">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                            </svg>
                            <p style="font-weight: 600;">Messages</p>
                            <div class="badge"><?= count($allMessages) ?></div>
                        </span>
                        <!-- <span class="tab" data-tab="doubts">
                            <i class="fa fa-question-circle"></i> Doubts <span class="badge">1</span>
                        </span>
                        <span class="tab" data-tab="files">
                            <i class="fa fa-file"></i> Files
                        </span>
                        <span class="tab" data-tab="groups">
                            <i class="fa fa-users"></i> Groups
                        </span> -->
                    </div>
                </div>

                <!-- Messages Container -->

                <div class="messages-container" id="messages-container">
                    <?php
                    $palette = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA726', '#AB47BC', '#26A69A'];

                    foreach ($allMessages as $msg):
                        $userName = trim($msg['user_name'] ?? '');
                        if ($userName === '' || $userName === 'undefined') {
                            continue;
                        }

                        if ($msg['message_type'] === 'file') {
                            $attachments = json_decode($msg['attachments'] ?? '', true);
                            if (empty($attachments) || !is_array($attachments)) {
                                continue;
                            }
                        }

                        if (in_array($msg['message_type'], ['text', 'link'], true) && trim($msg['message_text'] ?? '') === '') {
                            continue;
                        }

                        $isCurrentUser = ($loggedInUser && $loggedInUser->id == $msg['user_id']);
                        $isAdmin       = $msg['is_admin_message'];
                        $username      = htmlspecialchars($msg['user_name']);

                        $messageTime = new DateTime($msg['created_at']);
                        $isoTimestamp = $messageTime->format('c'); // ISO 8601 format
                        $displayTime = $messageTime->format('h:i A');

                        $messageText   = $msg['message_type'] === 'text' ? nl2br(ContentHelper::removeStyleTag(stripslashes(html_entity_decode($msg['message_text'])))) : '';
                        $initials      = strtoupper(substr($username, 0, 2));

                        $displayName   = $isCurrentUser ? '' : $username;
                        $wrapperClass  = $isAdmin ? 'message moderator-message' : 'message user-message';
                        $textClass     = $isAdmin ? 'message-text moderator-text' : 'message-text';
                        $highlight     = $isCurrentUser ? 'highlighted' : '';

                        $avatarStyle   = $isAdmin
                            ? 'background:#d81b60;color:#fff;'
                            : 'background:' . $palette[crc32($username) % count($palette)] . ';color:#fff;';

                        // attachments
                        $attachmentsHtml = '';
                        if ($msg['message_type'] === 'file' && !empty($msg['attachments'])) {
                            foreach ((array) json_decode($msg['attachments'], true) as $file) {
                                $fileName = htmlspecialchars($file['name']);
                                $fileUrl  = $file['url'];
                                $fileType = isset($file['type']) ? $file['type'] : '';
                                $isImage = preg_match('/\.(jpg|jpeg|png|gif|webp|svg)$/i', $fileName) ||
                                    strpos($fileType, 'image/') === 0;

                                $icon = $isImage ? '🖼️' : '📂';
                                $downloadAttr = $isImage ? '' : 'download="' . $fileName . '"';

                                $attachmentsHtml .= "
                    <div class='message-text attachment'>
                        <span class='chat-attachment-link-icon'>{$icon}</span> 
                       <a href='{$fileUrl}' target='_blank' class='chat-attachment-link' {$downloadAttr}>
                                <span class='filename'>{$fileName}</span>
                            </a>
                    </div>";
                            }
                        }
                        ?>
                        <div class="<?= $wrapperClass ?> <?= $highlight ?>" data-timestamp="<?= $isoTimestamp ?>">
                            <?php if (!$isCurrentUser): ?>
                                <div class="message-avatar" style="<?= $avatarStyle ?>"><?= $initials ?></div>
                            <?php endif; ?>

                            <div class="message-content">
                                <div class="message-header">
                                    <span class="username"><?= $displayName ?></span>
                                    <?php if ($isAdmin): ?>
                                        <span class="badge mod-badge">Mod</span>
                                    <?php endif; ?>
                                </div>

                                <div class="<?= $textClass ?>">
                                    <?php if (!empty($messageText)): ?>
                                        <?= $messageText ?>
                                    <?php endif; ?>

                                    <?php if (!empty($attachmentsHtml)): ?>
                                        <?= $attachmentsHtml ?>
                                    <?php endif; ?>

                                    <?php if ($msg['message_type'] === 'link'): ?>
                                        <a class="chat-user-link" href="<?= htmlspecialchars($msg['message_text']) ?>" target="_blank">
                                            <?= htmlspecialchars($msg['message_text']) ?>
                                        </a>
                                    <?php endif; ?>
                                </div>

                                <div class="message-footer <?= $isCurrentUser ? 'current-user-live' : '' ?>">
                                    <?php if ($isCurrentUser): ?>
                                        <span class="tick-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                viewBox="0 0 24 24" fill="none" stroke="#3ae478" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-check-check h-3 w-3 text-accent">
                                                <path d="M18 6 7 17l-5-5"></path>
                                                <path d="m22 10-7.5 7.5L13 16"></path>
                                            </svg>
                                        </span>
                                    <?php endif; ?>

                                    <span class="timestamp">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="<?= $isCurrentUser ? 16 : 12 ?>"
                                            height="<?= $isCurrentUser ? 16 : 12 ?>" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-clock <?= $isCurrentUser ? 'h-3 w-3 text-muted' : '' ?>">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12 6 12 12 16 14"></polyline>
                                        </svg>
                                        <?= $displayTime ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Message Input -->
                <div class="message-input-container">
                    <div class="input-wrapper">
                        <button class="attachment-btn" title="Attach file">
                            <!-- <i class="fa fa-plus"></i> -->
                            <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-plus h-4 w-4">
                                <path d="M5 12h14"></path>
                                <path d="M12 5v14"></path>
                            </svg> -->
                            <img src="/yas/images/live-user-plus-icon.svg" alt="Plus" class="icon h-4 w-4" style="width: 16px; height: 16px;">
                        </button>
                        <input type="file" id="file-input-live-chat" style="display:none;" multiple />
                        <div class="input-with-emoji">
                            <input type="text" placeholder="Type your message..." class="message-input" id="message-input-enhanced">
                            <!-- <button class="emoji-btn" title="Add emoji">
                                <i class="fa fa-smile-o"></i>
                            </button> -->
                        </div>
                        <button class="send-btn" id="send-btn-enhanced" title="Send message" disabled>
                            <!-- <i class="fa fa-send"></i> -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"></path>
                                <path d="m21.854 2.147-10.94 10.939"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <p id="input-hint-text">Max File Size: 10KB, Max 5 files</p>
        </div>
    </div>
</div>

<!-- Chat Guidelines Modal (Hidden by default) -->
<div class="modal fade" id="chatGuidelinesModal" tabindex="-1" role="dialog" style="display: none;">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content chat-guidelines-modal">
            <div class="modal-header">
                <button type="button" class="guidelinesClose" data-dismiss="modal" aria-label="Close">
                    <!-- <span aria-hidden="true">&times;</span> -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#000"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-4 w-4" data-lov-id="src/components/ui/dialog.tsx:46:8" data-lov-name="X" data-component-path="src/components/ui/dialog.tsx" data-component-line="46" data-component-file="dialog.tsx" data-component-name="X" data-component-content="%7B%22className%22%3A%22h-4%20w-4%22%7D">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
                <div class="guidelines-icon">
                    <!-- <i class="fa fa-users"></i> -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-6 w-6 text-white"
                        data-lov-id="src/components/chat/ChatGuidelines.tsx:39:12" data-lov-name="Users"
                        data-component-path="src/components/chat/ChatGuidelines.tsx" data-component-line="39" data-component-file="ChatGuidelines.tsx" data-component-name="Users" data-component-content="%7B%22className%22%3A%22h-6%20w-6%20text-white%22%7D">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                </div>
            </div>
            <div class="modal-body">
                <h2>Chat Guidelines</h2>
                <p class="guidelines-subtitle-p">Please follow these guidelines to maintain a healthy learning environment</p>

                <div class="guideline-item">
                    <div class="guideline-icon success">
                        <!-- <i class="fa fa-check"></i> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="#3ae478" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-users h-4 w-4 text-accent" data-lov-id="src/components/chat/ChatGuidelines.tsx:51:16" data-lov-name="guideline.icon" data-component-path="src/components/chat/ChatGuidelines.tsx" data-component-line="51" data-component-file="ChatGuidelines.tsx" data-component-name="guideline.icon" data-component-content="%7B%22className%22%3A%22h-4%20w-4%20text-accent%22%7D">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                    </div>
                    <div class="guideline-content">
                        <p class="guideline-title">✔️ Maintain proper decorum in class</p>
                        <p class="guideline-description">Be respectful to fellow learners and moderators</p>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon warning">
                        <!-- <i class="fa fa-exclamation-triangle"></i> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#3ae478"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert h-4 w-4 text-accent" data-lov-id="src/components/chat/ChatGuidelines.tsx:51:16" data-lov-name="guideline.icon"
                            data-component-path="src/components/chat/ChatGuidelines.tsx" data-component-line="51" data-component-file="ChatGuidelines.tsx" data-component-name="guideline.icon" data-component-content="%7B%22className%22%3A%22h-4%20w-4%20text-accent%22%7D">
                            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
                            <path d="M12 9v4"></path>
                            <path d="M12 17h.01"></path>
                        </svg>
                    </div>
                    <div class="guideline-content">
                        <p class="guideline-title">✔️ Inappropriate messages, cursing, or self-promotion is strictly prohibited</p>
                        <p class="guideline-description">Keep discussions relevant to education and exams</p>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon success">
                        <!-- <i class="fa fa-heart"></i> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#3ae478" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="lucide lucide-shield h-4 w-4 text-accent" data-lov-id="src/components/chat/ChatGuidelines.tsx:51:16" data-lov-name="guideline.icon" data-component-path="src/components/chat/ChatGuidelines.tsx"
                            data-component-line="51" data-component-file="ChatGuidelines.tsx" data-component-name="guideline.icon" data-component-content="%7B%22className%22%3A%22h-4%20w-4%20text-accent%22%7D">
                            <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path>
                        </svg>
                    </div>
                    <div class="guideline-content">
                        <p class="guideline-title">✔️ Be respectful to other learners</p>
                        <p class="guideline-description">Help create a supportive learning environment</p>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon info">
                        <!-- <i class="fa fa-shield"></i> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#3ae478" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big h-4 w-4 text-accent" data-lov-id="src/components/chat/ChatGuidelines.tsx:51:16" data-lov-name="guideline.icon"
                            data-component-path="src/components/chat/ChatGuidelines.tsx" data-component-line="51" data-component-file="ChatGuidelines.tsx" data-component-name="guideline.icon" data-component-content="%7B%22className%22%3A%22h-4%20w-4%20text-accent%22%7D">
                            <path d="M21.801 10A10 10 0 1 1 17 3.335"></path>
                            <path d="m9 11 3 3L22 4"></path>
                        </svg>
                    </div>
                    <div class="guideline-content">
                        <p class="guideline-title">✔️ Refrain from offensive behaviour</p>
                        <p class="guideline-description">Report any inappropriate content to moderators</p>
                    </div>
                </div>

                <div class="safe-space-section">
                    <div class="safe-space-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="#2196f3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-shield h-4 w-4" data-lov-id="src/components/chat/ChatGuidelines.tsx:67:12" data-lov-name="Shield" data-component-path="src/components/chat/ChatGuidelines.tsx" data-component-line="67" data-component-file="ChatGuidelines.tsx" data-component-name="Shield" data-component-content="%7B%22className%22%3A%22h-4%20w-4%22%7D">
                            <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path>
                        </svg>
                        <p class="safe-space-title">Safe Learning Space</p>
                    </div>
                    <p class="safe-space-content">Our moderators actively monitor chats to ensure a safe and productive learning environment for all students.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-block" id="start-chatting-btn">
                    Got it, let's chat! 🎓
                </button>
            </div>
        </div>
    </div>
</div>

<div id="live-chat-config"
    data-channel-id="<?= $group['channel_id'] ?>"
    data-channel-type="<?= $group['channel_type'] ?>"
    data-entity-type="<?= $group['entity'] ?>"
    data-entity-id="<?= $group['entity_id'] ?>"
    data-current-user='<?= !$loggedInUser ? 'null' : json_encode([
                            'id' => $loggedInUser->id,
                            'name' => $loggedInUser->name ?? 'User'
                        ]) ?>'>
</div>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

<?php
$this->registerJsFile(Yii::$app->params['jsPath'] . 'enhanced-live-chat.js', [
    'position' => \yii\web\View::POS_END,
    'defer' => true,
    'depends' => [AppAsset::class],
]);
?>
<style>
    .whatsapp-icon {
        width: 80px;
        height: 80px;
        padding: 5px;
        justify-content: center;
        display: flex;
        align-items: center;
        flex-direction: column;
        right: 20px;
        bottom: 63px;
        position: fixed;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 1px 1px 8px -1px #aaa;
        border: 1px solid rgba(255, 78, 83, 1);
        z-index: 10;
    }

    .icon-div {
        width: 70px;
        height: 70px;
        background: rgba(255, 78, 83, 1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* .pulse {
        right: 40px;
        bottom: 28px;
        width: 80px;
        height: 80px;
        border-radius: 50%;
    } */

    .pulse:before,
    .pulse:after {
        content: "";
        position: absolute;
        height: 100%;
        width: 100%;
        background-color: #eb4726;
        border-radius: 50%;
        z-index: -1;
        opacity: 0.7;
    }

    .pulse::before {
        animation: pulse-animation 2s ease-out infinite;
    }

    .pulse::after {
        animation: pulse-animation 2s 1s ease-out infinite;
    }

    @keyframes pulse-animation {
        100% {
            transform: scale(1.6);
            opacity: 0;
        }
    }

    /* Bottom Sheet Modal */
    #callerModal {
        display: none;
        position: fixed;
        bottom: 0;
        /* Start from below the screen */
        left: 0;
        width: 100%;
        background: white;
        box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.2);
        border-radius: 12px 12px 0 0;
        padding: 15px;
        text-align: center;
        transition: transform 0.4s ease-in-out;
        z-index: 10;
    }

    /* When modal is active */
    #callerModal.active {
        bottom: 0;
        /* Move it up */
    }

    #callerModal a {
        display: block;
        font-size: 18px;
        font-weight: bold;
        text-decoration: none;
        color: #007AFF;
        padding: 15px;
        background: #f9f9f9;
        border-radius: 12px;
        margin-bottom: 10px;
    }

    #callerModal a img {
        width: 16px;
        margin-right: 10px;
        vertical-align: middle;
    }

    .cancel-btn {
        font-size: 18px;
        font-weight: bold;
        color: #007AFF;
        background: white;
        border: none;
        width: 100%;
        padding: 15px;
        cursor: pointer;
        border-radius: 12px;
    }

    /* Overlay */
    .overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4);
    }
</style>
<div class="whatsapp-icon gtm-whatsapp-icon pulse" id="sendCallerLeadToCld" onclick="callerIcon(1)">
    <div class="icon-div">
        <img src="https://static.collegedekho.com/static-up/images/call-outgoing.svg" />
    </div>
</div>

<!-- Overlay -->
<div class="overlay" id="overlay"></div>

<!-- Bottom Sheet Modal -->
<div class="modal" id="callerModal">
    <a href="tel:+917969542200">
        <img src="https://static.collegedekho.com/static-up/images/call-outgoing.svg" />
        Call +91 79-69542200
    </a>
    <button class="cancel-btn" id="closeModal" onclick="callerIcon(2)">Cancel</button>
</div>
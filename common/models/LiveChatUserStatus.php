<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "live_chat_user_status".
 *
 * @property int $id
 * @property int $user_id Reference to user table
 * @property string $channel_id Reference to live_chat_group table
 * @property int|null $online_status Online status: 0=Offline, 1=Online
 * @property string $created_at
 * @property string $updated_at
 */
class LiveChatUserStatus extends \yii\db\ActiveRecord
{
    const STATUS_ONLINE = 1;
    const STATUS_OFFLINE = 0;

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'live_chat_user_status';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'channel_id'], 'required'],
            [['user_id', 'online_status'], 'integer'],
            [['channel_id'], 'unique', 'targetAttribute' => ['user_id', 'channel_id']],
            [['channel_id'], 'string', 'max' => 255],
            [['created_at', 'updated_at', 'last_seen_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'Reference to user table',
            'channel_id' => 'Reference to live_chat_group table',
            'online_status' => 'Online status: 0=Offline, 1=Online',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public function getUser()
    {
        return $this->hasOne(Student::className(), ['id' => 'user_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\LiveChatUserStatusQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\LiveChatUserStatusQuery(get_called_class());
    }
}

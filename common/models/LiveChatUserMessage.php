<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use common\models\User;

/**
 * This is the model class for table "live_chat_user_message".
 *
 * @property int $id
 * @property int $group_id Reference to live_chat_group table
 * @property int|null $user_id Reference to user table (null for guest users)
 * @property string $message_id GetStream message ID
 * @property string|null $message_text Message content
 * @property string|null $message_type Message type: text, image, file, etc.
 * @property string|null $attachments JSON encoded array of file attachments
 * @property string|null $user_name User display name
 * @property string|null $user_email User email
 * @property int|null $is_admin_message Is admin message: 0=No, 1=Yes
 * @property int|null $is_deleted Is message deleted: 0=No, 1=Yes
 * @property string $created_at
 * @property string $updated_at
 *
 * @property LiveChatGroup $group
 * @property User $user
 */
class LiveChatUserMessage extends ActiveRecord
{
    // Message type constants
    const MESSAGE_TYPE_TEXT = 'text';
    const MESSAGE_TYPE_IMAGE = 'image';
    const MESSAGE_TYPE_FILE = 'file';
    const MESSAGE_TYPE_VIDEO = 'video';
    const MESSAGE_TYPE_AUDIO = 'audio';
    const MESSAGE_TYPE_SYSTEM = 'system';

    // Admin message constants
    const ADMIN_MESSAGE_NO = 0;
    const ADMIN_MESSAGE_YES = 1;

    // Deleted status constants
    const DELETED_NO = 0;
    const DELETED_YES = 1;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'live_chat_user_message';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => function () {
                    return date('Y-m-d H:i:s');
                },
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['group_id', 'message_id'], 'required'],
            [['group_id', 'user_id', 'is_admin_message', 'is_deleted'], 'integer'],
            [['message_text'], 'string', 'max' => 280],
            [['created_at', 'updated_at'], 'safe'],
            [['message_id', 'user_name', 'user_email'], 'string', 'max' => 255],
            [['message_type'], 'string', 'max' => 50],
            [['message_type'], 'in', 'range' => [
                self::MESSAGE_TYPE_TEXT,
                self::MESSAGE_TYPE_IMAGE,
                self::MESSAGE_TYPE_FILE,
                self::MESSAGE_TYPE_VIDEO,
                self::MESSAGE_TYPE_AUDIO,
                self::MESSAGE_TYPE_SYSTEM
            ]],
            [['attachments'], 'safe'],
            [['is_admin_message'], 'in', 'range' => [self::ADMIN_MESSAGE_NO, self::ADMIN_MESSAGE_YES]],
            [['is_deleted'], 'in', 'range' => [self::DELETED_NO, self::DELETED_YES]],
            [['user_email'], 'email'],
            [['group_id'], 'exist', 'skipOnError' => true, 'targetClass' => LiveChatGroup::class, 'targetAttribute' => ['group_id' => 'id']],
            [['user_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['user_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'group_id' => 'Group',
            'user_id' => 'User',
            'message_id' => 'Message ID',
            'message_text' => 'Message Text',
            'message_type' => 'Message Type',
            'user_name' => 'User Name',
            'user_email' => 'User Email',
            'is_admin_message' => 'Admin Message',
            'is_deleted' => 'Deleted',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Group]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getGroup()
    {
        return $this->hasOne(LiveChatGroup::class, ['id' => 'group_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    /**
     * Get message types array for dropdown
     *
     * @return array
     */
    public static function getMessageTypes()
    {
        return [
            self::MESSAGE_TYPE_TEXT => 'Text',
            self::MESSAGE_TYPE_IMAGE => 'Image',
            self::MESSAGE_TYPE_FILE => 'File',
            self::MESSAGE_TYPE_VIDEO => 'Video',
            self::MESSAGE_TYPE_AUDIO => 'Audio',
            self::MESSAGE_TYPE_SYSTEM => 'System',
        ];
    }

    /**
     * Get admin message types array for dropdown
     *
     * @return array
     */
    public static function getAdminMessageTypes()
    {
        return [
            self::ADMIN_MESSAGE_NO => 'User Message',
            self::ADMIN_MESSAGE_YES => 'Admin Message',
        ];
    }

    /**
     * Get deleted status types array for dropdown
     *
     * @return array
     */
    public static function getDeletedStatusTypes()
    {
        return [
            self::DELETED_NO => 'Active',
            self::DELETED_YES => 'Deleted',
        ];
    }

    /**
     * Get message type label
     *
     * @return string
     */
    public function getMessageTypeLabel()
    {
        $types = self::getMessageTypes();
        return isset($types[$this->message_type]) ? $types[$this->message_type] : 'Unknown';
    }

    /**
     * Get admin message label
     *
     * @return string
     */
    public function getAdminMessageLabel()
    {
        $types = self::getAdminMessageTypes();
        return isset($types[$this->is_admin_message]) ? $types[$this->is_admin_message] : 'Unknown';
    }

    /**
     * Get deleted status label
     *
     * @return string
     */
    public function getDeletedStatusLabel()
    {
        $types = self::getDeletedStatusTypes();
        return isset($types[$this->is_deleted]) ? $types[$this->is_deleted] : 'Unknown';
    }

    /**
     * Get display name for the message sender
     *
     * @return string
     */
    public function getSenderDisplayName()
    {
        if ($this->is_admin_message) {
            return 'Admin';
        }
        
        if ($this->user) {
            return $this->user->name ?: $this->user->username;
        }
        
        return $this->user_name ?: 'Guest User';
    }

    /**
     * Check if message is from admin
     *
     * @return bool
     */
    public function isAdminMessage()
    {
        return $this->is_admin_message == self::ADMIN_MESSAGE_YES;
    }

    /**
     * Check if message is deleted
     *
     * @return bool
     */
    public function isDeleted()
    {
        return $this->is_deleted == self::DELETED_YES;
    }

    /**
     * Soft delete the message
     *
     * @return bool
     */
    public function softDelete()
    {
        $this->is_deleted = self::DELETED_YES;
        return $this->save(false);
    }

    /**
     * Restore the deleted message
     *
     * @return bool
     */
    public function restore()
    {
        $this->is_deleted = self::DELETED_NO;
        return $this->save(false);
    }

    /**
     * Get recent messages for a group
     *
     * @param int $groupId
     * @param int $limit
     * @return LiveChatUserMessage[]
     */
    public static function getRecentMessages($groupId, $limit = 10)
    {
        return self::find()
            ->where(['group_id' => $groupId, 'is_deleted' => self::DELETED_NO])
            ->orderBy(['created_at' => SORT_DESC])
            ->limit($limit)
            ->all();
    }
}

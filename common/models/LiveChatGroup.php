<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "live_chat_group".
 *
 * @property int $id
 * @property string $entity Entity type: college, exam, board, article, news
 * @property int $entity_id ID of the entity
 * @property string $group_name Name of the chat group
 * @property string|null $group_link GetStream API link for chat group
 * @property string|null $channel_id GetStream channel ID
 * @property string|null $channel_type GetStream channel type
 * @property string|null $description Group description
 * @property int|null $is_active_on_news Show on news pages: 0=No, 1=Yes
 * @property int|null $is_active_on_articles Show on article pages: 0=No, 1=Yes
 * @property int|null $status Group status: 0=Inactive, 1=Active
 * @property string $created_at
 * @property string $updated_at
 *
 * @property LiveChatUserMessage[] $liveChatUserMessages
 * @property College $college
 * @property Exam $exam
 * @property Board $board
 * @property Article $article
 * @property News $news
 */
class LiveChatGroup extends ActiveRecord
{
    public $pageName;

    // Entity type constants
    // const ENTITY_COLLEGE = 'college';
    const ENTITY_EXAM = 'exam';
    const ENTITY_BOARD = 'board';
    const ENTITY_ARTICLE = 'article';
    const ENTITY_NEWS = 'news';

    // Status constants
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_DELETED = 2;

    // Channel type constants
    const CHANNEL_TYPE_LIVESTREAM = 'livestream';
    const CHANNEL_TYPE_MESSAGING = 'messaging';
    const CHANNEL_TYPE_TEAM = 'team';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'live_chat_group';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => function () {
                    return date('Y-m-d H:i:s');
                },
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();

        // Set default values
        if ($this->isNewRecord) {
            $this->channel_type = $this->channel_type ?: self::CHANNEL_TYPE_MESSAGING;
            $this->status = $this->status ?: self::STATUS_ACTIVE;
            $this->is_active_on_articles = $this->is_active_on_articles ?: 1;
            $this->is_active_on_news = $this->is_active_on_news ?: 0;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            // Auto-generate channel_id if not provided
            if (empty($this->channel_id) && $insert) {
                $this->channel_id = $this->generateChannelId();
            }
            return true;
        }
        return false;
    }

    /**
     * Generate unique channel ID
     * @return string
     */
    private function generateChannelId()
    {
        return strtolower($this->entity . '_' . $this->entity_id . '_' . time());
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['entity', 'entity_id', 'channel_type', 'group_name'], 'required'],
            [['entity_id', 'is_active_on_news', 'is_active_on_articles', 'status'], 'integer'],
            [['description'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['entity'], 'string', 'max' => 20],
            [['entity'], 'in', 'range' => [
                // self::ENTITY_COLLEGE,
                self::ENTITY_EXAM,
                self::ENTITY_BOARD,
                self::ENTITY_ARTICLE,
                self::ENTITY_NEWS
            ]],
            [['group_link', 'channel_id'], 'string', 'max' => 255],
            [['channel_type', 'group_name'], 'string', 'max' => 50],
            [['channel_type'], 'in', 'range' => [
                self::CHANNEL_TYPE_LIVESTREAM,
                self::CHANNEL_TYPE_MESSAGING,
                self::CHANNEL_TYPE_TEAM
            ], 'skipOnEmpty' => false],
            [['status'], 'in', 'range' => [self::STATUS_INACTIVE, self::STATUS_ACTIVE, self::STATUS_DELETED]],
            [['is_active_on_news', 'is_active_on_articles'], 'in', 'range' => [0, 1]],
            [['entity', 'entity_id'], 'unique', 'targetAttribute' => ['entity', 'entity_id']],
            [['channel_id'], 'unique', 'skipOnEmpty' => true],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'entity' => 'Entity Type',
            'entity_id' => 'Entity',
            'group_name' => 'Channel Name',
            'group_link' => 'Group Link',
            'channel_id' => 'Channel ID',
            'channel_type' => 'Channel Type',
            'description' => 'Description',
            'is_active_on_news' => 'Show on News Pages',
            'is_active_on_articles' => 'Show on Article Pages',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[LiveChatUserMessages]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getLiveChatUserMessages()
    {
        return $this->hasMany(LiveChatUserMessage::class, ['group_id' => 'id'])
            ->where(['is_deleted' => 0]);
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::class, ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[Exam]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getExam()
    {
        return $this->hasOne(Exam::class, ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[Board]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoard()
    {
        return $this->hasOne(Board::class, ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[Article]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getArticle()
    {
        return $this->hasOne(Article::class, ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[News]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getNews()
    {
        return $this->hasOne(News::class, ['id' => 'entity_id']);
    }

    /**
     * Get entity types array for dropdown
     *
     * @return array
     */
    public static function getEntityTypes()
    {
        return [
            // self::ENTITY_COLLEGE => 'College',
            self::ENTITY_EXAM => 'Exam',
            self::ENTITY_BOARD => 'Board',
            self::ENTITY_ARTICLE => 'Article',
            self::ENTITY_NEWS => 'News',
        ];
    }

    /**
     * Get status types array for dropdown
     *
     * @return array
     */
    public static function getStatusTypes()
    {
        return [
            self::STATUS_INACTIVE => 'Inactive',
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_DELETED => 'Deleted',
        ];
    }

    /**
     * Get channel types array for dropdown
     *
     * @return array
     */
    public static function getChannelTypes()
    {
        return [
            self::CHANNEL_TYPE_MESSAGING => 'Messaging',
            self::CHANNEL_TYPE_LIVESTREAM => 'Livestream',
            self::CHANNEL_TYPE_TEAM => 'Team',
        ];
    }

    /**
     * Get entity name based on entity type and ID
     *
     * @return string|null
     */
    public function getEntityName()
    {
        switch ($this->entity) {
            case self::ENTITY_EXAM:
                $exam = $this->entity == self::ENTITY_EXAM ? $this->exam : null;
                return $exam ? $exam->display_name ?? $exam->name : null;
            case self::ENTITY_BOARD:
                $board = $this->entity == self::ENTITY_BOARD ? $this->board : null;
                return $board ? $board->display_name ?? $board->name : null;
            case self::ENTITY_ARTICLE:
                $article = $this->entity == self::ENTITY_ARTICLE ? $this->article : null;
                return $article ? $article->title : null;
            case self::ENTITY_NEWS:
                $news = $this->entity == self::ENTITY_NEWS ? $this->news : null;
                return $news ? $news->name : null;
            default:
                return null;
        }
    }

    public function getPageName()
    {
        if (!$this->entity_type || !$this->entity_id) {
            return null;
        }

        $entityFieldsArr = [
            'article'  => ['id', 'title as name'],
            'board'    => ['id', 'display_name as name'],
            'college'  => ['id', 'name as name'],
            'exam'     => ['id', 'display_name as name'],
            'course'   => ['id', 'name as name'],
            'career'   => ['id', 'name as name'],
            'olympiad' => ['id', 'name as name'],
            'NcertArticles' => ['id', 'title as name'],
            'NewsSubdomain' => ['id', 'name as name'],
            'others'   => ['id', 'name as name'],
        ];

        $entity = $this->entity_type;
        if ($entity == 'articles') {
            $entity = 'article';
        }
        if ($entity == 'ncert') {
            $entity = 'NcertArticles';
        }
        if ($entity == 'news') {
            $entity = 'NewsSubdomain';
        }

        if (!isset($entityFieldsArr[$entity])) {
            return null;
        }

        [$id, $name] = $entityFieldsArr[$entity];
        $modelClass = '\common\models\\' . ucfirst($entity);

        $row = $modelClass::find()
            ->select([$id, $name])
            ->where([$id => $this->entity_id])
            ->one();

        return $row ? [['id' => $row->id, 'name' => $row->name ?? $row->title]] : null;
    }


    /**
     * Get status label
     *
     * @return string
     */
    public function getStatusLabel()
    {
        $statuses = self::getStatusTypes();
        return isset($statuses[$this->status]) ? $statuses[$this->status] : 'Unknown';
    }

    /**
     * Get entity type label
     *
     * @return string
     */
    public function getEntityTypeLabel()
    {
        $types = self::getEntityTypes();
        return isset($types[$this->entity]) ? $types[$this->entity] : 'Unknown';
    }

    /**
     * Soft delete implementation
     */
    public function softDelete()
    {
        $this->status = self::STATUS_DELETED;
        return $this->save(false);
    }
}

<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use common\services\CacheClearService;

/**
 * This is the model class for table "college_program_content".
 *
 * @property int $id
 * @property int|null $college_course_id
 * @property string|null $qualification
 * @property string|null $content
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property string|null $h1
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class CollegeProgramContent extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    public $exams;
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'college_program_content';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_course_id', 'status', 'template_id'], 'integer'],
            [['content','editor_remark'], 'string'],
            [['created_at', 'updated_at', 'qualification'], 'safe'],
            [['meta_title', 'meta_description', 'h1'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'college_course_id' => 'College Course ID',
            'qualification' => 'Qualification',
            'content' => 'Content',
            'meta_title' => 'Meta Title',
            'meta_description' => 'Meta Description',
            'h1' => 'H1',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[CollegeCourse]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeCourseQuery
     */

    public function getCollegeCourse()
    {
        return $this->hasOne(CollegeProgram::className(), ['id' => 'college_course_id']);
    }

    public function getContentTemplate()
    {
        return $this->hasOne(ContentTemplate::className(), ['id' => 'template_id']);
    }


    public function getProgramName()
    {
        $programName = CollegeProgram::find()->where(['id' => $this->collegeCourse->id])->one();

        $data[] = [
            'id' => $programName-> program->id,
            'name' => $programName->program->name,
        ];

        return $data ?? [];
    }

    public function beforeSave($insert)
    {
        $items = [];
        if (!empty($this->qualification)) {
            $items['eligibility'] = $this->qualification;
        }
        if (!empty($this->exams)) {
            $items['exams'] = $this->exams;
        }

        if (!empty($items)) {
            $this->qualification = json_encode($items);
        } else {
            $this->qualification = '""';
        }

        return parent::beforeSave($insert);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\CollegeProgramContentQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\CollegeProgramContentQuery(get_called_class());
    }

    public function afterSave($insert, $changedAttributes)
    {
        // CacheClearService::entityContent(College::ENTITY_COLLEGE, 'courses-fees', $this->collegeCourse->program->slug, $this->collegeCourse->college->slug, $this->id, 'pi');

        return parent::afterSave($insert, $changedAttributes);
    }
}

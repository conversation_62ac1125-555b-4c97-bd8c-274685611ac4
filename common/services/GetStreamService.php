<?php

namespace common\services;

use common\helpers\ContentHelper;
use Yii;
use GetStream\StreamChat\Client as StreamClient;
use GetStream\StreamChat\StreamException;
use common\models\LiveChatGroup;
use common\models\LiveChatUserMessage;
use yii\base\Component;
use yii\base\InvalidConfigException;

/**
 * GetStream Service for managing chat functionality
 */
class GetStreamService extends Component
{
    /**
     * @var string GetStream API Key
     */
    public $apiKey;

    /**
     * @var string GetStream API Secret
     */
    public $apiSecret;

    /**
     * @var string GetStream App ID
     */
    public $appId;

    /**
     * @var bool Whether GetStream is enabled
     */
    public $enabled = true;

    /**
     * @var StreamClient GetStream client instance
     */
    private $_client;

    /**
     * Initialize the service
     * @throws InvalidConfigException
     */
    public function init()
    {
        parent::init();

        // Only validate credentials if service is enabled
        if ($this->enabled) {
            if (empty($this->apiKey) || $this->apiKey === 'YOUR_API_KEY_HERE') {
                Yii::warning('GetStream API Key not configured - service disabled', __METHOD__);
                $this->enabled = false;
            }

            if (empty($this->apiSecret) || $this->apiSecret === 'YOUR_API_SECRET_HERE') {
                Yii::warning('GetStream API Secret not configured - service disabled', __METHOD__);
                $this->enabled = false;
            }
        }
    }

    /**
     * Check if GetStream service is available
     * @return bool
     */
    public function isAvailable()
    {
        return $this->enabled && !empty($this->apiKey) && !empty($this->apiSecret);
    }

    /**
     * Get GetStream client instance
     * @return StreamClient|null
     */
    public function getClient()
    {
        if (!$this->enabled) {
            return null;
        }

        if ($this->_client === null) {
            $this->_client = new StreamClient($this->apiKey, $this->apiSecret);
        }

        return $this->_client;
    }

    /**
     * Create a user token for GetStream authentication
     * @param string $userId
     * @param array $extraData
     * @return string|null
     */
    public function createUserToken($userId, $extraData = [])
    {
        $client = $this->getClient();
        if (!$client) {
            return null;
        }
        return $client->createToken($userId, null, $extraData);
    }

    /**
     * Update/Create user in GetStream (version 1.0 uses updateUsers)
     * @param string $userId
     * @param array $userData
     * @return array
     */
    public function upsertUser($userId, $userData = [])
    {
        $defaultData = [
            'id' => $userId,
            'role' => 'user',
        ];

        $userData = array_merge($defaultData, $userData);

        return $this->getClient()->updateUsers([$userData]);
    }

    /**
     * Get app settings
     * @return array
     */
    public function getAppSettings()
    {
        return $this->getClient()->getAppSettings();
    }

    /**
     * Update app settings
     * @param array $settings
     * @return array
     */
    public function updateAppSettings($settings)
    {
        return $this->getClient()->updateAppSettings($settings);
    }

    /**
     * Get channel types
     * @return array
     */
    public function getChannelTypes()
    {
        $client = $this->getClient();
        if (!$client) {
            return [];
        }

        // Note: getChannelTypes() might not exist in all SDK versions
        // Use listChannelTypes() or similar method based on your SDK version
        try {
            return $client->listChannelTypes();
        } catch (\Exception $e) {
            Yii::error('Failed to get channel types: ' . $e->getMessage(), __METHOD__);
            return [];
        }
    }

    /**
     * Create or update channel type
     * @param string $name
     * @param array $settings
     * @return array
     */
    public function createChannelType($name, $settings = [])
    {
        return $this->getClient()->createChannelType($name, $settings);
    }

    public function updateMessage($message)
    {
        return $this->getClient()->updateMessage($message);
    }

    /**
     * Delete channel type
     * @param string $name
     * @return array
     */
    public function deleteChannelType($name)
    {
        return $this->getClient()->deleteChannelType($name);
    }

    /**
     * Delete channel
     * @param string $channelType
     * @param string $channelId
     * @param array $options
     * @return array
     */
    public function deleteChannel($channelType, $channelId, $options = [])
    {
        $client = $this->getClient();
        if (!$client) {
            Yii::warning('GetStream client not available - cannot delete channel', __METHOD__);
            return false;
        }

        try {
            $result = $this->deleteChannelStream($channelType, $channelId, $options);

            Yii::info("GetStream channel deleted successfully: {$channelType}:{$channelId}", __METHOD__);
            return $result;
        } catch (\Exception $e) {
            Yii::error("Failed to delete GetStream channel {$channelType}:{$channelId} - " . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    //delete channel
    public function deleteChannelStream($channelType, $channelId, $options = [])
    {
        $client = $this->getClient();
        if (!$client) {
            Yii::warning('GetStream client not available - cannot delete channel', __METHOD__);
            return false;
        }

        $result = $client->delete('channels/' . $channelType . '/' . $channelId, $options);

        return $result;
    }

    /**
     * Create or get a channel
     * @param string $channelType
     * @param string $channelId
     * @param array $channelData
     * @param string $createdByUserId
     * @return array|false
     */
    public function createChannel($channelType, $channelId, $channelData = [], $createdByUserId = null)
    {
        $client = $this->getClient();
        if (!$client) {
            Yii::warning('GetStream client not available - cannot create channel', __METHOD__);
            return false;
        }

        try {
            $channel = $client->Channel($channelType, $channelId);

            $data = array_merge([
                'name' => $channelData['name'] ?? $channelId,
            ], $channelData);

            if ($createdByUserId) {
                $data['created_by_id'] = $createdByUserId;
            }

            return $channel->create($createdByUserId, $data);
        } catch (\Exception $e) {
            Yii::error('Failed to create GetStream channel: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Update channel information
     * @param string $channelType
     * @param string $channelId
     * @param array $updateData
     * @return array
     */
    public function updateChannel($channelType, $channelId, $updateData = [])
    {
        $channel = $this->getClient()->Channel($channelType, $channelId);
        return $channel->update($updateData);
    }

    /**
     * Add members to a channel
     * @param string $channelType
     * @param string $channelId
     * @param array $userIds
     * @return array
     */
    public function addMembers($channelType, $channelId, $userIds = [])
    {
        $channel = $this->getClient()->Channel($channelType, $channelId);
        return $channel->addMembers($userIds);
    }

    /**
     * Remove members from a channel
     * @param string $channelType
     * @param string $channelId
     * @param array $userIds
     * @return array
     */
    public function removeMembers($channelType, $channelId, $userIds = [])
    {
        $channel = $this->getClient()->Channel($channelType, $channelId);
        return $channel->removeMembers($userIds);
    }

    public function sendAdminMessageWithAttachments(LiveChatGroup $group, $messageText, $attachments = [], $adminUserId = 'admin')
    {
        try {
            if (empty($group->channel_id)) {
                throw new \Exception('Channel ID not found for group');
            }
            
            $channelType = $group->channel_type ?: LiveChatGroup::CHANNEL_TYPE_MESSAGING;
            
            // Prepare GetStream message data
            $messageData = [
                'text' => !empty(trim(strip_tags($messageText))) ? $messageText : 'Files attached',
                'type' => 'system',
                'admin_message' => true,
            ];
            
            // Add attachments to GetStream format if present
            if (!empty($attachments)) {
                $messageData['attachments'] = $this->formatAttachmentsForGetStream($attachments);
            }
            
            // Send to GetStream
            $result = $this->sendMessage($channelType, $group->channel_id, $messageData, $adminUserId);
            
            // Store message in local database (same structure as frontend messages)
            $now = date('Y-m-d H:i:s');
            $messageType = !empty($attachments) ? LiveChatUserMessage::MESSAGE_TYPE_FILE : LiveChatUserMessage::MESSAGE_TYPE_TEXT;
            
            $message = new LiveChatUserMessage();
            $message->group_id = $group->id;
            $message->user_id = 1; // Admin user ID
            $message->message_id = ($result && isset($result['message']['id'])) ? $result['message']['id'] : 'admin_' . time() . '_' . rand(1000, 9999);
            $message->message_text = $messageText;
            $message->message_type = $messageType;
            $message->attachments = json_encode($attachments, JSON_UNESCAPED_SLASHES);
            $message->user_name = 'Admin';
            $message->user_email = '<EMAIL>';
            $message->is_admin_message = LiveChatUserMessage::ADMIN_MESSAGE_YES;
            $message->is_deleted = 0;
            $message->created_at = $now;
            $message->updated_at = $now;
            $message->save();
            
            return $result !== false ? $result : true;
        } catch (\Exception $e) {
            Yii::error('Failed to send admin message with attachments: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Format attachments for GetStream API (same as frontend)
     */
    private function formatAttachmentsForGetStream($attachments)
    {
        $getStreamAttachments = [];
        
        foreach ($attachments as $attachment) {
            $type = 'file'; // default type
            
            if (strpos($attachment['type'], 'image/') === 0) {
                $type = 'image';
            } elseif (strpos($attachment['type'], 'video/') === 0) {
                $type = 'video';
            }
            
            $getStreamAttachments[] = [
                'type' => $type,
                'asset_url' => $attachment['url'],
                'title' => $attachment['name'],
                'mime_type' => $attachment['type']
            ];
        }
        
        return $getStreamAttachments;
    }

    /**
     * Send a message to a channel
     * @param string $channelType
     * @param string $channelId
     * @param array $messageData
     * @param string $userId
     * @return array|false
     */
    public function sendMessage($channelType, $channelId, $messageData, $userId)
    {
        $client = $this->getClient();
        if (!$client) {
            Yii::warning("GetStream client not available - cannot send message. Channel: {$channelId}, User: {$userId}", __METHOD__);
            return false;
        }

        try {
            Yii::info("Attempting to send message to GetStream - Channel: {$channelId}, User: {$userId}, Text: " . substr($messageData['text'] ?? '', 0, 50), __METHOD__);

            $channel = $client->Channel($channelType, $channelId);
            $message = array_merge([
                'text' => $messageData['text'] ?? '',
            ], $messageData);

            $result = $channel->sendMessage($message, $userId);

            if ($result) {
                Yii::info('✅ Message sent successfully to GetStream - Message ID: ' . ($result['message']['id'] ?? 'unknown'), __METHOD__);
            }

            return $result;
        } catch (\Exception $e) {
            Yii::error("❌ Failed to send GetStream message - Channel: {$channelId}, Error: " . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Delete a message
     * @param string $messageId
     * @param array $options
     * @return array|false
     */
    public function deleteMessage($messageId, $options = [])
    {
        try {
            return $this->getClient()->deleteMessage($messageId, $options);
        } catch (StreamException $e) {
            Yii::error('Failed to delete message: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Get channel messages
     * @param string $channelType
     * @param string $channelId
     * @param array $options
     * @return array
     */
    public function getMessages($channelType, $channelId, $options = [])
    {
        $channel = $this->getClient()->Channel($channelType, $channelId);

        $defaultOptions = [
            'messages' => [
                'limit' => 50,
                'offset' => 0,
            ],
        ];

        if (isset($options['limit'])) {
            $defaultOptions['messages']['limit'] = $options['limit'];
        }
        if (isset($options['offset'])) {
            $defaultOptions['messages']['offset'] = $options['offset'];
        }
        if (isset($options['created_at_after'])) {
            $defaultOptions['messages']['created_at_after'] = $options['created_at_after'];
        }

        $result = $channel->query($defaultOptions);

        return [
            'messages' => $result['messages'] ?? [],
            'channel' => $result['channel'] ?? []
        ];
    }

    /**
     * Create GetStream channel for LiveChatGroup
     * @param LiveChatGroup $group
     * @param string $createdByUserId
     * @return array|false
     */
    public function createChannelForGroup(LiveChatGroup $group, $createdByUserId = 'admin')
    {
        try {
            $channelId = $this->generateChannelId($group);
            $channelType = $group->channel_type ?: LiveChatGroup::CHANNEL_TYPE_MESSAGING;

            $channelData = [
                'name' => $group->group_name,
                'description' => $group->description,
                'entity_type' => $group->entity,
                'entity_id' => $group->entity_id,
                'group_id' => $group->id,
            ];

            $result = $this->createChannel($channelType, $channelId, $channelData, $createdByUserId);

            // Update the group with channel information
            $group->channel_id = $channelId;
            $group->group_link = $this->generateChannelUrl($channelType, $channelId);
            $group->save(false);

            return $result;
        } catch (\Exception $e) {
            Yii::error('Failed to create GetStream channel: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Send admin message to group channel
     * @param LiveChatGroup $group
     * @param string $messageText
     * @param string $adminUserId
     * @return array|false
     */
    public function sendAdminMessage(LiveChatGroup $group, $messageText, $adminUserId = 'admin')
    {
        try {
            if (empty($group->channel_id)) {
                throw new \Exception('Channel ID not found for group');
            }

            $channelType = $group->channel_type ?: LiveChatGroup::CHANNEL_TYPE_MESSAGING;
            $messageData = [
                'text' => $messageText,
                'type' => 'system',
                'admin_message' => true,
            ];

            $result = $this->sendMessage($channelType, $group->channel_id, $messageData, $adminUserId);

            // Store message in local database regardless of GetStream result
            $message = new LiveChatUserMessage();
            $message->group_id = $group->id;
            $message->message_id = ($result && isset($result['message']['id'])) ? $result['message']['id'] : 'admin_' . time() . '_' . rand(1000, 9999);
            $message->message_text = $messageText;
            $message->message_type = LiveChatUserMessage::MESSAGE_TYPE_TEXT;
            $message->user_name = 'Admin';
            $message->user_id = 1;
            $message->user_email = '<EMAIL>';
            $message->is_admin_message = LiveChatUserMessage::ADMIN_MESSAGE_YES;
            $message->save();

            // Return true if message was saved locally, even if GetStream failed
            return $result !== false ? $result : true;
        } catch (\Exception $e) {
            Yii::error('Failed to send admin message: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Generate unique channel ID for a group
     * @param LiveChatGroup $group
     * @return string
     */
    private function generateChannelId(LiveChatGroup $group)
    {
        return strtolower($group->entity . '_' . $group->entity_id . '_' . $group->id);
    }

    /**
     * Generate channel URL
     * @param string $channelType
     * @param string $channelId
     * @return string
     */
    private function generateChannelUrl($channelType, $channelId)
    {
        return "https://chat.stream-io-api.com/channels/{$channelType}/{$channelId}";
    }

    /**
     * Sync message from GetStream webhook
     * @param array $messageData
     * @return LiveChatUserMessage|false
     */
    public function syncMessage($messageData)
    {
        try {
            // Find the group by channel ID
            $channelId = $messageData['channel']['id'] ?? null;
            if (!$channelId) {
                throw new \Exception('Channel ID not found in message data');
            }

            $group = LiveChatGroup::findOne(['channel_id' => $channelId]);
            if (!$group) {
                throw new \Exception('Group not found for channel ID: ' . $channelId);
            }

            // Check if message already exists
            $messageId = $messageData['message']['id'] ?? null;
            if (!$messageId) {
                throw new \Exception('Message ID not found');
            }

            $existingMessage = LiveChatUserMessage::findOne(['message_id' => $messageId]);
            if ($existingMessage) {
                return $existingMessage; // Message already synced
            }

            // Create new message record
            $message = new LiveChatUserMessage();
            $message->group_id = $group->id;
            $message->message_id = $messageId;
            $message->message_text = $messageData['message']['text'] ?? '';
            $message->message_type = $messageData['message']['type'] ?? LiveChatUserMessage::MESSAGE_TYPE_TEXT;
            $message->user_name = $messageData['user']['name'] ?? $messageData['user']['id'] ?? 'Unknown';
            $message->user_email = $messageData['user']['email'] ?? null;
            $message->is_admin_message = isset($messageData['message']['admin_message']) ?
                LiveChatUserMessage::ADMIN_MESSAGE_YES : LiveChatUserMessage::ADMIN_MESSAGE_NO;

            if ($message->save()) {
                return $message;
            } else {
                throw new \Exception('Failed to save message: ' . json_encode($message->getErrors()));
            }
        } catch (\Exception $e) {
            Yii::error('Failed to sync message: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }
}

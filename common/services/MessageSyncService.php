<?php

namespace common\services;

use Yii;
use common\models\LiveChatGroup;
use common\models\LiveChatUserMessage;
use yii\base\Component;

/**
 * Service to sync messages from GetStream to local database
 */
class MessageSyncService extends Component
{
    /**
     * Sync messages for all active groups
     * This method should be called every 5 minutes via cron job
     */
    public function syncAllGroupMessages()
    {
        $activeGroups = LiveChatGroup::find()
            ->where(['status' => LiveChatGroup::STATUS_ACTIVE])
            ->andWhere(['!=', 'channel_id', ''])
            ->all();

        $totalSynced = 0;
        $errors = [];

        foreach ($activeGroups as $group) {
            try {
                $synced = $this->syncGroupMessages($group);
                $totalSynced += $synced;
                Yii::info("Synced {$synced} messages for group {$group->id}", __METHOD__);
            } catch (\Exception $e) {
                $error = "Failed to sync messages for group {$group->id}: " . $e->getMessage();
                $errors[] = $error;
                Yii::error($error, __METHOD__);
            }
        }

        Yii::info("Message sync completed. Total synced: {$totalSynced}, Errors: " . count($errors), __METHOD__);
        
        return [
            'total_synced' => $totalSynced,
            'errors' => $errors,
            'groups_processed' => count($activeGroups)
        ];
    }

    /**
     * Sync messages for a specific group
     * @param LiveChatGroup $group
     * @return int Number of messages synced
     */
    public function syncGroupMessages(LiveChatGroup $group)
    {
        if (empty($group->channel_id)) {
            throw new \Exception("Group {$group->id} has no channel_id");
        }

        /** @var GetStreamService $getStreamService */
        $getStreamService = Yii::$app->getStreamService;
        
        // Get the last synced message timestamp for this group
        $lastMessage = LiveChatUserMessage::find()
            ->where(['group_id' => $group->id])
            ->orderBy(['created_at' => SORT_DESC])
            ->one();

        $since = null;
        if ($lastMessage) {
            // Get messages since the last synced message
            $since = date('c', strtotime($lastMessage->created_at));
        }

        // Get messages from GetStream
        $channelType = $group->channel_type ?: LiveChatGroup::CHANNEL_TYPE_MESSAGING;
        $options = [
            'limit' => 100, // Get up to 100 messages
        ];
        
        if ($since) {
            $options['created_at_after'] = $since;
        }

        $response = $getStreamService->getMessages($channelType, $group->channel_id, $options);
        
        if (!$response || !isset($response['messages'])) {
            return 0;
        }

        $messages = $response['messages'];
        $syncedCount = 0;

        foreach ($messages as $messageData) {
            try {
                if ($this->saveMessageToDatabase($group, $messageData)) {
                    $syncedCount++;
                }
            } catch (\Exception $e) {
                Yii::error("Failed to save message {$messageData['id']}: " . $e->getMessage(), __METHOD__);
            }
        }

        return $syncedCount;
    }

    /**
     * Save a GetStream message to local database
     * @param LiveChatGroup $group
     * @param array $messageData
     * @return bool
     */
    private function saveMessageToDatabase(LiveChatGroup $group, $messageData)
    {
        // Check if message already exists
        $existingMessage = LiveChatUserMessage::findOne(['message_id' => $messageData['id']]);
        if ($existingMessage) {
            return false; // Already exists
        }

        $message = new LiveChatUserMessage();
        $message->group_id = $group->id;
        $message->message_id = $messageData['id'];
        $message->message_text = $messageData['text'] ?? '';
        $message->message_type = $this->getMessageType($messageData);
        
        // Extract user information from GetStream message
        $user = $messageData['user'] ?? [];
        $message->user_name = $user['name'] ?? $user['id'] ?? 'Unknown User';
        $message->user_email = $user['email'] ?? null;
        $message->user_id = $user['id'] ?? null; // Store GetStream user ID
        
        // Check if it's an admin message
        $message->is_admin_message = $this->isAdminMessage($messageData) ?
            LiveChatUserMessage::ADMIN_MESSAGE_YES : LiveChatUserMessage::ADMIN_MESSAGE_NO;
        
        // Set timestamps from GetStream
        if (isset($messageData['created_at'])) {
            $message->created_at = date('Y-m-d H:i:s', strtotime($messageData['created_at']));
        }
        
        if (isset($messageData['updated_at'])) {
            $message->updated_at = date('Y-m-d H:i:s', strtotime($messageData['updated_at']));
        }

        return $message->save();
    }

    /**
     * Determine message type from GetStream message data
     * @param array $messageData
     * @return string
     */
    private function getMessageType($messageData)
    {
        if (isset($messageData['type'])) {
            switch ($messageData['type']) {
                case 'system':
                    return LiveChatUserMessage::MESSAGE_TYPE_SYSTEM;
                case 'error':
                    return LiveChatUserMessage::MESSAGE_TYPE_SYSTEM;
                default:
                    break;
            }
        }

        // Check for attachments to determine type
        if (isset($messageData['attachments']) && !empty($messageData['attachments'])) {
            $attachment = $messageData['attachments'][0];
            $type = $attachment['type'] ?? 'file';
            
            switch ($type) {
                case 'image':
                    return LiveChatUserMessage::MESSAGE_TYPE_IMAGE;
                case 'video':
                    return LiveChatUserMessage::MESSAGE_TYPE_VIDEO;
                case 'audio':
                    return LiveChatUserMessage::MESSAGE_TYPE_AUDIO;
                case 'file':
                default:
                    return LiveChatUserMessage::MESSAGE_TYPE_FILE;
            }
        }

        return LiveChatUserMessage::MESSAGE_TYPE_TEXT;
    }

    /**
     * Check if message is from admin
     * @param array $messageData
     * @return bool
     */
    private function isAdminMessage($messageData)
    {
        $user = $messageData['user'] ?? [];
        
        // Check if user role is admin
        if (isset($user['role']) && $user['role'] === 'admin') {
            return true;
        }
        
        // Check if message has admin flag
        if (isset($messageData['admin_message']) && $messageData['admin_message']) {
            return true;
        }
        
        // Check if user ID indicates admin
        $userId = $user['id'] ?? '';
        if (in_array($userId, ['admin', 'system', 'bot'])) {
            return true;
        }
        
        return false;
    }

    /**
     * Get user ID from GetStream message
     * This extracts the actual GetStream user ID, not our local database user ID
     * @param array $messageData
     * @return string|null
     */
    public function getStreamUserId($messageData)
    {
        $user = $messageData['user'] ?? [];
        return $user['id'] ?? null;
    }

    /**
     * Get user information from GetStream message
     * @param array $messageData
     * @return array
     */
    public function getStreamUserInfo($messageData)
    {
        $user = $messageData['user'] ?? [];
        
        return [
            'user_id' => $user['id'] ?? null,
            'name' => $user['name'] ?? null,
            'email' => $user['email'] ?? null,
            'image' => $user['image'] ?? null,
            'role' => $user['role'] ?? 'user',
            'online' => $user['online'] ?? false,
            'created_at' => isset($user['created_at']) ? date('Y-m-d H:i:s', strtotime($user['created_at'])) : null,
            'updated_at' => isset($user['updated_at']) ? date('Y-m-d H:i:s', strtotime($user['updated_at'])) : null,
        ];
    }

    /**
     * Clean up old messages (optional - for maintenance)
     * @param int $daysOld
     * @return int Number of messages deleted
     */
    public function cleanupOldMessages($daysOld = 90)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));
        
        $deletedCount = LiveChatUserMessage::deleteAll([
            'and',
            ['<', 'created_at', $cutoffDate],
            ['is_deleted' => LiveChatUserMessage::DELETED_YES]
        ]);

        Yii::info("Cleaned up {$deletedCount} old messages older than {$daysOld} days", __METHOD__);
        
        return $deletedCount;
    }
}

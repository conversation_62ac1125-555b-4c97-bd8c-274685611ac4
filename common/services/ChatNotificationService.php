<?php

namespace common\services;

use Yii;
use common\models\LiveChatGroup;
use common\models\LiveChatUserMessage;
use yii\base\Component;
use yii\helpers\Html;

/**
 * Service for handling chat notifications and retention mechanisms
 */
class ChatNotificationService extends Component
{
    /**
     * @var int Threshold for "many new messages" notification
     */
    public $messageThreshold = 15;

    /**
     * Send email notification when someone replies to user's query
     * @param LiveChatUserMessage $originalMessage
     * @param LiveChatUserMessage $replyMessage
     * @return bool
     */
    public function sendReplyNotification($originalMessage, $replyMessage)
    {
        if (!$originalMessage->user_email) {
            return false; // No email to send to
        }

        $group = $originalMessage->group;
        if (!$group) {
            return false;
        }

        $subject = "Reply to your question in {$group->group_name}";
        
        $body = $this->renderEmailTemplate('reply-notification', [
            'originalMessage' => $originalMessage,
            'replyMessage' => $replyMessage,
            'group' => $group,
            'chatUrl' => $this->generateChatUrl($group)
        ]);

        return $this->sendEmail($originalMessage->user_email, $subject, $body);
    }

    /**
     * Send notification when user is mentioned with @username
     * @param string $userEmail
     * @param string $userName
     * @param LiveChatUserMessage $mentionMessage
     * @return bool
     */
    public function sendMentionNotification($userEmail, $userName, $mentionMessage)
    {
        if (!$userEmail) {
            return false;
        }

        $group = $mentionMessage->group;
        if (!$group) {
            return false;
        }

        $subject = "You were mentioned in {$group->group_name}";
        
        $body = $this->renderEmailTemplate('mention-notification', [
            'userName' => $userName,
            'mentionMessage' => $mentionMessage,
            'group' => $group,
            'chatUrl' => $this->generateChatUrl($group)
        ]);

        return $this->sendEmail($userEmail, $subject, $body);
    }

    /**
     * Send notification when message volume crosses threshold
     * @param string $userEmail
     * @param string $userName
     * @param LiveChatGroup $group
     * @param int $newMessageCount
     * @param string $lastVisit
     * @return bool
     */
    public function sendVolumeNotification($userEmail, $userName, $group, $newMessageCount, $lastVisit)
    {
        if (!$userEmail || $newMessageCount < $this->messageThreshold) {
            return false;
        }

        $subject = "{$newMessageCount} new messages in {$group->group_name}";
        
        $body = $this->renderEmailTemplate('volume-notification', [
            'userName' => $userName,
            'group' => $group,
            'newMessageCount' => $newMessageCount,
            'lastVisit' => $lastVisit,
            'chatUrl' => $this->generateChatUrl($group)
        ]);

        return $this->sendEmail($userEmail, $subject, $body);
    }

    /**
     * Check for users who need volume notifications
     * This should be run periodically (e.g., every hour)
     */
    public function processVolumeNotifications()
    {
        $activeGroups = LiveChatGroup::find()
            ->where(['status' => LiveChatGroup::STATUS_ACTIVE])
            ->all();

        $notificationsSent = 0;

        foreach ($activeGroups as $group) {
            // Get users who have participated in this group
            $participantEmails = LiveChatUserMessage::find()
                ->select(['user_email', 'user_name', 'user_id'])
                ->where([
                    'group_id' => $group->id,
                    'is_admin_message' => LiveChatUserMessage::ADMIN_MESSAGE_NO,
                    'is_deleted' => LiveChatUserMessage::DELETED_NO
                ])
                ->andWhere(['!=', 'user_email', ''])
                ->groupBy(['user_email'])
                ->asArray()
                ->all();

            foreach ($participantEmails as $participant) {
                $email = $participant['user_email'];
                $name = $participant['user_name'];
                $streamUserId = $participant['user_id'];

                // Get user's last message timestamp (simulate last visit)
                $lastMessage = LiveChatUserMessage::find()
                    ->where([
                        'group_id' => $group->id,
                        'user_id' => $streamUserId
                    ])
                    ->orderBy(['created_at' => SORT_DESC])
                    ->one();

                if (!$lastMessage) {
                    continue;
                }

                $lastVisit = $lastMessage->created_at;
                
                // Count new messages since last visit
                $newMessageCount = LiveChatUserMessage::find()
                    ->where([
                        'group_id' => $group->id,
                        'is_deleted' => LiveChatUserMessage::DELETED_NO
                    ])
                    ->andWhere(['>', 'created_at', $lastVisit])
                    ->andWhere(['!=', 'user_id', $streamUserId]) // Exclude user's own messages
                    ->count();

                if ($newMessageCount >= $this->messageThreshold) {
                    if ($this->sendVolumeNotification($email, $name, $group, $newMessageCount, $lastVisit)) {
                        $notificationsSent++;
                    }
                }
            }
        }

        Yii::info("Sent {$notificationsSent} volume notifications", __METHOD__);
        return $notificationsSent;
    }

    /**
     * Process mention notifications from a message
     * @param LiveChatUserMessage $message
     * @return int Number of notifications sent
     */
    public function processMentionNotifications($message)
    {
        $messageText = $message->message_text;
        $notificationsSent = 0;

        // Find @mentions in the message
        if (preg_match_all('/@(\w+)/', $messageText, $matches)) {
            $mentionedUsernames = $matches[1];

            foreach ($mentionedUsernames as $username) {
                // Find user by username in the same group
                $mentionedUser = LiveChatUserMessage::find()
                    ->where([
                        'group_id' => $message->group_id,
                        'user_name' => $username,
                        'is_deleted' => LiveChatUserMessage::DELETED_NO
                    ])
                    ->andWhere(['!=', 'user_email', ''])
                    ->one();

                if ($mentionedUser && $mentionedUser->user_email) {
                    if ($this->sendMentionNotification($mentionedUser->user_email, $username, $message)) {
                        $notificationsSent++;
                    }
                }
            }
        }

        return $notificationsSent;
    }

    /**
     * Generate chat URL for a group
     * @param LiveChatGroup $group
     * @return string
     */
    private function generateChatUrl($group)
    {
        $baseUrl = Yii::$app->params['frontendUrl'] ?? 'http://localhost';
        
        switch ($group->entity) {
            case LiveChatGroup::ENTITY_EXAM:
                $exam = $group->exam;
                return $exam ? "{$baseUrl}/exam/live-chat-demo/{$exam->slug}" : $baseUrl;
            case LiveChatGroup::ENTITY_BOARD:
                $board = $group->board;
                return $board ? "{$baseUrl}/board/{$board->slug}" : $baseUrl;
            default:
                return $baseUrl;
        }
    }

    /**
     * Render email template
     * @param string $template
     * @param array $params
     * @return string
     */
    private function renderEmailTemplate($template, $params)
    {
        switch ($template) {
            case 'reply-notification':
                return $this->renderReplyNotificationTemplate($params);
            case 'mention-notification':
                return $this->renderMentionNotificationTemplate($params);
            case 'volume-notification':
                return $this->renderVolumeNotificationTemplate($params);
            default:
                return '';
        }
    }

    /**
     * Render reply notification email template
     */
    private function renderReplyNotificationTemplate($params)
    {
        $originalMessage = $params['originalMessage'];
        $replyMessage = $params['replyMessage'];
        $group = $params['group'];
        $chatUrl = $params['chatUrl'];

        return "
        <h2>Someone replied to your question!</h2>
        
        <div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>
            <strong>Your question:</strong><br>
            " . Html::encode($originalMessage->message_text) . "
        </div>
        
        <div style='background: #e8f5e9; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>
            <strong>Reply from " . Html::encode($replyMessage->getSenderDisplayName()) . ':</strong><br>
            ' . Html::encode($replyMessage->message_text) . "
        </div>
        
        <p>Join the discussion to see more replies and continue the conversation:</p>
        
        <a href='{$chatUrl}' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>
            View Discussion
        </a>
        
        <p style='margin-top: 30px; color: #666; font-size: 12px;'>
            This notification was sent because you participated in the live discussion for {$group->group_name}.
        </p>
        ";
    }

    /**
     * Render mention notification email template
     */
    private function renderMentionNotificationTemplate($params)
    {
        $userName = $params['userName'];
        $mentionMessage = $params['mentionMessage'];
        $group = $params['group'];
        $chatUrl = $params['chatUrl'];

        return "
        <h2>You were mentioned in a discussion!</h2>
        
        <p>Hi {$userName},</p>
        
        <p>You were mentioned by <strong>" . Html::encode($mentionMessage->getSenderDisplayName()) . "</strong> in the live discussion:</p>
        
        <div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;'>
            " . Html::encode($mentionMessage->message_text) . "
        </div>
        
        <p>Join the discussion to respond:</p>
        
        <a href='{$chatUrl}' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>
            View Discussion
        </a>
        
        <p style='margin-top: 30px; color: #666; font-size: 12px;'>
            This notification was sent because you were mentioned in the live discussion for {$group->group_name}.
        </p>
        ";
    }

    /**
     * Render volume notification email template
     */
    private function renderVolumeNotificationTemplate($params)
    {
        $userName = $params['userName'];
        $group = $params['group'];
        $newMessageCount = $params['newMessageCount'];
        $chatUrl = $params['chatUrl'];

        return "
        <h2>{$newMessageCount} new messages since your last visit!</h2>
        
        <p>Hi {$userName},</p>
        
        <p>There's been a lot of activity in the <strong>{$group->group_name}</strong> discussion since your last visit.</p>
        
        <div style='background: #e3f2fd; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;'>
            <h3 style='color: #1976d2; margin: 0;'>{$newMessageCount} New Messages</h3>
            <p style='margin: 10px 0 0 0; color: #666;'>Don't miss out on the conversation!</p>
        </div>
        
        <p>Join the discussion to catch up on what you've missed:</p>
        
        <a href='{$chatUrl}' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>
            View Discussion
        </a>
        
        <p style='margin-top: 30px; color: #666; font-size: 12px;'>
            This notification was sent because you participated in the live discussion for {$group->group_name}.
        </p>
        ";
    }

    /**
     * Send email using Yii's mailer
     * @param string $to
     * @param string $subject
     * @param string $body
     * @return bool
     */
    private function sendEmail($to, $subject, $body)
    {
        try {
            return Yii::$app->mailer->compose()
                ->setTo($to)
                ->setFrom([Yii::$app->params['supportEmailUser'] => Yii::$app->name . ' robot'])
                ->setSubject($subject)
                ->setHtmlBody($body)
                ->send();
        } catch (\Exception $e) {
            Yii::error('Failed to send email notification: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }
}

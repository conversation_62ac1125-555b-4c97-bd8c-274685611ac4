<?php
return [
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'vendorPath' => dirname(dirname(__DIR__)) . '/vendor',
    'timeZone' => 'Asia/Kolkata',
    'bootstrap' => ['devicedetect'],
    'language' => 'en-US',
    'sourceLanguage' => 'en-UK',
    'components' => [
        'formatter' => [
            'class'           => 'yii\i18n\Formatter',
            'defaultTimeZone' => 'Asia/Kolkata',
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
            'cache' => 'cache'
        ],
        'devicedetect' => [
            'class' => 'alexandernst\devicedetect\DeviceDetect'
        ],
        'i18n' => [
            'translations' => [
                '*' => [
                    'class' => yii\i18n\DbMessageSource::class,
                    'sourceMessageTable' => '{{%source_message}}',
                    'messageTable' => '{{%message}}',
                    'enableCaching' => YII_ENV_DEV,
                    'cachingDuration' => 3600,
                    'on missingTranslation' => [backend\modules\translation\Module::class, 'missingTranslation']
                ],
            ],
        ],
        // 'log' => [
        //     'traceLevel' => YII_DEBUG ? 3 : 0,
        //     'targets' => [
        //         [
        //             'class' => 'common\components\EmailTarget',
        //             'levels' => ['error', 'warning'],
        //         ],
        //         'db' => [
        //             'class' => 'yii\log\DbTarget',
        //             'levels' => ['error', 'warning'],
        //             'except' => ['yii\web\HttpException:*', 'yii\i18n\I18N\*'],
        //             'prefix' => function () {
        //                 $url = !Yii::$app->request->isConsoleRequest ? Yii::$app->request->getUrl() : null;
        //                 return sprintf('[%s][%s]', Yii::$app->id, $url);
        //             },
        //             'logVars' => [],
        //             'logTable' => '{{%system_log}}'
        //         ]
        //     ],
        // ],
        'sysMailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            'viewPath' => '@common/mail',
            // send all mails to a file by default. You have to set
            // 'useFileTransport' to false and configure a transport
            // for the mailer to send real emails.
            'useFileTransport' => YII_DEBUG,
            'transport' => [
                'class' => 'Swift_SmtpTransport',
                'host' => 'mail.theuniversityinfo.com',
                'username' => '<EMAIL>',
                'password' => 'umermans45',
                'port' => '587',
                'encryption' => 'tls',
            ],
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'common\components\EmailTarget',
                    'mailer' => 'sysMailer',
                    'cache' => 'cache',
                    'levels' => ['error'],
                    'message' => [
                        'from' => ['<EMAIL>'],
                        'to' => ['<EMAIL>', '<EMAIL>',  '<EMAIL>', '<EMAIL>'],
                        'subject' => sprintf('Error Log [%s]', \Yii::$app->id ?? ''),
                    ],
                    'except' => [
                        'yii\web\HttpException:400',
                        'yii\web\HttpException:404',
                        'yii\web\HttpException:406',
                        'yii\web\HttpException:403',
                        'yii\i18n\*'
                    ],
                ],
            ],
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            'viewPath' => '@common/mail',
            // send all mails to a file by default. You have to set
            // 'useFileTransport' to false and configure a transport
            // for the mailer to send real emails.
            'useFileTransport' => false,
            'transport' => [
                'class' => 'Swift_SmtpTransport',
                'host' => 'mail.theuniversityinfo.com',
                'username' => '<EMAIL>',
                'password' => 'umermans45',
                'port' => '587',
                'encryption' => 'tls',
            ],

        ],
        'getStreamService' => [
            'class' => 'common\services\GetStreamService',
            'apiKey' => 'ghxdk2q2n225', // ✅ Real API Key from GetStream logs
            'apiSecret' => '4zsn4wwd8euhpcwe9er4ugw2c47x6w3fpenru635rn5ugaev3vwns83sfd4m646t', // ❌ REPLACE WITH REAL SECRET FROM DASHBOARD
            'appId' => '1415657', // Your actual App ID
            'enabled' => true, // ✅ Enabled for testing
        ],
    ],
];

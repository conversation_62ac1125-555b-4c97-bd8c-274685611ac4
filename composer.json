{"name": "yiisoft/yii2-app-advanced", "description": "Yii 2 Advanced Project Template", "keywords": ["yii2", "framework", "advanced", "project template"], "homepage": "http://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=5.6.0", "yiisoft/yii2": "~2.0.14", "yiisoft/yii2-bootstrap": "~2.0.0", "yiisoft/yii2-swiftmailer": "~2.0.0 || ~2.1.0", "nesbot/carbon": "^2.41", "yiisoft/yii2-queue": "^2.3", "dmstr/yii2-adminlte-asset": "^2.6", "mdmsoft/yii2-admin": "~2.0", "kartik-v/yii2-widget-select2": "*", "yiisoft/yii2-mongodb": "^2.1", "vova07/yii2-imperavi-widget": "*", "2amigos/yii2-taggable-behavior": "~1.0", "2amigos/yii2-selectize-widget": "~1.0", "yiisoft/yii2-httpclient": "^2.0", "alexandernst/yii2-device-detect": "0.0.12", "drsdre/yii2-wordpress-api": "*", "unclead/yii2-multiple-input": "~2.0", "kartik-v/yii2-widget-datepicker": "@dev", "kartik-v/yii2-widget-datetimepicker": "*", "kartik-v/yii2-widget-depdrop": "@dev", "bedezign/yii2-audit": "1.1.1", "2amigos/yii2-tinymce-widget": "~1.1", "justinvoelker/yii2-separatedpager": "^1.0", "creocoder/yii2-flysystem": "^1.1", "league/flysystem-azure": "^1.0", "geoip2/geoip2": "^2.12", "2amigos/yii2-file-upload-widget": "^1.0", "yiisoft/yii2-elasticsearch": "~2.1.0", "aws/aws-sdk-php": "^3.191", "tinymce/tinymce": "5.10.2", "kartik-v/yii2-date-range": "dev-master", "twig/twig": "^3.0", "matthiasmullie/minify": "^1.3", "mpdf/mpdf": "^8.2", "sebastian/diff": "^3.0", "get-stream/stream-chat": "^1.0"}, "require-dev": {"yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "~2.1.0", "yiisoft/yii2-faker": "~2.0.0", "codeception/codeception": "^4.0", "codeception/module-asserts": "^1.0", "codeception/module-yii2": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/verify": "~0.5.0 || ~1.1.0", "symfony/browser-kit": ">=2.7 <=4.2.4", "symfony/var-dumper": "^5.2", "yiisoft/yii2-coding-standards": "2.*", "squizlabs/php_codesniffer": "^3.0@dev"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": false}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}], "scripts": {"post-install-cmd": ["@pre-commit-hook"], "post-update-cmd": ["@pre-commit-hook"], "pre-commit-hook": ["cp scripts/git-hooks/pre-commit .git/hooks/pre-commit", "chmod +x .git/hooks/pre-commit"]}}
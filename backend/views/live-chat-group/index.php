<?php

use yii\helpers\Html;
use yii\grid\GridView;
use common\models\LiveChatGroup;

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Live Chat Groups';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="live-chat-group-index">

    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><?= Html::encode($this->title) ?></h3>
                    <div class="box-tools pull-right">
                        <?= Html::a('<i class="fa fa-plus"></i> Create Live Chat Group', ['create'], ['class' => 'btn btn-success btn-sm']) ?>
                    </div>
                </div>
                <div class="box-body">
                    <?= GridView::widget([
                        'dataProvider' => $dataProvider,
                        'tableOptions' => ['class' => 'table table-striped table-bordered'],
                        'columns' => [
                            ['class' => 'yii\grid\SerialColumn'],

                            [
                                'attribute' => 'group_name',
                                'format' => 'text',
                                'contentOptions' => ['style' => 'max-width: 200px; word-wrap: break-word;'],
                            ],
                            [
                                'attribute' => 'entity',
                                'value' => function ($model) {
                                    return $model->getEntityTypeLabel();
                                },
                                'filter' => Html::activeDropDownList(
                                    new LiveChatGroup(),
                                    'entity',
                                    ['' => 'All'] + LiveChatGroup::getEntityTypes(),
                                    ['class' => 'form-control']
                                ),
                            ],
                            [
                                'attribute' => 'entity_id',
                                'label' => 'Entity Name',
                                'value' => function ($model) {
                                    return $model->getEntityName();
                                },
                                'contentOptions' => ['style' => 'max-width: 200px; word-wrap: break-word;'],
                            ],
                            [
                                'attribute' => 'channel_id',
                                'format' => 'text',
                                'contentOptions' => ['style' => 'max-width: 150px; word-wrap: break-word;'],
                            ],
                            [
                                'attribute' => 'is_active_on_news',
                                'label' => 'News Pages',
                                'value' => function ($model) {
                                    return $model->is_active_on_news ? 'Yes' : 'No';
                                },
                                'contentOptions' => function ($model) {
                                    return ['class' => $model->is_active_on_news ? 'text-success' : 'text-muted'];
                                },
                            ],
                            [
                                'attribute' => 'is_active_on_articles',
                                'label' => 'Article Pages',
                                'value' => function ($model) {
                                    return $model->is_active_on_articles ? 'Yes' : 'No';
                                },
                                'contentOptions' => function ($model) {
                                    return ['class' => $model->is_active_on_articles ? 'text-success' : 'text-muted'];
                                },
                            ],
                            [
                                'attribute' => 'status',
                                'value' => function ($model) {
                                    return $model->getStatusLabel();
                                },
                                'contentOptions' => function ($model) {
                                    return ['class' => $model->status == LiveChatGroup::STATUS_ACTIVE ? 'text-success' : 'text-danger'];
                                },
                                'filter' => Html::activeDropDownList(
                                    new LiveChatGroup(),
                                    'status',
                                    ['' => 'All'] + LiveChatGroup::getStatusTypes(),
                                    ['class' => 'form-control']
                                ),
                            ],
                            [
                                'attribute' => 'created_at',
                                'format' => ['date', 'php:Y-m-d H:i'],
                                'contentOptions' => ['style' => 'width: 150px;'],
                            ],

                            [
                                'class' => 'yii\grid\ActionColumn',
                                'template' => '{view} {update} {delete}',
                                'buttons' => [
                                    'view' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-eye"></i>', $url, [
                                            'title' => 'View',
                                            'class' => 'btn btn-xs btn-info',
                                            'data-pjax' => '0',
                                        ]);
                                    },
                                    'update' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-pencil"></i>', $url, [
                                            'title' => 'Update',
                                            'class' => 'btn btn-xs btn-primary',
                                            'data-pjax' => '0',
                                        ]);
                                    },
                                    'delete' => function ($url, $model, $key) {
                                        return Html::a('<i class="fa fa-trash"></i>', $url, [
                                            'title' => 'Delete',
                                            'class' => 'btn btn-xs btn-danger',
                                            'data' => [
                                                'confirm' => 'Are you sure you want to delete this live chat group?',
                                                'method' => 'post',
                                            ],
                                        ]);
                                    },
                                ],
                                'contentOptions' => ['style' => 'width: 120px; text-align: center;'],
                            ],
                        ],
                    ]); ?>
                </div>
            </div>
        </div>
    </div>

</div>

<style>
.grid-view th {
    background-color: #f4f4f4;
    font-weight: bold;
}

.btn-xs {
    margin: 1px;
}

.table > tbody > tr > td {
    vertical-align: middle;
}
</style>

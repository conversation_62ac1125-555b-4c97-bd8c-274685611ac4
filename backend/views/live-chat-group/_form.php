<?php

use common\helpers\DataHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\models\LiveChatGroup;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\JsExpression;

/* @var $this yii\web\View */
/* @var $model common\models\LiveChatGroup */
/* @var $form yii\widgets\ActiveForm */

// dd($model->entity_id);
// dd(ArrayHelper::map($model->getPageName(), 'id', 'name'));
$disabled = !$model->isNewRecord;
?>

<div class="live-chat-group-form">

    <?php $form = ActiveForm::begin([
        'id' => 'live-chat-group-form',
        'options' => ['class' => 'form-horizontal'],
        'fieldConfig' => [
            'template' => '<div class="col-sm-3">{label}</div><div class="col-sm-9">{input}{error}</div>',
            'labelOptions' => ['class' => 'control-label'],
        ],
    ]); ?>

    <div class="form-group">
        <div class="col-sm-3">
            <?= Html::label('Entity Type', 'entity-type', ['class' => 'control-label']) ?>
        </div>
        <div class="col-sm-9">
            <?= Html::dropDownList(
                'entity_type',
                $model->entity ?? '',
                ['' => 'Select Entity Type'] + LiveChatGroup::getEntityTypes(),
                [
                    'id' => 'entity-type',
                    'class' => 'form-control'
                ]
            ) ?>
        </div>
    </div>

    <?= $form->field($model, 'entity')->hiddenInput(['id' => 'livechatgroup-entity'])->label(false) ?>

    <?= $form->field($model, 'entity_id')->widget(Select2::classname(), [
        'data' => !empty($model->pageName) ? ArrayHelper::map($model->pageName, 'id', 'name') : [],
        'language' => 'en',
        'options' => [
            'placeholder' => '--Select--',
            'multiple' => false,
        ],
        'pluginOptions' => [
            'allowClear' => true,
            'placeholder' => '--Select--',
            'disabled' => !$model->isNewRecord,
            'minimumInputLength' => 3,
            'language' => [
                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
            ],
            'ajax' => [
                'url' => Url::to(['/faq/get-list']),
                'dataType' => 'json',
                'data' => new JsExpression("function(params) {return {q:params.term,depdrop_parents:$('#entity-type').val()}; }")
            ],
            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
            'templateResult' => new JsExpression('function(data) { return data.text; }'),
            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
        ],
    ])->label('Select Page');
?>

    <?= $form->field($model, 'group_name')->textInput(['maxlength' => true, 'placeholder' => 'Enter group name']) ?>

    <?= $form->field($model, 'channel_id')->textInput(['readonly' => $disabled, 'maxlength' => true, 'placeholder' => 'Auto-generated if empty']) ?>

    <?= $form->field($model, 'channel_type')->dropDownList(
        LiveChatGroup::getChannelTypes(),
        [
            'prompt' => 'Select Channel Type',
            'disabled' => $disabled
        ]
    ) ?>

    <?php if (!$model->isNewRecord) { ?>
        <?= Html::activeHiddenInput($model, 'channel_type') ?>
    <?php } ?>

    <?= $form->field($model, 'description')->textarea(['rows' => 4, 'placeholder' => 'Enter group description (optional)']) ?>

    <div class="form-group">
        <div class="col-sm-3">
            <label class="control-label">Display Settings</label>
        </div>
        <div class="col-sm-9">
            <div class="checkbox">
                <label>
                    <?= Html::activeCheckbox($model, 'is_active_on_news', ['value' => 1, 'uncheck' => 0]) ?>
                    Show on News Pages
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <?= Html::activeCheckbox($model, 'is_active_on_articles', ['value' => 1, 'uncheck' => 0]) ?>
                    Show on Article Pages
                </label>
            </div>
        </div>
    </div>

    <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', LiveChatGroup::class)) ?>
    
    <div class="form-group">
        <div class="col-sm-offset-3 col-sm-9">
            <?= Html::submitButton($model->isNewRecord ? '<i class="fa fa-save"></i> Create' : '<i class="fa fa-save"></i> Update', [
                'class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary'
            ]) ?>
            <?= Html::a('<i class="fa fa-times"></i> Cancel', ['index'], ['class' => 'btn btn-default']) ?>
        </div>
    </div>

    <?php ActiveForm::end(); ?>

</div>

<?php
$getEntitiesUrl = Url::to(['get-entities']);
$this->registerJs("
    // Handle entity type change
    $('#entity-type').on('change', function() {
        var entityType = $(this).val();
        var entitySelect = $('#entity-id');
        
        if (entityType) {
            // Enable entity dropdown
            entitySelect.prop('disabled', false);
            entitySelect.html('<option value=\"\">Loading...</option>');
            
            // Make AJAX request to get entities
            $.ajax({
                url: '$getEntitiesUrl',
                type: 'POST',
                data: {
                    entity_type: entityType,
                    '" . Yii::$app->request->csrfParam . "': '" . Yii::$app->request->csrfToken . "'
                },
                dataType: 'json',
                success: function(data) {
                    var options = '<option value=\"\">Select Entity</option>';
                    $.each(data, function(index, item) {
                        options += '<option value=\"' + item.id + '\">' + item.name + '</option>';
                    });
                    entitySelect.html(options);
                },
                error: function() {
                    entitySelect.html('<option value=\"\">Error loading entities</option>');
                }
            });
        } else {
            // Disable entity dropdown
            entitySelect.prop('disabled', true);
            entitySelect.html('<option value=\"\">Select Entity</option>');
        }
        
        // Update hidden entity field
        $('#livechatgroup-entity').val(entityType);
    });
    
    // Set initial entity type if editing
    " . ($model->entity ? "$('#entity-type').val('{$model->entity}').trigger('change'); setTimeout(function() { $('#entity-id').val('{$model->entity_id}'); }, 500);" : '') . "
    
    // Auto-generate group name based on entity selection
    $('#entity-id').on('change', function() {
        var entityName = $(this).find('option:selected').text();
        var entityType = $('#entity-type').find('option:selected').text();
        
        if (entityName && entityName !== 'Select Entity' && entityType && entityType !== 'Select Entity Type') {
            var groupName = entityType + ' - ' + entityName + ' Discussion';
            $('#livechatgroup-group_name').val(groupName);
        }
    });
");
?>

<style>
    .form-horizontal .form-group {
        margin-bottom: 20px;
    }

    .checkbox {
        margin-top: 0;
        margin-bottom: 10px;
    }

    .checkbox label {
        font-weight: normal;
    }
</style>
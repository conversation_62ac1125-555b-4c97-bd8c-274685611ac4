<?php

use common\helpers\ContentHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;
use yii\grid\GridView;
use common\models\LiveChatUserMessage;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\LiveChatGroup */
/* @var $messagesDataProvider yii\data\ActiveDataProvider */

$this->title = $model->group_name;
$this->params['breadcrumbs'][] = ['label' => 'Live Chat Groups', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

$this->registerJsFile('https://backend.getmyuni.com/assets/8706a37e/tinymce.js', ['depends' => [\yii\web\JqueryAsset::className()]]);
$this->registerJsFile('js/live_chat_backend.js', ['depends' => [\yii\web\JqueryAsset::className()]]);
$this->registerCssFile('css/live_chat_group.css', ['depends' => [\yii\web\JqueryAsset::className()]]);
?>

<div class="live-chat-group-view">

    <div class="row">
        <div class="col-md-8">
            <!-- Group Details -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Group Details</h3>
                    <div class="box-tools pull-right">
                        <?= Html::a('<i class="fa fa-pencil"></i> Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-sm']) ?>
                        <?= Html::a('<i class="fa fa-list"></i> Back to List', ['index'], ['class' => 'btn btn-default btn-sm']) ?>
                        <?= Html::a('<i class="fa fa-trash"></i> Delete', ['delete', 'id' => $model->id], [
                            'class' => 'btn btn-danger btn-sm',
                            'data' => [
                                'confirm' => 'Are you sure you want to delete this live chat group?',
                                'method' => 'post',
                            ],
                        ]) ?>
                    </div>
                </div>
                <div class="box-body">
                    <?= DetailView::widget([
                        'model' => $model,
                        'options' => ['class' => 'table table-striped table-bordered detail-view'],
                        'attributes' => [
                            'id',
                            'group_name',
                            [
                                'attribute' => 'entity',
                                'value' => $model->getEntityTypeLabel(),
                            ],
                            [
                                'attribute' => 'entity_id',
                                'label' => 'Entity Name',
                                'value' => $model->getEntityName(),
                            ],
                            'channel_id',
                            'channel_type',
                            'description:ntext',
                            [
                                'attribute' => 'is_active_on_news',
                                'value' => $model->is_active_on_news ? 'Yes' : 'No',
                            ],
                            [
                                'attribute' => 'is_active_on_articles',
                                'value' => $model->is_active_on_articles ? 'Yes' : 'No',
                            ],
                            [
                                'attribute' => 'status',
                                'value' => $model->getStatusLabel(),
                            ],
                            'created_at:datetime',
                            'updated_at:datetime',
                        ],
                    ]) ?>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Group Statistics -->
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-bar-chart"></i> Group Statistics</h3>
                </div>
                <div class="box-body">
                    <?php
                    $totalMessages = LiveChatUserMessage::find()->where(['group_id' => $model->id, 'is_deleted' => 0])->count();
                    $adminMessages = LiveChatUserMessage::find()->where(['group_id' => $model->id, 'is_admin_message' => 1, 'is_deleted' => 0])->count();
                    $userMessages = $totalMessages - $adminMessages;
                    $recentMessages = LiveChatUserMessage::find()->where(['group_id' => $model->id, 'is_deleted' => 0])->andWhere(['>=', 'created_at', date('Y-m-d', strtotime('-7 days'))])->count();
                    ?>

                    <div class="row">
                        <div class="col-xs-6">
                            <div class="description-block border-right">
                                <span class="description-percentage text-green"><i class="fa fa-comments"></i></span>
                                <h5 class="description-header"><?= $totalMessages ?></h5>
                                <span class="description-text">Total Messages</span>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="description-block">
                                <span class="description-percentage text-blue"><i class="fa fa-user"></i></span>
                                <h5 class="description-header"><?= $userMessages ?></h5>
                                <span class="description-text">User Messages</span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-6">
                            <div class="description-block border-right">
                                <span class="description-percentage text-yellow"><i class="fa fa-shield"></i></span>
                                <h5 class="description-header"><?= $adminMessages ?></h5>
                                <span class="description-text">Admin Messages</span>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="description-block">
                                <span class="description-percentage text-red"><i class="fa fa-clock-o"></i></span>
                                <h5 class="description-header"><?= $recentMessages ?></h5>
                                <span class="description-text">Last 7 Days</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Send Admin Message -->
    <div class="row">
        <div class="col-md-12">
            <div class="box box-success">
                <div class="box-body">
                    <?php $form = ActiveForm::begin([
                        'action' => ['send-message', 'id' => $model->id],
                        'method' => 'post',
                        'options' => [
                            'enctype' => 'multipart/form-data',
                            'id' => 'admin-message-form',
                            'data-group-id' => $model->id,
                            'class' => 'modern-admin-chat-form'
                        ]
                    ]); ?>

                    <div class="admin-chat-container">
                        <!-- Header -->
                        <div class="chat-header">
                            <div class="header-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" />
                                </svg>
                            </div>
                            <h3 class="chat-title">Send Admin Message</h3>
                        </div>

                        <!-- Rich Text Editor -->
                        <div class="editor-container">
                            <div class="editor-wrapper">
                                <textarea id="admin-message-editor" name="message_text"
                                    placeholder="Type your message here..."></textarea>

                                <div id="char-counter">
                                    0 / 280
                                </div>
                            </div>
                        </div>

                        <!-- ADD THIS: Mentioned Users Display -->
                        <div class="mentioned-users" style="display: none;">
                            <!-- Users will be dynamically added here -->
                        </div>

                        <!-- File Attachments Preview -->
                        <div id="attachments-preview" class="attachments-preview"></div>

                        <!-- Modern Input Bar -->
                        <div class="chat-input-container">
                            <div class="input-bar">
                                <!-- Attachment Button -->
                                <button type="button" class="attachment-button" id="attach-files-btn" title="Attach files">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10" />
                                        <path d="M12 8v8M8 12h8" />
                                    </svg>
                                    <span class="button-tooltip">Attach files</span>
                                </button>

                                <!-- ADD THIS: Mentions Button -->
                                <button type="button" class="mentions-button" id="mentions-btn" title="Mention users">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="8.5" cy="7" r="4"></circle>
                                        <path d="m22 11-5 5-3-3"></path>
                                    </svg>
                                    <span class="button-tooltip">Mention users</span>
                                </button>

                                <!-- Input Status Text -->
                                <div class="input-status">
                                    <span id="input-status-text">Compose your message above</span>
                                </div>

                                <!-- Send Button - CHANGED TO TYPE="BUTTON" -->
                                <button type="button" class="send-button" id="send-message-btn" disabled title="Send message">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z" />
                                        <path d="m21.854 2.147-10.94 10.939" />
                                    </svg>
                                    <span class="button-tooltip">Send message</span>
                                </button>
                            </div>
                            <p class="text-muted">Max File Size: 10KB, Max 5 files</p>
                        </div>

                        <!-- Hidden file input -->
                        <input type="file" id="admin-file-input" style="display:none;" multiple
                            accept="image/*,application/pdf,.doc,.docx,.txt,.zip,.rar,.csv,.xls,.xlsx">
                    </div>

                    <!-- Mentions Modal -->
                    <div id="mentions-modal" class="mentions-modal" style="display: none;">
                        <div class="mentions-modal-content">
                            <div class="mentions-header">
                                <h3>Select Users to Mention</h3>
                                <button type="button" class="close-mentions" id="close-mentions">×</button>
                            </div>
                            <div class="mentions-body">
                                <div class="search-users">
                                    <input type="text" id="user-search" placeholder="Search users...">
                                </div>

                                <!-- NEW: Select All Section -->
                                <div class="select-all-section" id="select-all-section" style="display: none;">
                                    <label class="select-all-label">
                                        <input type="checkbox" id="select-all-users" />
                                        <span>Select All Users</span>
                                    </label>
                                </div>

                                <div class="users-list" id="users-list">
                                    <!-- Users will be loaded via AJAX -->
                                </div>
                            </div>
                            <div class="mentions-footer">
                                <button type="button" class="btn btn-secondary" id="cancel-mentions">Cancel</button>
                                <button type="button" class="btn btn-primary" id="add-mentions">Add Mentions</button>
                            </div>
                        </div>
                    </div>

                    <?php ActiveForm::end(); ?>

                </div>
            </div>
        </div>
    </div>

    <!-- Messages List -->
    <div class="row">
        <div class="col-md-12">
            <div class="box box-warning">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-comments"></i> Recent Messages</h3>
                </div>
                <div class="box-body">
                    <?= GridView::widget([
                        'dataProvider' => $messagesDataProvider,
                        'tableOptions' => ['class' => 'table table-striped table-bordered'],
                        'columns' => [
                            ['class' => 'yii\grid\SerialColumn'],

                            [
                                'attribute' => 'user_name',
                                'label' => 'Sender',
                                'value' => function ($model) {
                                    return $model->getSenderDisplayName();
                                },
                                'contentOptions' => function ($model) {
                                    return ['class' => $model->isAdminMessage() ? 'text-success font-weight-bold' : ''];
                                },
                            ],
                            [
                                'attribute' => 'message_id',
                                'label' => 'Message ID',
                                'contentOptions' => ['style' => 'width: 100px;'],
                            ],
                            [
                                'attribute' => 'message_text',
                                'format' => 'raw',
                                'contentOptions' => ['style' => 'max-width: 300px; word-wrap: break-word;'],
                                'value' => function ($model) {
                                    return ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->message_text)));
                                }
                            ],
                            [
                                'attribute' => 'message_type',
                                'label' => 'Type',
                                'value' => function ($model) {
                                    return $model->getMessageTypeLabel();
                                },
                            ],
                            [
                                'attribute' => 'attachments',
                                'label' => 'Attachments',
                                'format' => 'raw',
                                'value' => function ($model) {
                                    $attachments = json_decode($model->attachments, true);
                                    if (empty($attachments)) {
                                        return '';
                                    }
                                    $links = [];
                                    foreach ($attachments as $file) {
                                        $links[] = Html::a($file['name'], $file['url'], ['target' => '_blank']);
                                    }
                                    return implode(', ', $links);
                                },
                                'contentOptions' => ['style' => 'max-width: 300px; word-wrap: break-word;'],
                            ],
                            [
                                'attribute' => 'is_admin_message',
                                'label' => 'Type',
                                'value' => function ($model) {
                                    return $model->getAdminMessageLabel();
                                },
                                'contentOptions' => function ($model) {
                                    return ['class' => $model->isAdminMessage() ? 'text-success' : 'text-primary'];
                                },
                            ],
                            [
                                'attribute' => 'created_at',
                                'format' => ['date', 'php:Y-m-d H:i:s'],
                                'contentOptions' => ['style' => 'width: 150px;'],
                            ],

                            [
                                'class' => 'yii\grid\ActionColumn',
                                'template' => '{delete}',
                                'buttons' => [
                                    'delete' => function ($url, $model, $key) {
                                        if ($model->isDeleted()) {
                                            return '<span class="text-muted">Deleted</span>';
                                        }
                                        return Html::a('<i class="fa fa-trash"></i>', ['delete-message', 'messageId' => $model->id], [
                                            'title' => 'Delete Message',
                                            'class' => 'btn btn-xs btn-danger',
                                            'data' => [
                                                'confirm' => 'Are you sure you want to delete this message?',
                                                'method' => 'post',
                                            ],
                                        ]);
                                    },
                                ],
                                'contentOptions' => ['style' => 'width: 80px; text-align: center;'],
                            ],
                        ],
                    ]); ?>
                </div>
            </div>
        </div>
    </div>

</div>
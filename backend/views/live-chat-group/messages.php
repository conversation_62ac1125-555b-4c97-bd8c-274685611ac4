<?php

use yii\helpers\Html;
use yii\grid\GridView;
use common\models\LiveChatUserMessage;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'All Chat Messages';
$this->params['breadcrumbs'][] = ['label' => 'Live Chat Groups', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="live-chat-messages-index">

    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><?= Html::encode($this->title) ?></h3>
                    <div class="box-tools pull-right">
                        <?= Html::a('<i class="fa fa-list"></i> Back to Groups', ['index'], ['class' => 'btn btn-default btn-sm']) ?>
                    </div>
                </div>
                <div class="box-body">
                    <?= GridView::widget([
                        'dataProvider' => $dataProvider,
                        'tableOptions' => ['class' => 'table table-striped table-bordered'],
                        'columns' => [
                            ['class' => 'yii\grid\SerialColumn'],

                            [
                                'attribute' => 'group.group_name',
                                'label' => 'Group',
                                'value' => function ($model) {
                                    return $model->group ? $model->group->group_name : 'N/A';
                                },
                                'contentOptions' => ['style' => 'max-width: 150px; word-wrap: break-word;'],
                            ],
                            [
                                'attribute' => 'user_name',
                                'label' => 'Sender',
                                'value' => function ($model) {
                                    return $model->getSenderDisplayName();
                                },
                                'contentOptions' => function ($model) {
                                    return ['class' => $model->isAdminMessage() ? 'text-success font-weight-bold' : ''];
                                },
                            ],
                            [
                                'attribute' => 'message_text',
                                'format' => 'ntext',
                                'contentOptions' => ['style' => 'max-width: 300px; word-wrap: break-word;'],
                            ],
                            [
                                'attribute' => 'message_type',
                                'value' => function ($model) {
                                    return $model->getMessageTypeLabel();
                                },
                            ],
                            [
                                'attribute' => 'is_admin_message',
                                'label' => 'Type',
                                'value' => function ($model) {
                                    return $model->getAdminMessageLabel();
                                },
                                'contentOptions' => function ($model) {
                                    return ['class' => $model->isAdminMessage() ? 'text-success' : 'text-primary'];
                                },
                                'filter' => Html::activeDropDownList(
                                    new LiveChatUserMessage(),
                                    'is_admin_message',
                                    ['' => 'All'] + LiveChatUserMessage::getAdminMessageTypes(),
                                    ['class' => 'form-control']
                                ),
                            ],
                            [
                                'attribute' => 'created_at',
                                'format' => ['date', 'php:Y-m-d H:i:s'],
                                'contentOptions' => ['style' => 'width: 150px;'],
                            ],

                            [
                                'class' => 'yii\grid\ActionColumn',
                                'template' => '{respond} {delete}',
                                'buttons' => [
                                    'respond' => function ($url, $model, $key) {
                                        if ($model->isAdminMessage()) {
                                            return ''; // Don't show respond button for admin messages
                                        }
                                        return Html::button('<i class="fa fa-reply"></i>', [
                                            'title' => 'Respond to Message',
                                            'class' => 'btn btn-xs btn-info respond-btn',
                                            'data-message-id' => $model->id,
                                            'data-message-text' => $model->message_text,
                                            'data-sender' => $model->getSenderDisplayName(),
                                        ]);
                                    },
                                    'delete' => function ($url, $model, $key) {
                                        if ($model->isDeleted()) {
                                            return '<span class="text-muted">Deleted</span>';
                                        }
                                        return Html::a('<i class="fa fa-trash"></i>', ['delete-message', 'messageId' => $model->id], [
                                            'title' => 'Delete Message',
                                            'class' => 'btn btn-xs btn-danger',
                                            'data' => [
                                                'confirm' => 'Are you sure you want to delete this message?',
                                                'method' => 'post',
                                            ],
                                        ]);
                                    },
                                ],
                                'contentOptions' => ['style' => 'width: 100px; text-align: center;'],
                            ],
                        ],
                    ]); ?>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Response Modal -->
<div class="modal fade" id="responseModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">Respond to Message</h4>
            </div>
            <?php $form = ActiveForm::begin([
                'id' => 'response-form',
                'action' => ['respond-to-message', 'messageId' => 0],
                'method' => 'post',
            ]); ?>
            <div class="modal-body">
                <div class="form-group">
                    <label><strong>Original Message:</strong></label>
                    <div class="well well-sm" id="original-message-text"></div>
                    <small class="text-muted">From: <span id="original-sender"></span></small>
                </div>
                
                <div class="form-group">
                    <?= Html::label('Your Response', 'response_text', ['class' => 'control-label']) ?>
                    <?= Html::textarea('response_text', '', [
                        'class' => 'form-control',
                        'rows' => 4,
                        'placeholder' => 'Type your response here...',
                        'required' => true
                    ]) ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <?= Html::submitButton('<i class="fa fa-send"></i> Send Response', [
                    'class' => 'btn btn-primary'
                ]) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
    </div>
</div>

<?php
$this->registerJs("
    // Handle respond button click
    $('.respond-btn').on('click', function() {
        var messageId = $(this).data('message-id');
        var messageText = $(this).data('message-text');
        var sender = $(this).data('sender');
        
        // Update modal content
        $('#original-message-text').text(messageText);
        $('#original-sender').text(sender);
        
        // Update form action
        $('#response-form').attr('action', '/live-chat-group/respond-to-message/' + messageId);
        
        // Clear response textarea
        $('#response_text').val('');
        
        // Show modal
        $('#responseModal').modal('show');
    });
");
?>

<style>
.grid-view th {
    background-color: #f4f4f4;
    font-weight: bold;
}

.btn-xs {
    margin: 1px;
}

.table > tbody > tr > td {
    vertical-align: middle;
}

.font-weight-bold {
    font-weight: bold;
}

.well {
    background-color: #f9f9f9;
    border: 1px solid #e3e3e3;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
}
</style>

<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\CollegeProgramContent;
use common\models\ContentTemplate;
use kartik\select2\Select2;
use unclead\multipleinput\MultipleInput;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeProgramContent */
/* @var $form yii\widgets\ActiveForm */

$model->content = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content)));
if (!empty($model->template_id)) {
    $template = ContentTemplate::find()->where(['id' => $model->template_id])->andWhere(['page' => 'pi'])->all();
    $templateData =  ArrayHelper::map($template, 'id', 'name');
}
?>

<div class="college-program-content-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <?= $form->field($model, 'qualification')->widget(MultipleInput::class, [
            'max'               => 20,
            'allowEmptyList'    => false,
            'enableGuessTitle'  => true,
            'columns' => [
                [
                    'name'  => 'educational_qualification',
                    'title' => 'Educational Qualification',
                    'type' => 'dropDownList',
                    'items' => DataHelper::$educationalQualification,
                    'columnOptions' => [
                        'style' => 'width: 40%;resize: none;',

                    ]
                ],
                [
                    'name'  => 'scale',
                    'title' => 'Scale',
                    'type' => 'dropDownList',
                    'items' => DataHelper::$scale,
                    'columnOptions' => [
                        'style' => 'width: 40%;resize: none;',

                    ]
                ],
                [
                    'name'  => 'marks',
                    'title' => 'Marks',
                    'columnOptions' => [
                        'style' => 'width:40%;',
                    ]
                ]
            ]
        ])->label(false);
?>

        <?php if (!empty($exams)): ?>
            <?= $form->field($model, 'exams')->widget(MultipleInput::class, [
                'max'  => 20,
                'allowEmptyList'    => false,
                'enableGuessTitle'  => true,
                'columns' => [
                    [
                        'name'  => 'exams',
                        'title' => 'Exams',
                        'type' => 'dropDownList',
                        'defaultValue' => '',
                        'items' => $exams,
                        'columnOptions' => [
                            'style' => 'width: 60%;resize: none;',

                        ]
                    ],
                    [
                        'name'  => 'marks',
                        'title' => 'Marks',
                        'columnOptions' => [
                            'style' => 'width:60%;',
                        ]
                    ]
                ]
            ])->label(false);
            ?>
        <?php endif; ?>

        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'content',
            'type' => ''
        ])
        ?>

        <?= $form->field($model, 'template_id')->widget(Select2::classname(), [
            'data' => $templateData ?? [], // array of text to show in the tag for the selected items
            'options' => [
                'placeholder' => '--Select--',
                'multiple' => false,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 3,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => ['/college-program-content/template-list'],
                    'dataType' => 'json',
                    'data' => new JsExpression('function(params) {return {q:params.term}; }')
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ]
        ])->label('Content Template');
?>

        <?= $form->field($model, 'meta_title')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'meta_description')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CollegeProgramContent::class)) ?>

        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'editor_remark',
            'type' => ''
        ])
        ?>

    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>
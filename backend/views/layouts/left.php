<?php


use mdm\admin\components\Helper;
use mdm\admin\components\MenuHelper;
use yii\bootstrap\Nav;
use common\helpers\DataHelper;
use Yii;

?>

<aside class="main-sidebar">

    <section class="sidebar">

        <!-- Sidebar user panel -->
        <div class="user-panel">
            <div class="pull-left image">
                <img src="<?= !empty(Yii::$app->user->identity->profile->image) ? DataHelper::s3Path(Yii::$app->user->identity->profile->image, 'user', true) : $directoryAsset . '/img/user2-160x160.jpg' ?>" class="img-circle" alt="User Image" />
            </div>
            <div class="pull-left info">
                <p><?= isset(Yii::$app->user->identity) ? ucfirst(Yii::$app->user->identity->username) : '' ?></p>

                <a href="#"><i class="fa fa-circle text-success"></i> Online</a>
            </div>
        </div>

        <!-- search form -->
        <form action="#" method="get" class="sidebar-form">
            <div class="input-group">
                <input type="text" name="q" class="form-control" placeholder="Search..." />
                <span class="input-group-btn">
                    <button type='submit' name='search' id='search-btn' class="btn btn-flat"><i class="fa fa-search"></i>
                    </button>
                </span>
            </div>
        </form>
        <!-- /.search form -->

        <?php
        $menuItems = [
            ['label' => 'Menu Yii2', 'options' => ['class' => 'header']],
            // ['label' => 'BD College', 'icon' => 'university', 'url' => ['/business-entity/index']],
            // ['label' => 'Spoc', 'icon' => 'users', 'url' => ['/business-entity-spoc/index']],
            // ['label' => 'Spoc', 'icon' => 'users', 'url' => ['/business-entity-spoc/index']],

            [
                'label' => 'Articles',
                'icon' => 'envelope',
                'url' => '#',
                'items' => [
                    ['label' => 'Article', 'icon' => 'circle-o', 'url' => ['/article/index']],
                    ['label' => 'Category', 'icon' => 'circle-o', 'url' => ['/category/index']],
                    ['label' => 'Tag', 'icon' => 'circle-o', 'url' => ['/tag/index']],
                    ['label' => 'CatagoryTranslation', 'icon' => 'circle-o', 'url' => ['/category-translation/index']],
                    ['label' => 'Article Section', 'icon' => 'circle-o', 'url' => ['/article-subpage-section/index']],
                ],
            ],

            // [
            //     'label' => 'BD DashBoard',
            //     'icon' => 'th-list',
            //     'url' => '#',
            //     'items' => [
            //         ['label' => 'DashBoard', 'icon' => 'dashboard', 'url' => ['/business-entity/home']],
            //         ['label' => 'BD College', 'icon' => 'university', 'url' => ['/business-entity/index']],
            //         ['label' => 'Spoc', 'icon' => 'users', 'url' => ['/business-entity-spoc/index']],
            //         ['label' => 'Leads', 'icon' => 'th-list', 'url' => ['/business-entity-lead/index']],
            //         // ['label' => 'Lead Activity', 'fas fa-file-text', 'url' => ['//business-entity-lead-activity/index']],
            //         ['label' => 'Status', 'fas fa-file-text', 'url' => ['/status/index']],
            //         ['label' => 'Disposal Status', 'fas fa-file-text', 'url' => ['/disposal-status/index']],
            //         ['label' => 'CRM Users', 'icon' => 'fas fa-file-text', 'url' => ['/user-mapping/index']],
            //     ],
            // ],

            [
                'label' => 'Boards',
                'icon' => 'check-square',
                'url' => '#',
                'items' => [
                    ['label' => 'Boards List', 'icon' => 'circle-o', 'url' => ['/board/index']],
                    ['label' => 'Board Content', 'icon' => 'circle-o', 'url' => ['/board-content/index']],
                    ['label' => 'Sample Papers', 'icon' => 'circle-o', 'url' => ['/board-sample-paper/index']],
                    ['label' => 'Sample Paper Files', 'icon' => 'circle-o', 'url' => ['/board-sample-paper-file/index']],
                    ['label' => 'Board Pages/SubPages', 'icon' => 'circle-o', 'url' => ['/board-pages/index']],
                ],
            ],

            [
                'label' => 'Course',
                'icon' => 'book',
                'url' => '#',
                'items' => [
                    ['label' => 'Course List', 'icon' => 'circle-o', 'url' => ['/course/index']],
                    ['label' => 'Course Content', 'icon' => 'circle-o', 'url' => ['/course-content/index']],
                    ['label' => 'Course Feature', 'icon' => 'circle-o', 'url' => ['/highlight-attribute/index', 'entity' => array_search('COURSE', DataHelper::$entities)]],
                    ['label' => 'Stream Content', 'icon' => 'circle-o', 'url' => ['/stream-content/index']],
                ],
            ],
            [
                'label' => 'Career',
                'icon' => 'graduation-cap',
                'url' => '#',
                'items' => [
                    ['label' => 'Career List', 'icon' => 'circle-o', 'url' => ['/career/index']],
                    ['label' => 'Career Content', 'icon' => 'circle-o', 'url' => ['/career-content/index']],
                    ['label' => 'Career Category', 'icon' => 'circle-o', 'url' => ['/career-category/index']],
                ],
            ],
            [
                'label' => 'College',
                'icon' => 'university',
                'url' => '#',
                'items' => [
                    ['label' => 'College', 'icon' => 'circle-o', 'url' => ['/college/index']],
                    ['label' => 'College Content', 'icon' => 'circle-o', 'url' => ['/college-content/index']],
                    ['label' => 'Feature Group', 'icon' => 'circle-o', 'url' => ['/feature-group/index']],
                    ['label' => 'Feature', 'icon' => 'circle-o', 'url' => ['/feature/index']],
                    ['label' => 'Specilaization', 'icon' => 'circle-o', 'url' => ['/specialization/index']],
                    ['label' => 'Program', 'icon' => 'circle-o', 'url' => ['/program/index']],
                    [
                        'label' => 'Cut Off',
                        'icon' => 'cut',
                        'url' => '#',
                        'items' => [
                            ['label' => 'Cut Off Category', 'icon' => 'circle-o', 'url' => ['/cutoff-category/index']],
                            ['label' => 'Cut off Upload', 'icon' => 'circle-o', 'url' => ['/cut-off/create']],
                        ]
                    ],
                    ['label' => 'Student Scholarship', 'icon' => 'circle-o', 'url' => ['/college-student-scholarship/index']],
                    ['label' => 'College Hostel', 'icon' => 'circle-o', 'url' => ['/college-hostel/index']],
                    ['label' => 'student passout', 'icon' => 'circle-o', 'url' => ['/college-student/index']],
                    ['label' => 'Student enrolment', 'icon' => 'circle-o', 'url' => ['/college-student-overall-enrollment/index']],
                    ['label' => 'Program Enrollment', 'icon' => 'circle-o', 'url' => ['/college-program-student-enrollment/index']],
                    ['label' => 'Content Template', 'icon' => 'circle-o', 'url' => ['/content-template/index']],
                ],
            ],
            [
                'label' => 'Notification Updates',
                'icon' => 'bell',
                'url' => '#',
                'items' => [
                    ['label' => 'Exam Notification', 'icon' => 'circle-o', 'url' => ['/exam-notification/index']],
                    ['label' => 'College Notification', 'icon' => 'circle-o', 'url' => ['/college-notification-update/index']],
                    ['label' => 'College Listing Notification', 'icon' => 'circle-o', 'url' => ['/college-listing-notification/index']],
                ],
            ],
            [
                'label' => 'CAM Sponsored',
                'icon' => 'university',
                'url' => '#',
                'items' => [
                    ['label' => 'CAM College', 'icon' => 'circle-o', 'url' => ['/cam-sponsored-colleges/index']],
                ],
            ],
            [
                'label' => 'College Rank Panel',
                'icon' => 'trophy',
                'url' => '/college-rankings/index',
            ],
            [
                'label' => 'College Filter Page',
                'icon' => 'filter',
                'url' => '#',
                'items' => [
                    // ['label' => 'College Filter', 'icon' => 'circle-o', 'url' => ['/filter/index']],
                    // ['label' => 'Filter Group', 'icon' => 'circle-o', 'url' => ['/filter-group/index']],
                    ['label' => 'Filter SEO', 'icon' => 'circle-o', 'url' => ['/filter-page-seo/index']],
                    ['label' => 'Listing Page Mapping', 'icon' => 'circle-o', 'url' => ['/college-listing-mapping/index']]
                ],
            ],
            [
                'label' => 'CMS Activity',
                'icon' => 'newspaper-o',
                'url' => '/audit-trail/index',
            ],

            [
                'label' => 'CLP',
                'icon' => 'desktop',
                'url' => '#',
                'items' => [
                    ['label' => 'Landing Page', 'icon' => 'tachometer', 'url' => ['/custom-landing-page/index']],
                    ['label' => 'College USPs', 'icon' => 'tachometer', 'url' => ['/clp-college-usp/tabular-form']],
                    ['label' => 'Top Recruits', 'icon' => 'tachometer', 'url' => ['/top-recruiters/tabular-form']],
                ]
            ],

            ['label' => 'Comments', 'icon' => 'comment', 'url' => ['/comment/index']],

            ['label' => 'DSA State Mapping', 'icon' => 'area-chart', 'url' => ['/dsa-state-mapping/index']],

            [
                'label' => 'Exams',
                'icon' => 'book',
                'url' => '#',
                'items' => [
                    ['label' => 'Exams List', 'icon' => 'circle-o', 'url' => ['/exam/index']],
                    ['label' => 'Exam Dates', 'icon' => 'circle-o', 'url' => ['/exam-date/index']],
                    ['label' => 'Exam Content', 'icon' => 'circle-o', 'url' => ['/exam-content/index']],
                    ['label' => 'Rank Predictor', 'icon' => 'circle-o', 'url' => ['/rank-predictor/index']],
                    ['label' => 'Exam Feature', 'icon' => 'circle-o', 'url' => ['/highlight-attribute/index', 'entity' => array_search('EXAM', DataHelper::$entities)]],
                ],
            ],

            ['label' => 'Exam College Rank Map', 'icon' => 'eject', 'url' => ['/institute-program-rank-mapping/index']],

            ['label' => 'Elastic Search', 'icon' => 'search', 'url' => ['/mysql-search/index']],

            ['label' => 'Faqs', 'icon' => 'question', 'url' => ['/faq/index']],

            [
                'label' => 'GetGIS Articles',
                'icon' => 'envelope',
                'url' => '#',
                'items' => [
                    ['label' => 'Article', 'icon' => 'circle-o', 'url' => ['/getgis-article/index']],
                    ['label' => 'Category', 'icon' => 'circle-o', 'url' => ['/getgis-article-category/index']],
                    // ['label' => 'Tag', 'icon' => 'circle-o', 'url' => ['/tag/index']],
                ],
            ],

            [
                'label' => 'Home',
                'icon' => 'home',
                'url' => '#',
                'items' => [
                    ['label' => 'Trending Pages', 'icon' => 'circle-o', 'url' => ['/trending-page/index']],
                ],
            ],
            [
                'label' => 'Home Page Slider',
                'icon' => 'picture-o',
                'url' => ['/homepage-sliders/index'],
            ],

            [
                'label' => 'Locations',
                'icon' => 'map-marker',
                'url' => '#',
                'items' => [
                    ['label' => 'Country', 'icon' => 'circle-o', 'url' => ['/country/index']],
                    ['label' => 'State', 'icon' => 'circle-o', 'url' => ['/state/index']],
                    ['label' => 'City', 'icon' => 'circle-o', 'url' => ['/city/index']],
                ],
            ],
            [
                'label' => 'Lead',
                'icon' => 'cube',
                'url' => '#',
                'items' => [
                    ['label' => 'Lead Gmail Domain', 'icon' => 'circle-o', 'url' => ['/lead-gmail-domain/index']],
                    ['label' => 'Lead Budget', 'icon' => 'circle-o', 'url' => ['/lead-education-budget/index']],
                ],
            ],

            [
                'label' => 'Media Drive',
                'icon' => 'hdd-o',
                'url' => '#',
                'items' => [
                    ['label' => 'Download & Upload', 'icon' => 'circle-o', 'url' => ['/media-drive/index']],
                    // ['label' => 'Upload Type', 'icon' => 'circle-o', 'url' => ['/media-drive-upload-type/index']],
                ]
            ],

            ['label' => 'Meta Top Content', 'icon' => 'external-link', 'url' => ['/gmu-meta-category/home']],

            [
                'label' => 'News',
                'icon' => 'newspaper-o',
                'url' => '#',
                'items' => [
                    ['label' => 'News Lists', 'icon' => 'circle-o', 'url' => ['/news/index']],
                    ['label' => 'News Category', 'icon' => 'circle-o', 'url' => ['/news-category/index']],
                    ['label' => 'Tag', 'icon' => 'circle-o', 'url' => ['/tags/index']],
                ],
            ],
            [
                'label' => 'News Subdomain',
                'icon' => 'newspaper-o',
                'url' => '#',
                'items' => [
                    ['label' => 'News Lists', 'icon' => 'circle-o', 'url' => ['/news-subdomain/index']],
                ],
            ],
            [
                'label' => 'Ncert',
                'icon' => 'envelope',
                'url' => '#',
                'items' => [
                    ['label' => 'Ncert', 'icon' => 'circle-o', 'url' => ['/ncert-articles/index']],
                    // ['label' => 'Category', 'icon' => 'circle-o', 'url' => ['/category-ncert/index']],
                ],
            ],

            [
                'label' => 'Olympiad',
                'icon' => 'shield',
                'url' => '#',
                'items' => [
                    ['label' => 'Olympiad List', 'icon' => 'circle-o', 'url' => ['/olympiad/index']],
                    ['label' => 'Olympiad Content', 'icon' => 'circle-o', 'url' => ['/olympiad-content/index']],
                ],
            ],

            [
                'label' => 'Productivity',
                'icon' => 'line-chart',
                'url' => '/productivity/index',
            ],

            ['label' => 'Profile', 'icon' => 'user', 'url' => (!empty(Yii::$app->user->identity->slug)) ? ['/user/profile', 'slug' => Yii::$app->user->identity->slug] : ''],

            [
                'label' => 'QNA',
                'icon' => 'quora',
                'url' => '#',
                'items' => [
                    ['label' => 'Questions', 'icon' => 'question-circle', 'url' => ['/qna/index']],
                    ['label' => 'Answers', 'icon' => 'slack', 'url' => ['/qna-answer/index']],
                    ['label' => 'Reported Answers', 'icon' => 'fa fa-flag', 'url' => ['/qna-report/index']],

                ],
            ],

            [
                'label' => 'Recent Activity',
                'icon' => 'envelope',
                'url' => '#',
                'items' => [
                    ['label' => 'Recent Activity', 'icon' => 'circle-o', 'url' => ['/recent-activity/index']],
                ],
            ],

            [
                'label' => 'Live Chat',
                'icon' => 'comments',
                'url' => '#',
                'items' => [
                    ['label' => 'Chat Groups', 'icon' => 'circle-o', 'url' => ['/live-chat-group/index']],
                    ['label' => 'All Messages', 'icon' => 'envelope', 'url' => ['/live-chat-group/messages']],
                    ['label' => 'Create Group', 'icon' => 'plus-circle', 'url' => ['/live-chat-group/create']],
                ],
            ],

            [
                'label' => 'RBAC Rules',
                'icon' => 'flag',
                'url' => '#',
                'items' => [
                    ['label' => 'Assignment', 'icon' => 'circle-o', 'url' => ['/admin/assignment/index']],
                    ['label' => 'Role', 'icon' => 'circle-o', 'url' => ['/admin/role/index']],
                    ['label' => 'Permission', 'icon' => 'circle-o', 'url' => ['/admin/permission/index']],
                    ['label' => 'Route', 'icon' => 'circle-o', 'url' => ['/admin/route/index']],
                    ['label' => 'User', 'icon' => 'circle-o', 'url' => ['/admin/user/index']],
                ],
            ],

            [
                'label' => 'Reviews',
                'icon' => 'address-book',
                'url' => '#',
                'items' => [
                    ['label' => 'Lists', 'icon' => 'circle-o', 'url' => ['/review/index']],
                    ['label' => 'Category', 'icon' => 'circle-o', 'url' => ['/review-category/index']],
                    ['label' => 'Question', 'icon' => 'circle-o', 'url' => ['/review-question/index']],
                    ['label' => 'Business Unit', 'icon' => 'circle-o', 'url' => ['/business-unit/index']],
                ],
            ],

            [
                'label' => 'Scholarship',
                'icon' => 'graduation-cap',
                'url' => '#',
                'items' => [
                    ['label' => 'Scholarship List', 'icon' => 'circle-o', 'url' => ['/scholarship/index']],
                    ['label' => 'Scholarship Content', 'icon' => 'circle-o', 'url' => ['/scholarship-content/index']],
                    ['label' => 'Scholarship Category', 'icon' => 'circle-o', 'url' => ['/scholarship-category/index']],
                ],
            ],
            [
                'label' => 'Study Abroad',
                'icon' => 'building',
                'url' => '#',
                'items' => [
                    ['label' => 'Country Lists', 'icon' => 'circle-o', 'url' => ['/sa-country/index']],
                    ['label' => 'Country Detail', 'icon' => 'circle-o', 'url' => ['/sa-country-detail/index']],
                    ['label' => 'Colleges', 'icon' => 'circle-o', 'url' => ['/sa-college/index']],
                    ['label' => 'College Details', 'icon' => 'circle-o', 'url' => ['/sa-college-detail/index']],
                    ['label' => 'College Fee', 'icon' => 'circle-o', 'url' => ['/sa-college-fees-detail/index']],
                    ['label' => 'College Subpage', 'icon' => 'circle-o', 'url' => ['/sa-college-subpage/index']],
                    ['label' => 'College Content', 'icon' => 'circle-o', 'url' => ['/sa-college-subpage-content/index']],
                    ['label' => 'Subpage Sidemenu', 'icon' => 'circle-o', 'url' => ['/sa-college-subpage-sidemenu/index']],
                    ['label' => 'Subpage Sidemenu Content', 'icon' => 'circle-o', 'url' => ['/sa-college-subpage-sidemenu-detail/index']],
                    ['label' => 'Course', 'icon' => 'circle-o', 'url' => ['/sa-course/index']],
                    ['label' => 'Course Detail', 'icon' => 'circle-o', 'url' => ['/sa-course-detail/index']],
                    ['label' => 'SA Stream', 'icon' => 'circle-o', 'url' => ['/sa-stream/index']],
                    ['label' => 'SA Program', 'icon' => 'circle-o', 'url' => ['/sa-program/index']],
                    ['label' => 'SA  Specialization', 'icon' => 'circle-o', 'url' => ['/sa-specialization/index']],
                    ['label' => 'SA Degree', 'icon' => 'circle-o', 'url' => ['/sa-degree/index']],
                ],
            ],

            ['label' => 'Seo info', 'icon' => 'google', 'url' => ['/seo-info/index']],
            [
                'label' => 'Sponsor',
                'icon' => 'check-square-o',
                'url' => '#',
                'items' => [
                    ['label' => 'Sponsor Colleges', 'icon' => 'circle-o', 'url' => ['/sponsor-college/index']],
                    ['label' => 'Live Application', 'icon' => 'circle-o', 'url' => ['/live-application/index']],
                ],
            ],


            ['label' => 'Template', 'icon' => 'html5', 'url' => ['/template/index']],
            [
                'label' => 'Utils',
                'icon' => 'link',
                'url' => '#',
                'items' => [
                    ['label' => 'Redirection Panel', 'icon' => 'circle-o', 'url' => ['/redirection/index']],
                    ['label' => 'Cache Flush', 'icon' => 'bug', 'url' => ['/cache-flush/form']],
                    [
                        'label' => 'Dynamic CTA',
                        'icon' => 'anchor',
                        'url' => '#',
                        'items' => [
                            ['label' => 'Tinymce', 'icon' => 'leaf', 'url' => ['/dynamic-cta/index']],
                            ['label' => 'Bucket', 'icon' => 'laptop', 'url' => ['/lead-bucket/home']],
                            ['label' => 'Bucket Cta', 'icon' => 'laptop', 'url' => ['/lead-bucket-cta-detail/index']],
                            ['label' => 'Bucket Tagging', 'icon' => 'laptop', 'url' => ['/lead-bucket-tagging/home']],
                            ['label' => 'Alternate CTA', 'icon' => 'laptop', 'url' => ['/alternate-cta-text/index']],
                            ['label' => 'Thank You Text', 'icon' => 'laptop', 'url' => ['/cta-thank-you/index']],
                        ],
                    ],

                ],
            ],

            [
                'label' => 'Users',
                'icon' => 'users',
                'url' => '#',
                'items' => [
                    ['label' => 'Users', 'icon' => 'users', 'url' => ['/user/index']],
                    ['label' => 'User Translation', 'icon' => 'users', 'url' => ['/user-translation/index']],
                ],
            ],

            [
                'label' => 'Old Panels',
                'icon' => 'tree',
                'url' => '#',
                'items' => [
                    ['label' => 'Sponsor Microsite', 'icon' => 'book', 'url' => ['/sponsor-colleges-microsite/index']],
                    [
                        'label' => 'Abroad',
                        'icon' => 'navicon',
                        'url' => '#',
                        'items' => [
                            ['label' => 'Courses', 'icon' => 'circle-o', 'url' => ['/old/sa-course/index']],
                            ['label' => 'Degree', 'icon' => 'circle-o', 'url' => ['/old/sa-degree/index']],
                            ['label' => 'Stream', 'icon' => 'circle-o', 'url' => ['/old/sa-stream/index']],
                            ['label' => 'HeadOne', 'icon' => 'circle-o', 'url' => ['/old/sa-headone/index']],
                            ['label' => 'Colleges', 'icon' => 'circle-o', 'url' => ['/old/sa-colleges/index']],
                            ['label' => 'URL-Redirection', 'icon' => 'circle-o', 'url' => ['/old/url-redirection/index']],
                            ['label' => 'Users', 'icon' => 'users', 'url' => ['/old/admin-user/index']],

                        ],
                    ]
                ],
            ],
            // [
            //     'label' => 'System',
            //     'icon' => 'dashboard',
            //     'url' => '#',
            //     'items' => [
            //         ['label' => 'Cache', 'icon' => 'circle-o', 'url' => ['/system/cache/index']],
            //         ['label' => 'Information', 'icon' => 'circle-o', 'url' => ['/system/information/index']],
            //         ['label' => 'Key Storage', 'icon' => 'circle-o', 'url' => ['/system/key-storage/index']],
            //         ['label' => 'Logs', 'icon' => 'circle-o', 'url' => ['/system/log/index']]
            //     ],
            // ],
        ];
        ?>

        <?= dmstr\widgets\Menu::widget(
            [
                'options' => ['class' => 'sidebar-menu tree', 'data-widget' => 'tree'],
                'items' => YII_DEBUG ? $menuItems : Helper::filter($menuItems),
            ]
        ) ?>

    </section>

</aside>
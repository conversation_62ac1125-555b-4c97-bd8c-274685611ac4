<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CutOffSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Cut Off';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="cut-off-index box box-primary">
    <?php /*<div class="box-header with-border">
        <?= Html::a('Create Cut Off', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>*/ ?>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]); ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                // 'id',
                [
                    'attribute' => 'college_id',
                    'label' => 'College',
                    'value' => 'college.name'
                ],
                [
                    'attribute' => 'exam_id',
                    'label' => 'Exam',
                    'value' => function ($m) {
                        return $m->exam->display_name ?? '';
                    }
                ],
                [
                    'attribute' => 'course_id',
                    'label' => 'Course Name',
                    'value' => function ($m) {
                        return $m->course->short_name ?? '';
                    }
                ],
                [
                    'attribute' => 'specialization_id',
                    'label' => 'specialization Name',
                    'value' => function ($m) {
                        return $m->specialization->display_name ?? '';
                    }
                ],
                // 'category',
                // 'gender',
                // 'type',
                'year',
                'round',
                // 'opening_rank',
                // 'closing_rank',
                // 'percentile',
                // 'closing_score',
                // 'created_at',
                // 'updated_at',

                // ['class' => 'yii\grid\ActionColumn', 'template' => '{delete}'],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{view}',
                    'buttons' => [
                        'view' => function ($url, $m) {
                            return Html::a('<span class="glyphicon glyphicon-eye-open"></span>', $url, ['target' => '_blank']);
                        },
                    ],
                    'urlCreator' => function ($action, $model) {
                        if ($action === 'view') {
                            $url = ['cut-off/home', 'id' => $model->college->id];
                            return $url;
                        }
                    }
                ],

            ],
        ]); ?>
    </div>
</div>

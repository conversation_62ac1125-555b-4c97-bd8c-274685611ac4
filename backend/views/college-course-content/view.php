<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\CollegeCourseContent;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeCourseContent */

$this->title = $model->college->display_name ?? $model->college->name;
$this->params['breadcrumbs'][] = ['label' => 'College Course Contents', 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => $model->college->name, 'url' => '/college/view?id=' . $model->college->id];
$this->params['breadcrumbs'][] = 'College Course Content';
?>
<div class="college-course-content-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if ($model->page_index == 1): ?>
            <?= Html::a('Subpage Content', ['college-ci-subpage-content/add-subpage-content', 'college_course_content_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'attribute' => 'course_id',
                    'label' => 'Course',
                    'value' => function ($model) {
                        return $model->course->name ?? $model->course->slug;
                    }
                ],
                [
                    'label' => 'Qualification',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return $this->render('_qualification', ['model' => $model]);
                    }
                ],
                [
                    'label' => 'Content',
                    'format' => 'raw',
                    'value' => ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content))),
                ],
                'eligibility',
                'meta_title',
                'meta_description',
                'duration',
                [
                    'attribute' => 'duration_type',
                    'value' => function ($data) {
                        if (isset($data->duration_type) && !empty(CollegeHelper::$programDurationType[$data->duration_type])) {
                            return CollegeHelper::$programDurationType[$data->duration_type];
                        }
                    }
                ],
                'h1',
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeCourseContent::class), $model->status)
                ],
            ],
        ]) ?>
    </div>
</div>
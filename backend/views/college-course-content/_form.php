<?php

use common\helpers\CollegeHelper;
use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\CollegeCourse;
use common\models\CollegeProgram;
use common\models\CollegeCourseContent;
use common\models\Course;
use kartik\select2\Select2;
use unclead\multipleinput\MultipleInput;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeCourseContent */
/* @var $form yii\widgets\ActiveForm */
$model->content = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->content)));

?>

<div class="college-course-content-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

    <div class="col-md-12">
       <?= $form->field($model, 'course_id')->widget(Select2::class, [
            'data' => ArrayHelper::map(CollegeProgram::find()->where(['college_id' => $model->college->id])->all(), 'course_id', 'course.name'),
            'language' => 'en',
            'disabled' => !$model->isNewRecord,
            'options' => ['placeholder' => '--Select Course--'],
            'pluginOptions' => [
                'allowClear' => true
            ],
        ])->label('Course');
?>
    </div>
    <div class="col-md-12">
        <?= $form->field($model, 'qualification')->widget(MultipleInput::class, [
            'max'               => 20,
            'allowEmptyList'    => false,
            'enableGuessTitle'  => true,
            'columns' => [
                [
                    'name'  => 'educational_qualification',
                    'title' => 'Educational Qualification',
                    'type' => 'dropDownList',
                    'items' => DataHelper::$educationalQualification,
                    'columnOptions' => [
                        'style' => 'width: 40%;resize: none;',

                    ]
                ],
                [
                    'name'  => 'scale',
                    'title' => 'Scale',
                    'type' => 'dropDownList',
                    'items' => DataHelper::$scale,
                    'columnOptions' => [
                        'style' => 'width: 40%;resize: none;',

                    ]
                ],
                [
                    'name'  => 'marks',
                    'title' => 'Marks',
                    'columnOptions' => [
                        'style' => 'width:40%;',
                    ]
                ]
            ]
        ])->label(false);
?>
    </div>
    <div class="col-md-12">
        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'content',
            'type' => ''
        ])
        ?>
    </div>
    <div class="col-md-12">
        <?= $form->field($model, 'eligibility')->textInput(['maxlength' => true]) ?>
    </div>
    <div class="col-md-6">
        <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>
    </div>
    <div class="col-md-6">
        <?= $form->field($model, 'meta_title')->textInput(['maxlength' => true]) ?> 
    </div>
    <div class="col-md-12">
        <?= $form->field($model, 'meta_description')->textInput(['maxlength' => true]) ?>
    </div>
    <div class="col-md-6">
        <?= $form->field($model, 'duration')->textInput()->label('Duration (In Months only).') ?>
    </div>
    <div class="col-md-6">
        <?= $form->field($model, 'duration_type')->dropDownList(CollegeHelper::$programDurationType)->label('Duration Type') ?>
    </div>
    <div class="col-md-6">
        <?= $form->field($model, 'page_index')->dropDownList(array_reverse(DataHelper::getConstantList('STATUS', CollegeCourseContent::class))) ?>
    </div>
    <div class="col-md-6">
        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CollegeCourseContent::class)) ?>
    </div>
    <div class="col-md-12">
        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'editor_remark',
            'type' => ''
        ])
        ?>
    </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>
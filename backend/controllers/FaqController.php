<?php

namespace backend\controllers;

use Yii;
use common\models\Faq;
use common\models\NcertArticles;
use backend\models\FaqSearch;
use common\helpers\BoardHelper;
use common\helpers\CareerHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use common\helpers\DataHelper;
use common\helpers\CollegeHelper;
use common\helpers\CourseHelper;
use common\models\Filter;
use common\models\FilterPageSeo;
use common\models\Course;
use common\models\Exam;
use common\models\BoardPages;
use common\models\College;
use common\models\Board;
use yii\db\Query;
use yii\web\Response;

/**
 * FaqController implements the CRUD actions for Faq model.
 */
class FaqController extends Controller
{
    // protected static $fields = [
    //     'exam' => ['id', 'name'],
    //     'article' => ['id', 'title'],
    //     'college' => ['id', 'name']
    // ];
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Faq models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new FaqSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Faq model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Faq model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Faq();

        if ($model->load(Yii::$app->request->post())) {
            if (strtolower($model->entity != 'filter')) {
                $entityModel = '\common\models\\' . ucfirst($model->entity);
                $entity = $model->entity;
                if ($model->entity == 'articles') {
                    $entity = 'article';
                }
                if ($model->entity == 'ncert') {
                    $entity = 'ncertArticles';
                }
                $entityModel = '\common\models\\' . ucfirst($entity);
                $model->page = $this->findSlug($entityModel, $model->entity, $model->entity_id);
                if (!empty($model->child_sub_page)) {
                    if ($model->entity == 'exam') {
                        $model->child_sub_page = $this->createSlug($model->child_sub_page);
                    } else if ($model->entity == 'college') {
                        $dropDownValue = '';
                        if ($model->sub_page == 'syllabus') {
                            $dropDownValue = $this->getCourseSlug($model->child_sub_page);
                        } else if ($model->sub_page == 'cut-off') {
                            $dropDownValue = $this->getExamSlug($model->child_sub_page);
                        } else if ($model->sub_page == 'admission') {
                            $dropDownValue = $model->child_sub_page;
                        }
                        $model->child_sub_page = $dropDownValue;
                    } else {
                        $model->child_sub_page = $this->createSlug($model->child_sub_page);
                    }
                }
            } else {
                $filterPageSeo = FilterPageSeo::findOne($model->entity_id);
                $entityModel = '\common\models\\' . 'FilterPageSeo';
                $model->page = $filterPageSeo->slug;
                // $model->entity_id = Filter::ENTITY_FILTER_ID; // defualt id is given.
            }
            if (!$model->validate()) {
                Yii::$app->session->setFlash('error', $this->getValidationErrors($model->errors));
                return $this->redirect(['index']);
            }
            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing Faq model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $entityModel = '\common\models\\';
        $model = $this->findModel($id);

        $entity = $model->entity;
        if ($entity == 'articles') {
            $entity = 'article';
        }
        $entityModel .= ucfirst($entity);

        if ($model->entity == 'ncert') {
            $entityModel = ucfirst('ncertArticles');
        }

        if ($model->load(Yii::$app->request->post())  && $model->validate()) {
            if ($model->oldattributes['entity_id'] !== $model->attributes['entity_id'] && strtolower($model->entity != 'filter')) {
                $model->page = $this->findSlug($entityModel, $model->entity, $model->entity_id);
            }

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                print_r($model->getErrors());
            }
        } else {
            if ($entity == 'filter') {
                $seoData = FilterPageSeo::find()->select(['id', 'entity'])->where(['like', 'slug', '%' . $model->page . '%', false])
                    ->andWhere(['status' => FilterPageSeo::STATUS_ACTIVE])
                    ->all();
                $data = $entityModel::find()->select(FAQ::$fields[strtolower($entity)])
                    ->where(['id' => $model->entity_id])
                    ->andWhere(['status' => FilterPageSeo::STATUS_ACTIVE])
                    ->all();
                $data = array_map(function ($list) {
                    return [
                        'id' => $list['id'],
                        'name' => isset($list['title']) ? $list['title'] : ($list['name'] ?? $list['display_name']),
                    ];
                }, $data);
                $data['0']['name'] = $seoData['0']->entity;
                // } else if ($entity == 'ncert') {
                //     $data = NcertArticles::find()->select(FAQ::$fields[strtolower($entity)])
                //         ->where(['id' => $model->entity_id])
                //         ->andWhere(['status' => NcertArticles::STATUS_ACTIVE])
                //         ->all();
            } else {
                $data = $entityModel::find()->select(FAQ::$fields[strtolower($entity)])
                    ->where(['id' => $model->entity_id])
                    ->andWhere(['status' => Faq::STATUS_ACTIVE])
                    ->all();
            }

            // dd($data);
            $data = array_map(function ($list) {
                return [
                    'id' => $list['id'],
                    'name' => isset($list['title']) ? $list['title'] : ($list['name'] ?? $list['display_name']),
                ];
            }, $data);

            $model->pageName = $data;

            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing Faq model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    /** public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }
     */

    /**
     * Finds the model based on its entity value received
     * And add faq for that model
     * @return $list of data fetched from model  Article/Category/Exam
     */
    public function actionGetList()
    {

        $entityFieldsArr = [
            'article' => ['id', 'title as name', 'title'],
            'board' => ['id', 'display_name as name', 'display_name'],
            'college' => ['id', 'name as name', 'name'],
            'exam' => ['id', 'display_name as name', 'display_name'],
            'course' => ['id', 'name as name', 'name'],
            'career' => ['id', 'name as name', 'name'],
            'olympiad' => ['id', 'name as name', 'name'],
            'NcertArticles' => ['id', 'title', 'title'],
            'NewsSubdomain' => ['id', 'name', 'name'],
            'others' => ['id', 'name', 'name'],
        ];

        $requestParam = Yii::$app->request->get('depdrop_parents');
        $requestParamQuery = Yii::$app->request->get('q');

        if ($requestParam != '') {
            $entity = $requestParam;
            if ($entity == 'articles') {
                $entity = 'article';
            }
            if ($entity == 'ncert') {
                $entity = 'NcertArticles';
            }
            if ($entity == 'news') {
                $entity = 'NewsSubdomain';
            }
            if ($entity == 'filter') {
                $model = '\common\models\\' . 'FilterPageSeo';
                $data = $model::find()->select(['id', 'slug'])->where(['like', 'slug', '%' . $requestParamQuery . '%', false])
                    ->andWhere(['status' => 1])
                    ->all();
                $data = array_map(function ($list) {
                    return [
                        'id' => $list['id'],
                        'text' => $list['slug']
                    ];
                }, $data);
                // }
                // if ($entity == 'ncert') {
                //     $data = $this->getNcertDetails($requestParamQuery);
            } else if ($entity == 'others') {
                $data[] = '';
            } else {
                list($id, $name, $column) = $entityFieldsArr[$entity];
                $model = '\common\models\\' . ucfirst($entity);
                $data = $model::find()->select([$id, $name])
                    ->where(['like', $column, '%' . $requestParamQuery . '%', false])
                    ->andWhere(['status' => 1])
                    ->all();
                $data = array_map(function ($list) {
                    return [
                        'id' => $list['id'],
                        'text' => $list['name'] ?? $list['title']
                    ];
                }, $data);
            }
            if (!empty($data)) {
                \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
                $out['results'] = array_values($data);
                return $out;
            } else {
                return false;
            }
        }
    }

    /**
     * Finds the Faq model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Faq the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Faq::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    /**
     * Finds the Faq model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Faq the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findSlug($entityModel, $entity, $entityId)
    {

        $pageSlug = $entityModel::find()->select(['slug'])->where(['id' => $entityId])->one();

        return $pageSlug->slug ?? null;
    }

    //to do
    public function actionGetSubPage()
    {
        $allBoardSubpages =  ArrayHelper::map(BoardPages::find()->where(['status'=>BoardPages::STATUS_ACTIVE])->andWhere(['is', 'parent_id', new \yii\db\Expression('null')])->all(), 'slug', 'name');

        $entitySubPageArr = [
            'article' => '',
            'board' => $allBoardSubpages,
            'college' => CollegeHelper::$subPages,
            'exam' => DataHelper::examContentList(),
            'course' => CourseHelper::$subPages,
            'career' => CareerHelper::$subPages,
            'ncert' => ''
        ];

        $requestParam = Yii::$app->request->post('depdrop_parents');

        if (!empty($requestParam)) {
            $entity = $requestParam[0];
            if ($entity == 'articles' || $entity == 'filter' || $entity == 'ncert') {
                return '';
            }

            $subpage = $entitySubPageArr[$entity];

            $subpage = array_map(function ($key, $value) {
                return [
                    'id' => $key,
                    'name' => $value
                ];
            }, array_keys($subpage), array_values($subpage));

            return json_encode(['output' => $subpage, 'selected' => '']);
        }
    }

    // public function getNcertDetails($requestParamQuery)
    // {
    //     $model = '\common\models\\' . 'NcertArticles';
    //     $data = $model::find()->select(['id', 'title'])
    //         ->where(['like', 'slug', '%' . $requestParamQuery . '%', false])
    //         ->andWhere(['status' => Faq::STATUS_ACTIVE])
    //         ->all();
    //     $data = array_map(function ($list) {
    //         return [
    //             'id' => $list['id'],
    //             'text' => $list['title']
    //         ];
    //     }, $data);
    //     return $data;
    // }

    /**
     * Load the child subPages options in dropDown for different categories
     * @return childSubpages
     */
    public function actionGetDropdownSubPage()
    {
        $entity = Yii::$app->request->get('depdrop_parents');
        $sub_page_param = Yii::$app->request->get('depdrop_child');
        $subpage_check = Yii::$app->request->get('subpage_check');
        $entity_id = Yii::$app->request->get('entity_id');
        $entities = ['course' => 'course_id', 'exam' => 'exam_id', 'board' => 'board_id', 'college' => 'entity_id'];
        $entities_content = ['course' => 'page', 'exam' => 'name', 'board' => 'page', 'college' => 'sub_page'];
        $entityDropdownSubPageArr = [];
        $subpage = [];
        $out = [];
        $data = 0;

        foreach ($entities as $entityKey => $entityVal) {
            if ($entity == $entityKey) {
                $subPageArr = [];
                $entityDropdownSubPageArr[$entityKey] = [];
                $tableName = $entityKey . '_content';
                $query = new Query;
                // $condition = ($entityKey == 'exam') ?  'slug' :  $entities_content[$entityKey];
                if ($entityKey == 'exam') {
                    $condition = 'slug';
                } else if ($entityKey == 'board') {
                    $condition = 'page_slug';
                } else {
                    $condition = $entities_content[$entityKey];
                }
                $parentPage = $query->select(['aa.' . $entities_content[$entityKey]])
                    ->from($tableName . ' as a')
                    ->innerJoin($tableName . ' as aa', 'aa.parent_id = a.id')
                    ->where(['a.' . $condition => $sub_page_param, 'aa.' . $entityVal => $entity_id, 'a.status' => Faq::STATUS_ACTIVE, 'aa.status' => Faq::STATUS_ACTIVE])
                    ->all();
                if ($entityKey == 'college') {
                    foreach ($parentPage as $key => $value) {
                        if ($sub_page_param == 'syllabus') {
                            $data = Course::find()
                                ->select(['id', 'short_name'])
                                ->parentOnly()
                                ->andWhere(['status' => Course::STATUS_ACTIVE, 'slug' => $value['sub_page']])
                                ->one();
                            if (!empty($data)) {
                                $subPageArr[$key + 1] = $data['short_name'];
                                $entityDropdownSubPageArr['college'] = ['syllabus' => $subPageArr];
                            }
                        } elseif ($sub_page_param == 'cut-off') {
                            $data = Exam::find()
                                ->select(['id', 'display_name'])
                                ->andWhere(['status' => Exam::STATUS_ACTIVE, 'slug' => $value['sub_page']])
                                ->one();
                            if (!empty($data)) {
                                $subPageArr[$key + 1] = $data['display_name'];
                                $entityDropdownSubPageArr['college'] = ['cut-off' => $subPageArr];
                            }
                        } elseif ($sub_page_param == 'admission') {
                            $subPageArr[$key + 1] = $value['sub_page'];
                            $entityDropdownSubPageArr['college'] = ['admission' => $subPageArr];
                        }
                    }
                } else {
                    foreach ($parentPage as $key => $value) {
                        $subPageArr[$key + 1] = $value[$entities_content[$entityKey]];
                    }
                    $entityDropdownSubPageArr[$entityKey] = [$sub_page_param => $subPageArr];
                }
            }
        }

        if (!empty($entity) && array_key_exists($entity, $entities)) {
            if (in_array($sub_page_param, array_keys($entityDropdownSubPageArr[$entity]))) {
                $subpage = $entityDropdownSubPageArr[$entity];
                $subpage = array_map(function ($key, $value) {
                    return [
                        'id' => $value,
                        'text' => $value
                    ];
                }, array_keys($subpage[$sub_page_param]), array_values($subpage[$sub_page_param]));
                \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
                $out['results'] = array_values($subpage);
                $data = count($out['results']);
                if ($subpage_check) {
                    return $data;
                }
                return $out;
            }
            if ($subpage_check) {
                return $data;
            }
        }
        return json_encode(['output' => $subpage, 'selected' => '']);
    }

    /**
     * Function to convert the given string to slug format
     * @param string $string
     * @return string $slug
     */
    private function createSlug($string)
    {
        $string = str_replace(' ', '-', $string); // Replaces all spaces with hyphens.
        return str_replace('--', '-', strtolower(preg_replace('/[^A-Za-z0-9\-]+/', '', $string))); //removes special character
    }

    /**
     * Function to fetch the slug wrt shortName
     * @param string $short_name
     * @return string $slug
     */
    private function getCourseSlug($short_name)
    {
        $course =  Course::find()->select(['id', 'short_name', 'slug'])->where(['short_name' => $short_name])->one();
        return $course->slug;
    }

    /**
     * Function to fetch the slug wrt displayName
     * @param string $displayName
     * @return string $slug
     */
    private function getExamSlug($display_name)
    {
        $exam = Exam::find()->select(['id', 'slug'])->where(['display_name' => $display_name])->one();
        return $exam->slug;
    }

    /**
     * Function to display errors
     * @param array $errors
     * @return string $validationMessage
     */
    protected function getValidationErrors($errors)
    {
        $validationErrros = [];
        foreach (array_keys($errors) as $error) {
            $validationErrros = array_merge($validationErrros, array_values($errors[$error]));
        }
        return reset($validationErrros);
    }
}

<?php

namespace backend\controllers;

use Yii;
use common\models\CollegeProgramContent;
use backend\models\CollegeProgramContentSearch;
use common\models\CollegeProgram;
use common\models\CollegeProgramExam;
use common\models\ContentTemplate;
use yii\db\Query;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use common\helpers\DataHelper;

/**
 * CollegeProgramContentController implements the CRUD actions for CollegeProgramContent model.
 */
class CollegeProgramContentController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CollegeProgramContent models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegeProgramContentSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CollegeProgramContent model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CollegeProgramContent model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CollegeProgramContent();
        $collegeCourse = CollegeProgram::findOne(['id' => Yii::$app->request->get('id')]);

        $postRequest = Yii::$app->request->post();

        /**** Check for cache and restricted urls in content ****/
        if (!empty($postRequest['CollegeProgramContent']['content'])) {
            $restrictedUrl = DataHelper::checkRestrictedUrl($postRequest['CollegeProgramContent']['content']);
        }
        if (!empty($restrictedUrl)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        /*********************************************************/

        $data = json_decode($model->qualification, true);
        if (!empty($data['eligibilty'])) {
            $model->qualification = $data['eligibilty'];
            unset($data['eligibilty']);

            if (!empty($data['exams'])) {
                $model->exams = $data['exams'];
                unset($data['exams']);
            }
        } else {
            $model->qualification = json_decode($model->qualification, true);
        }

        if (!empty(Yii::$app->request->post('CollegeProgramContent')['exams'])) {
            $model->exams = Yii::$app->request->post('CollegeProgramContent')['exams'];
        }

        $model->college_course_id = $collegeCourse->id;

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
                'collegeCourse' => $collegeCourse,
                'exams' => $this->getExams($model->college_course_id)
            ]);
        }
    }

    /**
     * Updates an existing CollegeProgramContent model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $collegeCourse = CollegeProgram::findOne(['id' => $model->college_course_id]);

        $data = json_decode($model->qualification, true);

        $postRequest = Yii::$app->request->post();

        /**** Check for cache and restricted urls in content ****/
        if (!empty($postRequest['CollegeProgramContent']['content'])) {
            $restrictedUrl = DataHelper::checkRestrictedUrl($postRequest['CollegeProgramContent']['content']);
        }
        if (!empty($restrictedUrl)) {
            return $this->redirect(Yii::$app->request->referrer);
        }
        /*********************************************************/

        if (!empty($data['eligibility'])) {
            $model->qualification = $data['eligibility'];
            unset($data['eligibility']);

            if (!empty($data['exams'])) {
                $model->exams = $data['exams'];
                unset($data['exams']);
            }
        } else {
            $model->qualification = json_decode($model->qualification, true);
        }

        if (!empty(Yii::$app->request->post('CollegeProgramContent')['exams'])) {
            $model->exams = Yii::$app->request->post('CollegeProgramContent')['exams'];
        }

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
                'collegeCourse' => $collegeCourse,
                'exams' => $this->getExams($model->college_course_id)
            ]);
        }
    }

    /**
     * Deletes an existing CollegeProgramContent model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    /*public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }*/

    /**
     * Finds the CollegeProgramContent model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegeProgramContent the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CollegeProgramContent::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    //get the exam
    public function getExams($id)
    {
        $query = new Query();
        $query->select(['e.display_name', 'e.slug'])
            ->from(CollegeProgramExam::tableName() . ' as ec')
            ->innerJoin('exam as e', 'e.id = ec.exam_id')
            ->where(['ec.college_program_id' => $id])
            ->andWhere(['e.status' => CollegeProgram::STATUS_ACTIVE]);

        $result = $query->all();
        $items = [];
        foreach ($result as $re) {
            $items[$re['display_name']] = $re['display_name'];
        }

        return $items;
    }

    public function actionTemplateList()
    {
        $request = Yii::$app->request;
        $q = $request->get('q');
        $models = ContentTemplate::find()
            ->where(['page' => 'pi'])
            ->andWhere(['like', 'name', $q])
            ->andWhere(['entity_type' => 'college'])
            ->andWhere(['status' => ContentTemplate::STATUS_ACTIVE])
            ->all();
        $items = [];
        foreach ($models as $model) {
            $items[] = ['id' => $model['id'], 'text' => $model['name']];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;

        $data['results'] = $items;

        return $data;
    }
}

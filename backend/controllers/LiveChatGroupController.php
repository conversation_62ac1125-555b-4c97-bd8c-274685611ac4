<?php

namespace backend\controllers;

use common\helpers\DataHelper;
use Yii;
use common\models\LiveChatGroup;
use common\models\LiveChatUserMessage;
use common\models\Exam;
use common\models\Board;
use common\models\Article;
use common\models\Faq;
use common\models\News;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\helpers\Json;
use common\services\GetStreamService;
use common\services\S3Service;
use Exception;
use ZipArchive;

/**
 * LiveChatGroupController implements the CRUD actions for LiveChatGroup model.
 */
class LiveChatGroupController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all LiveChatGroup models.
     * @return mixed
     */
    public function actionIndex()
    {
        $dataProvider = new ActiveDataProvider([
            'query' => LiveChatGroup::find()->orderBy(['created_at' => SORT_DESC]),
            'pagination' => [
                'pageSize' => 20,
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single LiveChatGroup model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);

        // Get recent messages for this group
        $messagesDataProvider = new ActiveDataProvider([
            'query' => LiveChatUserMessage::find()
                ->where(['group_id' => $id])
                ->orderBy(['created_at' => SORT_DESC]),
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        return $this->render('view', [
            'model' => $model,
            'messagesDataProvider' => $messagesDataProvider,
        ]);
    }

    /**
     * Creates a new LiveChatGroup model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new LiveChatGroup();

        if ($model->load(Yii::$app->request->post())) {
            if ($model->validate()) {
                // Start database transaction
                $transaction = Yii::$app->db->beginTransaction();

                try {
                    // Step 1: Save to local database
                    if ($model->save()) {
                        // Step 2: Create channel in GetStream
                        $this->createGetStreamChannel($model);

                        // Step 3: Send welcome message
                        $this->sendWelcomeMessage($model);

                        $transaction->commit();
                        Yii::$app->session->setFlash('success', 'Live chat group created successfully and integrated with GetStream.');
                        return $this->redirect(['view', 'id' => $model->id]);
                    } else {
                        throw new \Exception('Failed to save model: ' . json_encode($model->getErrors()));
                    }
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    Yii::error('Channel creation failed: ' . $e->getMessage(), __METHOD__);
                    Yii::$app->session->setFlash('error', 'Failed to create live chat group: ' . $e->getMessage());
                }
            } else {
                // Debug validation errors
                Yii::error('LiveChatGroup validation errors: ' . json_encode($model->getErrors()), __METHOD__);
                Yii::$app->session->setFlash('error', 'Validation failed. Errors: ' . json_encode($model->getErrors()));
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Create channel in GetStream when new group is created
     * @param LiveChatGroup $model
     * @throws \Exception
     */
    private function createGetStreamChannel($model)
    {
        try {
            /** @var \common\services\GetStreamService $getStreamService */
            $getStreamService = Yii::$app->getStreamService;

            // Create channel in GetStream
            $channelData = [
                'name' => $model->group_name,
                'description' => $model->description ?: 'Live discussion for ' . $model->group_name,
                'entity_type' => $model->entity,
                'entity_id' => $model->entity_id,
                'created_by' => Yii::$app->user->identity->name,
            ];

            $result = $getStreamService->createChannel(
                $model->channel_type ?: 'livestream',
                $model->channel_id,
                $channelData,
                Yii::$app->user->identity->name
            );

            Yii::info('GetStream channel created successfully: ' . json_encode($result), __METHOD__);
        } catch (\Exception $e) {
            Yii::error('Failed to create GetStream channel: ' . $e->getMessage(), __METHOD__);
            throw new \Exception('GetStream integration failed: ' . $e->getMessage());
        }
    }

    /**
     * Delete channel in GetStream when group is deleted
     * @param LiveChatGroup $model
     * @throws \Exception
     */
    private function deleteGetStreamChannel($model)
    {
        try {
            /** @var \common\services\GetStreamService $getStreamService */
            $getStreamService = Yii::$app->getStreamService;

            $result = $getStreamService->deleteChannel(
                $model->channel_type ?: 'livestream',
                $model->channel_id,
                ['hard_delete' => true]
            );

            if ($result) {
                Yii::info('GetStream channel deleted successfully: ' . json_encode($result), __METHOD__);
            } else {
                Yii::warning('GetStream channel delete returned false', __METHOD__);
            }
        } catch (\Exception $e) {
            Yii::error('Failed to delete GetStream channel: ' . $e->getMessage(), __METHOD__);
            throw new \Exception('GetStream integration failed: ' . $e->getMessage());
        }
    }

    /**
     * Send welcome message to new channel
     * @param LiveChatGroup $model
     */
    private function sendWelcomeMessage($model)
    {
        /** @var \common\services\GetStreamService $getStreamService */
        $getStreamService = Yii::$app->getStreamService;

        $welcomeText = "Welcome to the {$model->group_name} discussion! Feel free to ask your questions and share your thoughts.";

        // Send via GetStream
        $result = $getStreamService->sendMessage(
            $model->channel_type ?: 'livestream',
            $model->channel_id,
            [
                'text' => $welcomeText,
                'type' => 'system',
                'admin_message' => true
            ],
            'admin'
        );

        // Also save to local database
        $message = new LiveChatUserMessage();
        $message->group_id = $model->id;
        $message->message_id = $result['message']['id'] ?? 'admin_' . time() . '_' . rand(1000, 9999);
        $message->message_text = $welcomeText;
        $message->message_type = LiveChatUserMessage::MESSAGE_TYPE_TEXT;
        $message->user_name = 'Admin';
        $message->attachments = null;
        $message->user_id = 1;
        $message->user_email = '<EMAIL>';
        $message->is_admin_message = LiveChatUserMessage::ADMIN_MESSAGE_YES;

        if ($message->save()) {
        } else {
            Yii::error('Failed to save welcome message: ' . json_encode($message->getErrors()), __METHOD__);
        }
    }

    /**
     * Updates an existing LiveChatGroup model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $entityModel = '\common\models\\';
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            //update Channel name
            /** @var \common\services\GetStreamService $getStreamService */
            $getStreamService = Yii::$app->getStreamService;

            $getStreamService->updateChannel($model->channel_type ?: 'livestream', $model->channel_id, ['name' => $model->group_name]);

            Yii::$app->session->setFlash('success', 'Live chat group updated successfully.');
            return $this->redirect(['view', 'id' => $model->id]);
        }

        $entityModel .= ucfirst($model->entity);
        $data = $entityModel::find()->select(Faq::$fields[strtolower($model->entity)])
            ->where(['id' => $model->entity_id])
            ->andWhere(['status' => Faq::STATUS_ACTIVE])
            ->all();
        $data = array_map(function ($list) {
            return [
                'id' => $list['id'],
                'name' => isset($list['title']) ? $list['title'] : ($list['name'] ?? $list['display_name']),
            ];
        }, $data);

        $model->pageName = $data;

        return $this->render('update', [
            'model' => $model
        ]);
    }

    /**
     * Deletes an existing LiveChatGroup model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);

        // Delete channel in GetStream
        $this->deleteGetStreamChannel($model);

        // Soft delete model
        $model->softDelete();

        Yii::$app->session->setFlash('success', 'Live chat group deleted successfully.');

        return $this->redirect(['index']);
    }

    /**
     * AJAX action to get entities based on entity type
     * @return array
     */
    public function actionGetEntities()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $entityType = Yii::$app->request->post('entity_type');
        $entities = [];

        switch ($entityType) {
            // case LiveChatGroup::ENTITY_COLLEGE:
            //     $entities = College::find()
            //         ->select(['id', 'name'])
            //         ->where(['status' => 1])
            //         ->orderBy(['name' => SORT_ASC])
            //         ->asArray()
            //         ->all();
            //     break;

            case LiveChatGroup::ENTITY_EXAM:
                $entities = Exam::find()
                    ->select(['id', 'name'])
                    ->where(['status' => 1])
                    ->orderBy(['name' => SORT_ASC])
                    ->asArray()
                    ->all();
                break;

            case LiveChatGroup::ENTITY_BOARD:
                $entities = Board::find()
                    ->select(['id', 'name'])
                    ->where(['status' => 1])
                    ->orderBy(['name' => SORT_ASC])
                    ->asArray()
                    ->all();
                break;

            case LiveChatGroup::ENTITY_ARTICLE:
                $entities = Article::find()
                    ->select(['id', 'title as name'])
                    ->where(['status' => 1])
                    ->orderBy(['title' => SORT_ASC])
                    ->limit(100) // Limit to prevent too many results
                    ->asArray()
                    ->all();
                break;

            case LiveChatGroup::ENTITY_NEWS:
                $entities = News::find()
                    ->select(['id', 'name'])
                    ->where(['status' => 1])
                    ->orderBy(['name' => SORT_ASC])
                    ->limit(100) // Limit to prevent too many results
                    ->asArray()
                    ->all();
                break;
        }

        return $entities;
    }

    public function actionSendMessage($id)
    {
        $group = $this->findModel($id);

        if (Yii::$app->request->isPost) {
            $messageText = Yii::$app->request->post('message_text');
            $mentionedUsers = Yii::$app->request->post('mentioned_users');

            // Parse mentioned users
            $mentions = [];
            if ($mentionedUsers) {
                $mentions = Json::decode($mentionedUsers);
            }

            // Handle file uploads
            $uploadedFiles = $this->handleAdminFileUploads();

            if (!empty(trim(strip_tags($messageText))) || !empty($uploadedFiles)) {
                /** @var GetStreamService $getStreamService */
                $getStreamService = Yii::$app->getStreamService;

                // Format message with mentions
                $formattedMessage = $this->formatMessageWithMentions($messageText, $mentions);

                // Send to GetStream
                $result = $getStreamService->sendAdminMessageWithAttachments($group, $formattedMessage, $uploadedFiles);

                if ($result) {
                    // Send email notifications for mentions
                    if (!empty($mentions)) {
                        $this->sendMentionEmails($group, $mentions, $formattedMessage);
                    }

                    Yii::$app->session->setFlash('success', 'Admin message sent successfully.');
                } else {
                    Yii::$app->session->setFlash('error', 'Failed to send admin message.');
                }
            } else {
                Yii::$app->session->setFlash('error', 'Message cannot be empty.');
            }
        }

        return $this->redirect(['view', 'id' => $id]);
    }

    /**
     * Format message with mentions
     */
    private function formatMessageWithMentions($messageText, $mentions)
    {
        if (empty($mentions)) {
            return $messageText;
        }

        $mentionString = '';
        foreach ($mentions as $mention) {
            $mentionString .= '@' . $mention['name'] . ' ';
        }

        return $mentionString . $messageText;
    }

    /**
     * Send email notifications for mentioned users using EmailService
     */
    private function sendMentionEmails($group, $mentions, $messageText)
    {
        try {
            foreach ($mentions as $mention) {
                $this->sendMentionEmail($group, $mention, $messageText);
            }
        } catch (\Exception $e) {
            Yii::error('Failed to send mention emails: ' . $e->getMessage(), __METHOD__);
        }
    }

    private function sendMentionEmail($group, $mention, $messageText)
    {
        try {
            // Define entity mapping
            static $entityMap = [
                'exam' => 'exam',
                'board' => 'board',
                'article' => 'articles'
            ];

            $entity = '';
            $entitySlug = '';
            $entityType = $group->entity;

            // Check if entity type exists in mapping
            if (isset($entityMap[$entityType])) {
                $entity = $entityMap[$entityType];

                // Dynamic class instantiation
                $modelClass = '\\common\\models\\' . ucfirst($entityType);
                if (class_exists($modelClass)) {
                    $entityRecord = $modelClass::findOne($group->entity_id);
                    $entitySlug = $entityRecord ? $entityRecord->slug : '';
                }
            }

            $conversationUrl = "https://getmyuni.com/{$entity}/{$entitySlug}";
            $subject = "You were mentioned in {$group->group_name}";
            $cleanMessage = strip_tags($messageText);

            // Prepare email data
            $emailData = [
                'user_name' => $mention['name'],
                'group_name' => $group->group_name,
                'admin_message' => $cleanMessage,
                'conversation_url' => $conversationUrl
            ];

            return $this->sendMail(
                $mention['email'],
                $subject,
                $emailData,
                '@backend/views/mail/live-chat/live-chat-user-email'
            );
        } catch (\Exception $e) {
            Yii::error("Failed to send mention email to {$mention['email']}: " . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    public function sendMail($to, $subject, $data, $template)
    {
        try {
            // Handle both single email and array of emails
            $emails = is_array($to) ? $to : [$to];

            foreach ($emails as $email) {
                try {
                    Yii::$app->mailer->compose($template, $data)
                        ->setFrom(['<EMAIL>' => 'GetMyUni Live Chat'])
                        ->setTo($email)
                        ->setSubject($subject)
                        ->send();
                } catch (\Exception $e) {
                    Yii::error("Failed to send email to {$email}: " . $e->getMessage(), 'Notification Error');
                }
            }
        } catch (\Exception $e) {
            Yii::error('SendMail method error: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Handle file uploads for admin messages (same logic as frontend)
     */
    private function handleAdminFileUploads()
    {
        $uploadedFiles = [];

        if (empty($_FILES['files'])) {
            return $uploadedFiles;
        }

        $s3 = new S3Service(); // Your S3 wrapper
        $tempNames = Yii::$app->request->post('tempNames', []);
        $types = Yii::$app->request->post('types', []);

        foreach ($_FILES['files']['tmp_name'] as $i => $tmpName) {
            if ($_FILES['files']['error'][$i] === UPLOAD_ERR_OK) {
                $originalName = $_FILES['files']['name'][$i];
                $type = $types[$i] ?? $_FILES['files']['type'][$i];
                $tempName = $tempNames[$i] ?? $originalName;

                // Basic ZIP validation
                if (in_array($type, ['application/zip', 'application/x-zip-compressed'])) {
                    $zip = new ZipArchive();
                    if ($zip->open($tmpName) !== true) {
                        continue; // Skip invalid ZIP
                    }
                    $zip->close();
                }

                // normalize file names (spaces → _, lowercase)
                $originalName = strtolower(str_replace(' ', '_', $originalName));
                $tempName = strtolower(str_replace(' ', '_', $tempName));

                // Build S3 path (same as frontend)
                $s3Path = DataHelper::s3Path($tempName, 'live_chat');

                // Upload file to S3
                $s3->uploadFile($s3Path, $tmpName);
                $bucketBaseUrl = 'https://media.getmyuni.com/';

                $uploadedFiles[] = [
                    'name' => $originalName,
                    'type' => $type,
                    'url'  => $bucketBaseUrl . ltrim($s3Path, '/'),
                ];
            }
        }

        return $uploadedFiles;
    }

    /**
     * Get users who have participated in this channel
     */
    public function actionGetChannelUsers()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $groupId = Yii::$app->request->post('group_id');

        if (!$groupId) {
            return ['error' => 'Group ID is required'];
        }

        // Get unique users from messages in this group
        $users = LiveChatUserMessage::find()
            ->select(['user_name', 'user_email', 'user_id'])
            ->where(['group_id' => $groupId, 'is_deleted' => 0])
            ->andWhere(['!=', 'user_email', ''])
            ->andWhere(['!=', 'is_admin_message', 1]) // Exclude admin messages
            ->groupBy(['user_id'])
            ->orderBy(['user_name' => SORT_ASC])
            ->asArray()
            ->all();

        $formattedUsers = [];
        foreach ($users as $user) {
            $formattedUsers[] = [
                'name' => $user['user_name'],
                'email' => $user['user_email']
            ];
        }

        return [
            'users' => $formattedUsers,
            'count' => count($formattedUsers)
        ];
    }

    /**
     * Delete a message
     * @param integer $messageId
     * @return mixed
     */
    public function actionDeleteMessage($messageId)
    {
        $message = LiveChatUserMessage::findOne($messageId);

        if ($message) {
            $groupId = $message->group_id;

            /** @var \common\services\GetStreamService $getStreamService */
            $getStreamService = Yii::$app->getStreamService;

            $getStreamService->deleteMessage(
                $message->message_id,
                ['hard_delete' => true]
            );

            if ($message->softDelete()) {
                Yii::$app->session->setFlash('success', 'Message deleted successfully.');
            } else {
                Yii::$app->session->setFlash('error', 'Failed to delete message.');
            }
            return $this->redirect(['view', 'id' => $groupId]);
        }

        throw new NotFoundHttpException('Message not found.');
    }

    /**
     * Manage all messages across groups
     * @return mixed
     */
    public function actionMessages()
    {
        $dataProvider = new ActiveDataProvider([
            'query' => LiveChatUserMessage::find()
                ->with(['group'])
                ->where(['is_deleted' => 0])
                ->orderBy(['created_at' => SORT_DESC]),
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        return $this->render('messages', [
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Respond to a user message
     * @param integer $messageId
     * @return mixed
     */
    public function actionRespondToMessage($messageId)
    {
        $originalMessage = LiveChatUserMessage::findOne($messageId);

        if (!$originalMessage) {
            throw new NotFoundHttpException('Message not found.');
        }

        if (Yii::$app->request->isPost) {
            $responseText = Yii::$app->request->post('response_text');

            if (!empty($responseText)) {
                $group = $originalMessage->group;
                /** @var GetStreamService $getStreamService */
                $getStreamService = Yii::$app->getStreamService;

                // Format response to reference original message
                $formattedResponse = 'Admin Response: ' . $responseText;

                $result = $getStreamService->sendAdminMessage($group, $formattedResponse);

                if ($result) {
                    Yii::$app->session->setFlash('success', 'Admin response sent successfully.');
                } else {
                    Yii::$app->session->setFlash('error', 'Failed to send admin response.');
                }
            } else {
                Yii::$app->session->setFlash('error', 'Response text cannot be empty.');
            }
        }

        return $this->redirect(['messages']);
    }

    /**
     * Test page for GetStream functionality
     * @return mixed
     */
    public function actionTest()
    {
        return $this->render('test');
    }

    /**
     * Debug GetStream connection and show database messages
     * @return mixed
     */
    public function actionDebugGetstream()
    {
        /** @var GetStreamService $getStreamService */
        $getStreamService = Yii::$app->getStreamService;

        $debug = [
            'service_available' => $getStreamService->isAvailable(),
            'enabled' => $getStreamService->enabled,
            'api_key' => $getStreamService->apiKey ? (substr($getStreamService->apiKey, 0, 8) . '...') : 'NOT SET',
            'api_secret' => $getStreamService->apiSecret ? (substr($getStreamService->apiSecret, 0, 8) . '...') : 'NOT SET',
            'app_id' => $getStreamService->appId,
            'client_status' => $getStreamService->getClient() ? 'INITIALIZED' : 'NULL',
        ];

        // Test creating a user token
        try {
            $token = $getStreamService->createUserToken('test_user_123');
            $debug['token_test'] = $token ? 'SUCCESS' : 'FAILED';
        } catch (\Exception $e) {
            $debug['token_test'] = 'ERROR: ' . $e->getMessage();
        }

        // Show recent messages from database
        $recentMessages = \common\models\LiveChatUserMessage::find()
            ->orderBy(['created_on' => SORT_DESC])
            ->limit(10)
            ->all();

        $debug['recent_messages_count'] = count($recentMessages);
        $debug['recent_messages'] = [];

        foreach ($recentMessages as $msg) {
            $debug['recent_messages'][] = [
                'id' => $msg->id,
                'group_id' => $msg->group_id,
                'message_id' => $msg->message_id,
                'message_text' => substr($msg->message_text, 0, 50) . '...',
                'user_name' => $msg->user_name,
                'is_admin' => $msg->is_admin_message,
                'created_on' => $msg->created_on,
            ];
        }

        // Show groups
        $groups = \common\models\LiveChatGroup::find()->limit(5)->all();
        $debug['groups'] = [];
        foreach ($groups as $group) {
            $debug['groups'][] = [
                'id' => $group->id,
                'name' => $group->name,
                'channel_id' => $group->channel_id,
                'entity' => $group->entity,
                'entity_id' => $group->entity_id,
                'status' => $group->status,
            ];
        }

        return $this->asJson($debug);
    }

    /**
     * Finds the LiveChatGroup model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return LiveChatGroup the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = LiveChatGroup::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}

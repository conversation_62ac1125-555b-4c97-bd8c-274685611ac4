<?php

namespace backend\controllers;

use Yii;
use yii\web\Controller;
use yii\web\Response;
use yii\data\ActiveDataProvider;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;
use common\models\LiveChatGroup;
use common\models\LiveChatUserMessage;
use yii\web\NotFoundHttpException;

/**
 * Live Chat Controller for Admin Panel
 * Manage GetStream chat interactions and monitor user activity
 */
class LiveChatController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete-group' => ['POST'],
                    'delete-message' => ['POST'],
                    'send-admin-message' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Display all chat groups
     * @return string
     */
    public function actionIndex()
    {
        $dataProvider = new ActiveDataProvider([
            'query' => LiveChatGroup::find()->orderBy(['created_on' => SORT_DESC]),
            'pagination' => [
                'pageSize' => 20,
            ],
        ]);

        // Get GetStream service status
        $getStreamService = Yii::$app->getStreamService;
        $serviceStatus = [
            'enabled' => $getStreamService->enabled,
            'available' => $getStreamService->isAvailable(),
            'appId' => $getStreamService->appId,
            'hasApiKey' => !empty($getStreamService->apiKey) && $getStreamService->apiKey !== 'YOUR_API_KEY_HERE',
            'hasApiSecret' => !empty($getStreamService->apiSecret) && $getStreamService->apiSecret !== 'YOUR_API_SECRET_HERE',
        ];

        return $this->render('index', [
            'dataProvider' => $dataProvider,
            'serviceStatus' => $serviceStatus,
        ]);
    }

    /**
     * View chat group details and messages
     * @param int $id
     * @return string
     */
    public function actionView($id)
    {
        $group = $this->findGroup($id);
        
        $messagesProvider = new ActiveDataProvider([
            'query' => LiveChatUserMessage::find()
                ->where(['group_id' => $id])
                ->orderBy(['created_on' => SORT_ASC]),
            'pagination' => [
                'pageSize' => 50,
            ],
        ]);

        // Get GetStream channel info if available
        $channelInfo = null;
        $getStreamService = Yii::$app->getStreamService;
        
        if ($getStreamService->isAvailable() && $group->channel_id) {
            try {
                $channelInfo = $getStreamService->getMessages(
                    $group->channel_type ?: 'livestream',
                    $group->channel_id,
                    ['limit' => 10]
                );
            } catch (\Exception $e) {
                Yii::warning('Failed to get GetStream channel info: ' . $e->getMessage());
            }
        }

        return $this->render('view', [
            'group' => $group,
            'messagesProvider' => $messagesProvider,
            'channelInfo' => $channelInfo,
        ]);
    }

    /**
     * Send admin message to a chat group
     * @param int $id
     * @return Response
     */
    public function actionSendAdminMessage($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $group = $this->findGroup($id);
        $messageText = Yii::$app->request->post('message');
        
        if (empty($messageText)) {
            return ['success' => false, 'error' => 'Message text is required'];
        }

        $getStreamService = Yii::$app->getStreamService;
        
        try {
            // Send via GetStream if available
            if ($getStreamService->isAvailable()) {
                $result = $getStreamService->sendAdminMessage($group, $messageText);
                if (!$result) {
                    throw new \Exception('Failed to send message via GetStream');
                }
            } else {
                // Store locally if GetStream is not available
                $message = new LiveChatUserMessage();
                $message->group_id = $group->id;
                $message->message_id = 'admin_' . time() . '_' . rand(1000, 9999);
                $message->message_text = $messageText;
                $message->message_type = LiveChatUserMessage::MESSAGE_TYPE_TEXT;
                $message->user_name = 'Admin';
                $message->is_admin_message = LiveChatUserMessage::ADMIN_MESSAGE_YES;
                
                if (!$message->save()) {
                    throw new \Exception('Failed to save admin message');
                }
            }
            
            return ['success' => true, 'message' => 'Admin message sent successfully'];
        } catch (\Exception $e) {
            Yii::error('Failed to send admin message: ' . $e->getMessage(), __METHOD__);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get chat statistics
     * @return string
     */
    public function actionStats()
    {
        // Total groups
        $totalGroups = LiveChatGroup::find()->count();
        $activeGroups = LiveChatGroup::find()->where(['status' => LiveChatGroup::STATUS_ACTIVE])->count();
        
        // Total messages
        $totalMessages = LiveChatUserMessage::find()->count();
        $adminMessages = LiveChatUserMessage::find()
            ->where(['is_admin_message' => LiveChatUserMessage::ADMIN_MESSAGE_YES])
            ->count();
        
        // Messages by entity type
        $messagesByEntity = LiveChatGroup::find()
            ->select(['entity', 'COUNT(*) as count'])
            ->groupBy('entity')
            ->asArray()
            ->all();
        
        // Recent activity (last 7 days)
        $recentMessages = LiveChatUserMessage::find()
            ->where(['>=', 'created_on', date('Y-m-d H:i:s', strtotime('-7 days'))])
            ->count();
        
        // Messages per day (last 30 days)
        $dailyStats = LiveChatUserMessage::find()
            ->select(['DATE(created_on) as date', 'COUNT(*) as count'])
            ->where(['>=', 'created_on', date('Y-m-d H:i:s', strtotime('-30 days'))])
            ->groupBy('DATE(created_on)')
            ->orderBy('date ASC')
            ->asArray()
            ->all();

        return $this->render('stats', [
            'totalGroups' => $totalGroups,
            'activeGroups' => $activeGroups,
            'totalMessages' => $totalMessages,
            'adminMessages' => $adminMessages,
            'messagesByEntity' => $messagesByEntity,
            'recentMessages' => $recentMessages,
            'dailyStats' => $dailyStats,
        ]);
    }

    /**
     * Export chat data
     * @param int $groupId
     * @return Response
     */
    public function actionExport($groupId = null)
    {
        $query = LiveChatUserMessage::find()
            ->joinWith('group')
            ->orderBy(['live_chat_user_message.created_on' => SORT_ASC]);
        
        if ($groupId) {
            $query->where(['group_id' => $groupId]);
            $filename = 'chat_export_group_' . $groupId . '_' . date('Y-m-d') . '.csv';
        } else {
            $filename = 'chat_export_all_' . date('Y-m-d') . '.csv';
        }
        
        $messages = $query->all();
        
        // Create CSV content
        $csv = "Group ID,Group Name,Entity,Entity ID,Message ID,User Name,User Email,Message Text,Message Type,Is Admin,Created On\n";
        
        foreach ($messages as $message) {
            $csv .= sprintf(
                "%d,\"%s\",\"%s\",%d,\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",%s,\"%s\"\n",
                $message->group_id,
                addslashes($message->group->group_name ?? ''),
                addslashes($message->group->entity ?? ''),
                $message->group->entity_id ?? 0,
                addslashes($message->message_id),
                addslashes($message->user_name ?? ''),
                addslashes($message->user_email ?? ''),
                addslashes($message->message_text),
                addslashes($message->message_type ?? ''),
                $message->is_admin_message == LiveChatUserMessage::ADMIN_MESSAGE_YES ? 'Yes' : 'No',
                $message->created_on
            );
        }
        
        // Send as download
        Yii::$app->response->format = Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/csv');
        Yii::$app->response->headers->add('Content-Disposition', 'attachment; filename="' . $filename . '"');
        
        return $csv;
    }

    /**
     * Sync messages from GetStream
     * @return Response
     */
    public function actionSyncMessages()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $getStreamService = Yii::$app->getStreamService;
        
        if (!$getStreamService->isAvailable()) {
            return ['success' => false, 'error' => 'GetStream service not available'];
        }
        
        $syncCount = 0;
        $errors = [];
        
        // Get all active groups with channel IDs
        $groups = LiveChatGroup::find()
            ->where(['status' => LiveChatGroup::STATUS_ACTIVE])
            ->andWhere(['!=', 'channel_id', ''])
            ->all();
        
        foreach ($groups as $group) {
            try {
                $channelData = $getStreamService->getMessages(
                    $group->channel_type ?: 'livestream',
                    $group->channel_id,
                    ['limit' => 50]
                );
                
                if (isset($channelData['messages'])) {
                    foreach ($channelData['messages'] as $messageData) {
                        // Check if message already exists
                        $existingMessage = LiveChatUserMessage::findOne(['message_id' => $messageData['id']]);
                        if (!$existingMessage) {
                            $message = new LiveChatUserMessage();
                            $message->group_id = $group->id;
                            $message->message_id = $messageData['id'];
                            $message->message_text = $messageData['text'] ?? '';
                            $message->message_type = $messageData['type'] ?? 'regular';
                            $message->user_name = $messageData['user']['name'] ?? $messageData['user']['id'] ?? 'Unknown';
                            $message->user_email = $messageData['user']['email'] ?? null;
                            $message->is_admin_message = isset($messageData['admin_message']) ?
                                LiveChatUserMessage::ADMIN_MESSAGE_YES : LiveChatUserMessage::ADMIN_MESSAGE_NO;
                            
                            if ($message->save()) {
                                $syncCount++;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                $errors[] = "Group {$group->id}: " . $e->getMessage();
            }
        }
        
        return [
            'success' => true,
            'syncCount' => $syncCount,
            'errors' => $errors,
            'message' => "Synced {$syncCount} messages" . (count($errors) > 0 ? ' with ' . count($errors) . ' errors' : '')
        ];
    }

    /**
     * Find chat group by ID
     * @param int $id
     * @return LiveChatGroup
     * @throws NotFoundHttpException
     */
    protected function findGroup($id)
    {
        if (($model = LiveChatGroup::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested chat group does not exist.');
    }
}

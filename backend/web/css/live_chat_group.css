.description-block {
    text-align: center;
    padding: 10px 0;
}

.description-header {
    margin: 10px 0 5px 0;
    font-size: 24px;
    font-weight: bold;
}

.description-text {
    font-size: 12px;
    text-transform: uppercase;
    color: #999;
}

.border-right {
    border-right: 1px solid #f0f0f0;
}

.font-weight-bold {
    font-weight: bold;
}

/* ===== ADMIN CHAT CONTAINER ===== */
.admin-chat-container {
    max-width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== CHAT HEADER ===== */
.chat-header {
    background: #dce0e5;
    backdrop-filter: blur(20px);
    padding: 9px 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.header-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.chat-title {
    flex: 1;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.chat-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #68d391;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #68d391;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* ===== SCROLLABLE EDITOR UPDATES ===== */
.editor-container {
    background: white;
    margin: 20px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    max-height: 400px;
    /* Maximum height before scroll */
}

.editor-wrapper {
    position: relative;
    max-height: 300px;
    /* Maximum content height */
    overflow-y: auto;
    /* Enable vertical scrolling */
}

/* ===== TINYMCE SCROLLABLE UPDATES ===== */
.tox-tinymce {
    border: none !important;
    border-radius: 16px !important;
    max-height: 300px !important;
}

.tox-edit-area__iframe {
    background: white !important;
    min-height: 150px !important;
    max-height: 250px !important;
    overflow-y: auto !important;
}

/* ===== SCROLLABLE ATTACHMENTS PREVIEW ===== */
.attachments-preview {
    margin: 0 20px 20px;
    display: none;
    max-height: 8vh;
    /* Maximum height for file previews */
    overflow-y: auto;
    /* Enable scrolling for multiple files */
    padding-right: 5px;
    /* Space for scrollbar */
}

.attachments-preview::-webkit-scrollbar {
    width: 6px;
}

.attachments-preview::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.attachments-preview::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 3px;
}

.attachments-preview::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

/* ===== RESPONSIVE CONTAINER HEIGHT ===== */
.admin-chat-container {
    max-width: 100%;
    max-height: 80vh;
    /* Maximum viewport height */
    overflow-y: auto;
    /* Enable scrolling for entire container */
    background: #dfe3e8;
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== CUSTOM SCROLLBAR STYLING ===== */
.admin-chat-container::-webkit-scrollbar,
.editor-wrapper::-webkit-scrollbar,
.tox-edit-area__iframe::-webkit-scrollbar {
    width: 8px;
}

.admin-chat-container::-webkit-scrollbar-track,
.editor-wrapper::-webkit-scrollbar-track,
.tox-edit-area__iframe::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.admin-chat-container::-webkit-scrollbar-thumb,
.editor-wrapper::-webkit-scrollbar-thumb,
.tox-edit-area__iframe::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.admin-chat-container::-webkit-scrollbar-thumb:hover,
.editor-wrapper::-webkit-scrollbar-thumb:hover,
.tox-edit-area__iframe::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* ===== MOBILE RESPONSIVE SCROLLING ===== */
@media (max-width: 768px) {
    .admin-chat-container {
        max-height: 90vh;
        margin: 5px;
        border-radius: 16px;
    }

    .editor-container {
        margin: 16px;
        max-height: 300px;
    }

    .editor-wrapper {
        max-height: 200px;
    }

    .attachments-preview {
        max-height: 120px;
        margin: 0 16px 16px;
    }
}

/* ===== AUTO-RESIZE WITH SCROLL FALLBACK ===== */
.auto-resize-textarea {
    min-height: 60px;
    max-height: 200px;
    overflow-y: auto;
    resize: none;
    transition: height 0.2s ease;
}

/* ===== SCROLL INDICATORS ===== */
.scroll-indicator {
    position: absolute;
    right: 10px;
    bottom: 10px;
    background: rgba(102, 126, 234, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
}

.editor-wrapper:hover .scroll-indicator,
.attachments-preview:hover .scroll-indicator {
    opacity: 1;
}

/* ===== ATTACHMENTS PREVIEW ===== */
.attachments-preview {
    margin: 0 20px 20px;
    display: none;
}

.attachments-preview.has-files {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
}

.attachment-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 0px 11px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.attachment-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.attachment-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.attachment-info {
    flex: 1;
}

.attachment-name {
    font-weight: 500;
    color: #2d3748;
    font-size: 14px;
    margin-bottom: 2px;
}

.attachment-size {
    font-size: 12px;
    color: #718096;
}

.remove-attachment {
    width: 24px;
    height: 24px;
    border: none;
    background: #fed7d7;
    color: #e53e3e;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.remove-attachment:hover {
    background: #feb2b2;
    transform: scale(1.1);
}

/* ===== MODERN INPUT BAR ===== */
.chat-input-container {
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.input-bar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 50px;
    padding: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.input-bar:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* ===== ATTACHMENT BUTTON ===== */
.attachment-button {
    position: relative;
    width: 34px;
    height: 34px;
    background: linear-gradient(135deg, #92a0e3, #887899);
    border: none;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.attachment-button:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.attachment-button:active {
    transform: scale(0.95);
}

/* ===== INPUT STATUS ===== */
.input-status {
    flex: 1;
    text-align: center;
    padding: 0 16px;
}

#input-status-text {
    font-size: 14px;
    color: #718096;
    font-style: italic;
    transition: all 0.3s ease;
}

.input-status.ready #input-status-text {
    color: #667eea;
    font-weight: 500;
}

/* ===== SEND BUTTON ===== */
.send-button {
    position: relative;
    width: 34px;
    height: 34px;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border: none;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.send-button:disabled {
    background: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.send-button:not(:disabled):hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.send-button:not(:disabled):active {
    transform: scale(0.95);
}

.send-button.loading {
    pointer-events: none;
}

.send-button.loading svg {
    opacity: 0;
}

.send-button.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* ===== TOOLTIPS ===== */
.button-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #2d3748;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    margin-bottom: 8px;
    z-index: 1000;
}

.button-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #2d3748;
}

.attachment-button:hover .button-tooltip,
.send-button:hover .button-tooltip {
    opacity: 1;
    visibility: visible;
}

#char-counter {
    font-size: 12px;
    color: #666;
    position: relative;
    top: -17px;
    float: right;
    margin-right: 10px;
}

/* ===== ANIMATIONS ===== */
.fade-in {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mentions Button */
.mentions-button {
    position: relative;
    width: 34px;
    height: 34px;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    border: none;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.mentions-button:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
}

/* Mentioned Users Display */
.mentioned-users {
    margin: 16px 20px;
    padding: 12px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    display: none;
}

.mentioned-users.has-mentions {
    display: block;
}

.mention-tag {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin: 2px 4px;
    position: relative;
}

.mention-tag .remove-mention {
    background: none;
    border: none;
    color: white;
    margin-left: 4px;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
}

/* Mentions Modal */
.mentions-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.mentions-modal-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    /* max-height: 100vh; */
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.mentions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
    background: #f7fafc;
}

.mentions-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.close-mentions {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #718096;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-mentions:hover {
    background: #e2e8f0;
    color: #2d3748;
}

.mentions-body {
    padding: 20px 24px;
    /* max-height: 400px; */
    /* overflow-y: auto; */
}

.search-users input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 16px;
    transition: border-color 0.2s ease;
}

.search-users input:focus {
    outline: none;
    border-color: #667eea;
}

.users-list {
    max-height: 300px;
    overflow-y: auto;
}

.user-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e2e8f0;
}

.user-item:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.user-item.selected {
    background: #ebf8ff;
    border-color: #3182ce;
}

.user-checkbox {
    margin-right: 12px;
}

.user-info {
    flex: 1;
    margin-left: 15px;
}

.user-name {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 2px;
}

.user-email {
    font-size: 12px;
    color: #718096;
}

.mentions-footer {
    padding: 16px 24px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    background: #f7fafc;
}

.mentions-footer .btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
    border: none;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-primary {
    background: #667eea;
    color: white;
    border: none;
}

.btn-primary:hover {
    background: #5a67d8;
}

/* Select All Section */
.select-all-section {
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
    background: #f7fafc;
    margin-bottom: 8px;
}

.select-all-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
    color: #4a5568;
    margin: 0;
}

.select-all-label input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.select-all-label:hover {
    color: #2d3748;
}

/* Indeterminate checkbox styling */
.select-all-label input[type="checkbox"]:indeterminate {
    background-color: #667eea;
    border-color: #667eea;
}

/* Remove All button styling */
.remove-all-mentions {
    background: transparent !important;
    border: none !important;
    color: #e53e3e !important;
    cursor: pointer !important;
    font-weight: bold !important;
    font-size: 12px !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    transition: background-color 0.2s ease !important;
}

.remove-all-mentions:hover {
    background: rgba(229, 62, 62, 0.1) !important;
    color: #c53030 !important;
}


/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .admin-chat-container {
        margin: 10px;
        border-radius: 16px;
    }

    .chat-header {
        padding: 16px 20px;
    }

    .editor-container {
        margin: 16px;
    }

    .chat-input-container {
        padding: 16px;
    }

    .input-bar {
        padding: 6px;
    }

    .attachment-button,
    .send-button {
        width: 40px;
        height: 40px;
    }
}

/* ===== SUCCESS/ERROR MESSAGES ===== */
.message-alert {
    margin: 16px 20px;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    animation: slideIn 0.3s ease;
}

.message-alert.success {
    background: rgba(72, 187, 120, 0.1);
    color: #2f855a;
    border: 1px solid rgba(72, 187, 120, 0.3);
}

.message-alert.error {
    background: rgba(229, 62, 62, 0.1);
    color: #c53030;
    border: 1px solid rgba(229, 62, 62, 0.3);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}
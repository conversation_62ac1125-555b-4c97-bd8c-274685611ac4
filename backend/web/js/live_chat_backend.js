// Global variables
window.yiiCsrfParam = $('meta[name=csrf-param]').attr('content');
window.yiiCsrfToken = $('meta[name=csrf-token]').attr('content');

// Initialize TinyMCE with modern config
tinymce.init({
    selector: '#admin-message-editor',
    height: 180,
    menubar: false,
    plugins: 'lists textcolor autolink link code',
    toolbar: 'bold italic underline | forecolor | numlist bullist | link | code',
    toolbar_mode: 'sliding',
    resize: false,
    branding: false,

    // PREVENT INLINE STYLES FROM BEING GENERATED
    convert_fonts_to_spans: false,
    remove_trailing_brs: true,
    verify_html: false,
    cleanup: true,
    cleanup_on_startup: true,
    trim_span_elements: true,
    remove_redundant_brs: true,

    // RESTRICT VALID ELEMENTS TO PREVENT STYLE ATTRIBUTES
    valid_elements: 'p,b,strong,i,em,u,ul,ol,li,br,a[href|target|title]',
    invalid_elements: 'style,span[style]',
    forced_root_block: 'p',
    content_css: false,

    // CLEAN PASTE CONTENT
    paste_remove_styles: true,
    paste_strip_class_attributes: 'all',
    paste_retain_style_properties: '',

    content_style: `
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            font-size: 14px; 
            line-height: 1.6;
            color: #2d3748;
            padding: 16px;
            margin: 0;
        }
        p { margin: 0 0 8px 0; }
        ul, ol { margin: 8px 0; padding-left: 24px; }
    `,
    placeholder: 'Type your message here...',
    setup: function (editor) {
        editor.on('change keyup paste', function () {
            // Raw HTML
            let html = editor.getContent();

            // Clean styles & span wrappers
            let cleanHtml = html
                .replace(/\s*style\s*=\s*"[^"]*"/gi, '')
                .replace(/\s*style\s*=\s*'[^']*'/gi, '')
                .replace(/<span[^>]*>([^<]*)<\/span>/gi, '$1');

            if (cleanHtml !== html) {
                editor.setContent(cleanHtml, { format: 'raw' });
            }

            // Plain text count
            let text = editor.getContent({ format: 'text' });
            let textCount = text.length;

            // Raw HTML count
            let htmlCount = editor.getContent().length;

            // Update counter UI
            const counterEl = document.getElementById('char-counter');
            if (counterEl) {
                counterEl.textContent = `Text: ${textCount} / 280 | HTML: ${htmlCount} chars`;

                if (htmlCount > 280) {
                    counterEl.style.color = 'red';
                    counterEl.style.fontWeight = 'bold';
                } else {
                    counterEl.style.color = '#666';
                    counterEl.style.fontWeight = 'normal';
                }
            }

            editor.save();
            updateInputState();
        });
    }

});


// Enhanced Admin File Manager with Mentions
class ModernAdminFileManager {
    constructor() {
        this.selectedFiles = [];
        this.isSubmitting = false; // Prevent double submission
        this.mentionedUsers = []; // NEW: Store mentioned users
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateInputState();
    }

    bindEvents() {
        // Attachment button - prevent multiple bindings
        $('#attach-files-btn').off('click').on('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            $('#admin-file-input').click();
        });

        // File selection
        $('#admin-file-input').off('change').on('change', (e) => {
            this.handleFileSelection(e.target.files);
        });

        // Send button click handler
        $('#send-message-btn').off('click').on('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            if (!this.isSubmitting) {
                this.submitMessage();
            }
        });

        // Form submission handler - prevent browser default
        $('#admin-message-form').off('submit').on('submit', (e) => {
            e.preventDefault();
            e.stopImmediatePropagation();

            if (!this.isSubmitting) {
                this.submitMessage();
            }
            return false;
        });

        // NEW: Mentions functionality
        this.bindMentionEvents();
    }

    bindMentionEvents() {
        // Open mentions modal
        $('#mentions-btn').off('click').on('click', (e) => {
            e.preventDefault();
            this.openMentionsModal();
        });

        // Close mentions modal
        $('#close-mentions, #cancel-mentions').off('click').on('click', () => {
            this.closeMentionsModal();
        });

        // Add mentions
        $('#add-mentions').off('click').on('click', () => {
            this.addSelectedMentions();
        });

        // User search
        $('#user-search').off('input').on('input', (e) => {
            this.filterUsers(e.target.value);
        });

        // NEW: Select All checkbox functionality
        $('#select-all-users').off('change').on('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // Click outside modal to close
        $('#mentions-modal').off('click').on('click', (e) => {
            if (e.target.id === 'mentions-modal') {
                this.closeMentionsModal();
            }
        });
    }

    async openMentionsModal() {
        try {
            // Load users for this group
            const groupId = $('#admin-message-form').data('group-id');
            console.log('Loading users for group:', groupId); // Debug log

            const response = await $.ajax({
                url: '/live-chat-group/get-channel-users',
                method: 'POST',
                data: {
                    group_id: groupId,
                    [window.yiiCsrfParam]: window.yiiCsrfToken
                }
            });

            console.log('Users loaded:', response); // Debug log
            this.displayUsers(response.users || []);
            $('#mentions-modal').show();
        } catch (error) {
            console.error('Failed to load users:', error);
            this.showAlert('Failed to load users for mentions.', 'error');
        }
    }

    closeMentionsModal() {
        $('#mentions-modal').hide();
        $('#user-search').val('');
    }

    displayUsers(users) {
        const container = $('#users-list');
        container.empty();

        if (users.length === 0) {
            container.html('<div class="no-users">No users found in this channel.</div>');
            $('#select-all-section').hide(); // Hide select all if no users
            return;
        }

        // Show select all section if users exist
        $('#select-all-section').show();

        users.forEach(user => {
            const isSelected = this.mentionedUsers.some(mu => mu.email === user.email);
            const item = $(`
        <div class="user-item ${isSelected ? 'selected' : ''}" data-email="${user.email}" data-name="${user.name}">
            <input type="checkbox" class="user-checkbox" ${isSelected ? 'checked' : ''}>
            <div class="user-info">
                <div class="user-name">${user.name}</div>
                <div class="user-email">${user.email}</div>
            </div>
        </div>
    `);

            // Single unified click handler
            item.on('click', (e) => {
                const checkbox = item.find('.user-checkbox');

                // If clicking directly on checkbox, let it handle itself
                if ($(e.target).is('.user-checkbox')) {
                    const isChecked = $(e.target).is(':checked');
                    item.toggleClass('selected', isChecked);
                    this.updateSelectAllState(); // Update select all state
                    return;
                }

                // If clicking anywhere else, toggle the checkbox
                const currentState = checkbox.is(':checked');
                const newState = !currentState;

                checkbox.prop('checked', newState);
                item.toggleClass('selected', newState);
                this.updateSelectAllState(); // Update select all state
            });

            container.append(item);
        });

        // Update select all checkbox state based on current selections
        this.updateSelectAllState();
    }

    // NEW: Toggle all users selection
    toggleSelectAll(checked) {
        $('.user-item:visible').each(function () {
            const checkbox = $(this).find('.user-checkbox');
            checkbox.prop('checked', checked);

            if (checked) {
                $(this).addClass('selected');
            } else {
                $(this).removeClass('selected');
            }
        });
    }

    // NEW: Update select all checkbox state
    updateSelectAllState() {
        const visibleItems = $('.user-item:visible');
        const checkedItems = visibleItems.filter('.selected');
        const selectAllCheckbox = $('#select-all-users');

        if (visibleItems.length === 0) {
            selectAllCheckbox.prop('indeterminate', false);
            selectAllCheckbox.prop('checked', false);
        } else if (checkedItems.length === 0) {
            selectAllCheckbox.prop('indeterminate', false);
            selectAllCheckbox.prop('checked', false);
        } else if (checkedItems.length === visibleItems.length) {
            selectAllCheckbox.prop('indeterminate', false);
            selectAllCheckbox.prop('checked', true);
        } else {
            selectAllCheckbox.prop('indeterminate', true);
            selectAllCheckbox.prop('checked', false);
        }
    }

    filterUsers(searchTerm) {
        const term = searchTerm.toLowerCase();
        $('.user-item').each(function () {
            const name = $(this).find('.user-name').text().toLowerCase();
            const email = $(this).find('.user-email').text().toLowerCase();

            if (name.includes(term) || email.includes(term)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });

        // NEW: Update select all state after filtering
        this.updateSelectAllState();
    }

    addSelectedMentions() {
        const selectedUsers = [];
        $('.user-item.selected').each(function () {
            const email = $(this).data('email');
            const name = $(this).data('name');
            selectedUsers.push({ email, name });
        });

        console.log('Selected users for mention:', selectedUsers); // Debug log

        // Directly assign selected users (no duplicate check needed)
        this.mentionedUsers = selectedUsers;

        console.log('All mentioned users:', this.mentionedUsers); // Debug log

        this.displayMentionedUsers();
        this.updateInputState();
        this.closeMentionsModal();
    }

    displayMentionedUsers() {
        let container = $('.mentioned-users');

        // Create the container if it doesn't exist
        if (container.length === 0) {
            container = $('<div class="mentioned-users"></div>');
            $('.editor-container').after(container);
        }

        container.empty();

        if (this.mentionedUsers.length === 0) {
            container.removeClass('has-mentions').hide();
            return;
        }

        container.addClass('has-mentions').show();

        // Add heading with Remove All button
        const headerDiv = $(`
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <div style="font-weight: 500; color: #2d3748;">Mentioning:</div>
            <button type="button" class="remove-all-mentions" style="background: transparent; border: none; color: #e53e3e; cursor: pointer; font-weight: bold; font-size: 12px;">Remove All</button>
        </div>
    `);

        // Bind Remove All button event
        headerDiv.find('.remove-all-mentions').on('click', (e) => {
            e.preventDefault();
            this.mentionedUsers = [];
            this.displayMentionedUsers();
            this.updateInputState();
        });

        container.append(headerDiv);

        // Add individual mention tags
        this.mentionedUsers.forEach((user, index) => {
            const tag = $(`
            <span class="mention-tag">
                @${user.name}
                <button type="button" class="remove-mention" data-index="${index}">×</button>
            </span>
        `);

            tag.find('.remove-mention').on('click', (e) => {
                e.preventDefault();
                this.removeMention(index);
            });

            container.append(tag);
        });

        console.log('Displayed mentioned users:', this.mentionedUsers);
    }

    removeMention(index) {
        this.mentionedUsers.splice(index, 1);
        this.displayMentionedUsers();
        this.updateInputState();
    }

    handleFileSelection(files) {
        Array.from(files).forEach(file => {
            if (file.size > 1024 * 10) {
                this.showAlert('File size exceeds the limit of 10KB. Please select a smaller file.', 'error');
                return false;
            }
            file.tempName = `${Date.now()}_${Math.floor(Math.random() * 10000)}_${file.name}`;
            this.selectedFiles.push(file);
        });

        if (this.selectedFiles.length > 5) {
            this.showAlert('You can only attach a maximum of 5 files.', 'error');
            return false;
        }

        this.displayAttachments();
        this.updateInputState();
        $('#admin-file-input').val('');
    }

    displayAttachments() {
        const container = $('#attachments-preview');
        container.empty();

        if (this.selectedFiles.length === 0) {
            container.removeClass('has-files');
            return;
        }

        container.addClass('has-files');

        this.selectedFiles.forEach((file, index) => {
            const item = $(`
                <div class="attachment-item fade-in">
                    <div class="attachment-icon">
                        <i class="fas ${this.getFileIcon(file.type)}"></i>
                    </div>
                    <div class="attachment-info">
                        <div class="attachment-name">${file.name}</div>
                        <div class="attachment-size">${this.formatFileSize(file.size)}</div>
                    </div>
                    <button type="button" class="remove-attachment" data-index="${index}">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
            `);

            container.append(item);
        });

        // Bind remove events
        $('.remove-attachment').off('click').on('click', (e) => {
            const index = parseInt($(e.target).closest('.remove-attachment').data('index'));
            this.removeFile(index);
        });
    }

    removeFile(index) {
        this.selectedFiles.splice(index, 1);
        this.displayAttachments();
        this.updateInputState();
    }

    getFileIcon(type) {
        if (type.startsWith('image/')) return 'fa-image';
        if (type.includes('pdf')) return 'fa-file-pdf';
        if (type.includes('word')) return 'fa-file-word';
        if (type.includes('zip')) return 'fa-file-archive';
        return 'fa-file';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    updateInputState() {
        const editor = tinymce.get('admin-message-editor');
        const messageText = editor ? editor.getContent().trim() : '';
        const hasFiles = this.selectedFiles.length > 0;
        const hasMentions = this.mentionedUsers.length > 0;

        // UPDATED LOGIC: Require message text OR files (mentions alone are not enough)
        const hasContent = messageText.length > 0 || hasFiles;

        // Update send button - disabled if only mentions without content
        $('#send-message-btn').prop('disabled', !hasContent || this.isSubmitting);

        // Update status text
        const statusContainer = $('.input-status');
        const statusText = $('#input-status-text');

        if (this.isSubmitting) {
            statusText.text('Sending message...');
        } else if (hasContent) {
            statusContainer.addClass('ready');
            let statusParts = [];

            if (messageText) statusParts.push('message');
            if (hasFiles) statusParts.push(`${this.selectedFiles.length} file${this.selectedFiles.length > 1 ? 's' : ''}`);
            if (hasMentions) statusParts.push(`${this.mentionedUsers.length} mention${this.mentionedUsers.length > 1 ? 's' : ''}`);

            statusText.text(`${statusParts.join(' + ')} ready to send`.charAt(0).toUpperCase() + `${statusParts.join(' + ')} ready to send`.slice(1));
        } else if (hasMentions && !hasContent) {
            // NEW: Special case for mentions without content
            statusContainer.removeClass('ready');
            statusText.text('Add a message or file to send mentions');
        } else {
            statusContainer.removeClass('ready');
            statusText.text('Compose your message above');
        }
    }


    async submitMessage() {
        // Guard against double submission
        if (this.isSubmitting) {
            console.log('Already submitting, aborting...');
            return;
        }

        const editor = tinymce.get('admin-message-editor');
        const messageText = editor.getContent();

        if (!messageText.trim() && this.selectedFiles.length === 0 && this.mentionedUsers.length === 0) {
            this.showAlert('Please enter a message, attach files, or mention users.', 'error');
            return;
        }

        // Set submission guard
        this.isSubmitting = true;
        this.setLoadingState(true);

        const formData = new FormData();
        formData.append('message_text', messageText);
        formData.append('group_id', $('#admin-message-form').data('group-id'));

        // NEW: Add mentioned users
        if (this.mentionedUsers.length > 0) {
            console.log('Adding mentioned users to form data:', this.mentionedUsers); // Debug log
            formData.append('mentioned_users', JSON.stringify(this.mentionedUsers));
        }

        // Add CSRF token
        formData.append($('meta[name=csrf-param]').attr('content'), $('meta[name=csrf-token]').attr('content'));

        this.selectedFiles.forEach((file, index) => {
            formData.append(`files[${index}]`, file);
            formData.append(`tempNames[${index}]`, file.tempName);
            formData.append(`types[${index}]`, file.type);
        });

        try {
            console.log('Submitting message...'); // Debug log

            const response = await $.ajax({
                url: $('#admin-message-form').attr('action'),
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 30000
            });

            console.log('Message submitted successfully'); // Debug log
            console.log('Server response:', response); // Debug log

            this.clearForm();

            // Show success message with mention info
            let successMsg = 'Admin message sent successfully!';
            if (this.mentionedUsers.length > 0) {
                successMsg += ` Email notifications sent to ${this.mentionedUsers.length} mentioned user${this.mentionedUsers.length > 1 ? 's' : ''}.`;
            }

            this.showAlert(successMsg, 'success');

            setTimeout(() => window.location.reload(), 2000);

        } catch (error) {
            console.error('Submit error:', error); // Debug log
            console.error('Error details:', error.responseText); // Debug log
            this.showAlert('Failed to send message. Please try again. Error: ' + (error.responseText || error.statusText), 'error');
        } finally {
            // Always reset the submission guard
            this.isSubmitting = false;
            this.setLoadingState(false);
        }
    }

    setLoadingState(loading) {
        const button = $('#send-message-btn');
        if (loading) {
            button.addClass('loading').prop('disabled', true);
        } else {
            button.removeClass('loading');
            this.updateInputState();
        }
    }

    clearForm() {
        tinymce.get('admin-message-editor').setContent('');
        this.selectedFiles = [];
        this.mentionedUsers = []; // Clear mentions
        this.displayAttachments();
        this.displayMentionedUsers();
        this.updateInputState();
    }

    showAlert(message, type) {
        $('.message-alert').remove();

        const alert = $(`
            <div class="message-alert ${type}">
                ${message}
            </div>
        `);

        $('.admin-chat-container').prepend(alert);

        setTimeout(() => {
            alert.fadeOut(() => alert.remove());
        }, 10000); // Increased to 10 seconds to see errors better
    }
}

// Global update function
function updateInputState() {
    if (window.modernAdminFileManager) {
        window.modernAdminFileManager.updateInputState();
    }
}

// Initialize when ready - PREVENT MULTIPLE INSTANCES
$(document).ready(() => {
    if ($('#admin-message-editor').length && !window.modernAdminFileManager) {
        console.log('Initializing ModernAdminFileManager...');
        window.modernAdminFileManager = new ModernAdminFileManager();
    } else if (window.modernAdminFileManager) {
        console.log('ModernAdminFileManager already exists, skipping initialization.');
    }
});
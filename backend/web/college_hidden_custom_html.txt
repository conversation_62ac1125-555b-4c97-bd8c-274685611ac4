<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>My Static Page</title>
      <!-- Link to Tailwind CSS for utility-first styling -->
      <script src="https://cdn.tailwindcss.com"></script>
      <style>
         /* Apply the 'Inter' font globally for a modern, clean look */
         body {
         font-family: 'Inter', sans-serif;
         background-color: #f8fafc; /* Light gray background for the page */
         }
      </style>
   </head>
   <body class="flex flex-col min-h-screen">
      <!-- Header Section -->
      <header class="bg-blue-600 text-white p-4 shadow-md">
         <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold">Getmyuni</h1>
            <nav>
               <ul class="flex space-x-4">
                  <li><a href="#" class="hover:text-blue-200 transition-colors duration-200">Home</a></li>
                  <li><a href="#" class="hover:text-blue-200 transition-colors duration-200">About</a></li>
                  <li><a href="#" class="hover:text-blue-200 transition-colors duration-200">Services</a></li>
                  <li><a href="#" class="hover:text-blue-200 transition-colors duration-200">Contact</a></li>
               </ul>
            </nav>
         </div>
      </header>
      <!-- Main Content Area -->
      <main class="container mx-auto mt-8 p-4 flex-grow">
         <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
            <h2 class="text-4xl font-extrabold text-gray-900 mb-6 text-center">
               Welcome to Our Page!
            </h2>
            <p class="text-lg text-gray-700 leading-relaxed mb-4">
               IIT Madras is a premium technical university which was established on 1959 in Chennai, Tamil Nadu. It ranked #1 among Engineering Universities in India, under NIRF Ranking 2024. 
               IITM offers undergraduate, postgraduate and doctorate degrees in Engineering, Management and Science. The MBA course is offered through the DoMS IIT Madras. Admissions are based on JEE Advanced, GATE, IIT JAM, CAT scores for various courses.  The Indian Institute of Technology Madras features a big and diverse faculty of 630 members across all academic departments, with PhD degrees in their respective professions.
               IIT Madras placements saw admirable placement percentages and the average package offered to students was INR 22 LPA during the 2024 placement procedure. 
            </p>
            <p class="text-lg text-gray-700 leading-relaxed mb-8">
               You can easily modify this content, add more sections, images, or interactive elements
               as needed. The structure provides a solid foundation for any web project.
            </p>
            <!-- New Form Section -->
            <div class="mt-8 p-6 bg-gray-50 rounded-lg shadow-inner">
               <h3 class="text-2xl font-bold text-gray-800 mb-4 text-center">Get in Touch!</h3>
               <form id="leadFormSwipePage" action="https://api.getmyuni.com/v1/student-lead-from-capture/submit-swipe-page-leads" method="POST" class="space-y-4">
                  <div>
                     <label for="full-name" class="block text-sm font-medium text-gray-700">Full Name</label>
                     <input
                        type="text"
                        id="name"
                        name="name"
                        placeholder="Your Full Name"
                        required
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                  </div>
                  <div>
                     <label for="phone-number" class="block text-sm font-medium text-gray-700">Phone Number</label>
                     <input
                        type="tel"
                        id="phone"
                        name="mobile"
                        placeholder="e.g., +1234567890"
                        required
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                  </div>
                  <div>
                     <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                     <input
                        type="email"
                        id="email"
                        name="email"
                        placeholder="<EMAIL>"
                        required
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                  </div>
                  <div>
                     <label for="course" class="block text-sm font-medium text-gray-700">Course</label>
                     <select class="select2HookClass user_course field-style required-field select-arrow" name="course" id="user_course">
                        <option value="">Select a Course</option>
                        <option value="1">B.Tech</option>
                        <option value="2">MBA</option>
                        <option value="3">BBA</option>
                     </select>
                  </div>
                  <!-- Hidden UTM fields -->
                  <input type="hidden" id="state" name="state" value="1">
                  <input type="hidden" id="utm_source" name="utm_source" value="gmu">
                  <input type="hidden" id="utm_medium" name="utm_medium" value="GMU">
                  <input type="hidden" name="utm_campaign" id="utm_campaign">
                  <input type="hidden" name="utm_id" id="utm_id">
                  <input type="hidden" name="utm_term" id="utm_term">
                  <input type="hidden" id="college" name="college" value=1>
                  <input type="hidden" id="page_url" name="page_url" value="lpu">
                  <button
                     type="submit"
                     class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                     >
                  Submit
                  </button>
               </form>
            </div>
            <div class="mt-8 text-center">
               <img
                  src="https://placehold.co/800x400/e0e7ff/003366?text=Placeholder+Image"
                  alt="Placeholder image"
                  class="w-full max-w-xl mx-auto rounded-md shadow-md"
                  >
            </div>
         </div>
      </main>
      <!-- Footer Section -->
      <footer class="bg-gray-800 text-white p-4 mt-8 shadow-inner">
         <div class="container mx-auto text-center text-sm">
            &copy; 2025 My Static Page. All rights reserved.
         </div>
      </footer>
      <script>
         // JavaScript to populate hidden UTM fields from URL parameters
            function getFinalUtmValue(field) {
    const urlParams = new URLSearchParams(window.location.search);
    let val = urlParams.get(field);
    return val ? val.replace(/[{}]/g, '').trim() : '';
}

function attachUtmToForm(form) {
    const utmFields = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_id', 'utm_term'];
    utmFields.forEach(field => {
        let input = form.querySelector(`input[name="${field}"]`);
        if (!input) {
            input = document.createElement('input');
            input.type = 'hidden';
            input.name = field;
            form.appendChild(input);
        }
        input.value = getFinalUtmValue(field);
    });
}

function applyUtmToAllForms() {
    document.querySelectorAll('form').forEach(form => {
        attachUtmToForm(form);
    });
}

document.addEventListener("DOMContentLoaded", () => {
    applyUtmToAllForms();

    // For future forms loaded via modal or AJAX
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            mutation.addedNodes.forEach(node => {
                if (node.nodeType === 1) {
                    // If it's a form or contains a form
                    if (node.matches('form')) {
                        attachUtmToForm(node);
                    } else {
                        node.querySelectorAll('form').forEach(form => {
                            attachUtmToForm(form);
                        });
                    }
                }
            });
        });
    });

    observer.observe(document.body, { childList: true, subtree: true });
});
         
         
         document.addEventListener('DOMContentLoaded', function () {
         const form = document.getElementById('leadFormSwipePage');
         
         form.addEventListener('submit', function (event) {
         event.preventDefault();
         
         const formData = new FormData(form);
         
         fetch(form.action, {
             method: 'POST',
             body: formData
         })
         .then(response => response.json())
         .then(result => {
             // Optional: show a success message
             // Redirect after success please which domain you want to redirect 
             window.location.href = 'https://www.getmyuni.com/';
         })
         .catch(error => {
             console.error('Form submission error:', error);
             alert("Something went wrong. Please try again.");
         });
         });
         });
         
         document.querySelector("#name").addEventListener('input', (e) => { e.target.value = e.target.value.replace(/[^A-Za-z ]/g, '') });
         
         document.querySelector("#email").addEventListener('input', (e) => {
                     e.target.value = e.target.value.replace(/^[^a-zA-Z]|[^a-zA-Z0-9@\-\.]|[^\w\d\-@\.]$/g, '');
                     if ((e.target.value.match(/\./g) || []).length > 1) {
                         e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                     }
                     if ((e.target.value.match(/@/g) || []).length > 1) {
                         e.target.value = e.target.value.substring(0, e.target.value.length - 1);
                     }
                 })
         
         document.querySelector("#phone").addEventListener('input', (e) => { 
         e.target.value = e.target.value.replace(/^[^6-9]|\D/g, '') 
         if(e.target.value.length > 10) {
         e.target.value = e.target.value.substr(0, 10);
         }
         });
         
         document.addEventListener("DOMContentLoaded", function () {
         // Load jQuery
         var jQueryScript = document.createElement('script');
         jQueryScript.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
         
         jQueryScript.onload = function () {
         // Load Select2 CSS
         var select2Css = document.createElement('link');
         select2Css.rel = 'stylesheet';
         select2Css.href = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css';
         document.head.appendChild(select2Css);
         
         // Load custom swipe page CSS
         var customCss = document.createElement('link');
         customCss.rel = 'stylesheet';
         customCss.href = '/yas/css/version2/swipe_pages.css';
         document.head.appendChild(customCss);
         
         // Load Select2 JS
         var select2Script = document.createElement('script');
         select2Script.src = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js';
         
         select2Script.onload = function () {
             // Initialize all dropdowns
             $('#user_course, #exam, #specialization, #city , #college, #state').select2({
                 placeholder: 'Select an option',
                 allowClear: true,
                 width: '100%'
             });
         };
         
         document.head.appendChild(select2Script);
         };
         
         document.head.appendChild(jQueryScript);
         });
      </script>
   </body>
</html>